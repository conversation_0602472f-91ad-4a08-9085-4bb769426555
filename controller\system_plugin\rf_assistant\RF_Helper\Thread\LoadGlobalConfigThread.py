from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import os, requests
import configparser
import ast
from pathlib import Path


GLOBAL_CONFIG_URL = "http://10.2.71.213:31381/RFCode/Config/GlobalConfig.ini"

class LoadGlobalConfigThread(QThread):
    config_loaded = pyqtSignal(dict)


    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        name = GLOBAL_CONFIG_URL.split('/')[-1]
        self.file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), name)
        self.CofingDataDic = {}
    
    def load_global_data(self):
        self.load_global_data_from_server()
        self.read_global_data()

    def load_global_data_from_server(self):
        try:
            resp = requests.get(GLOBAL_CONFIG_URL, timeout=10)
            print("Downloading config file to:", self.file_path)
            if resp.status_code == 200:
                with open(self.file_path, "wb") as file:
                    file.write(resp.content)
                print("Config file downloaded successfully")
                return True
            else:
                print(f"Failed to download config file. Status code: {resp.status_code}")
                return False
        except Exception as e:
            print(f"Error downloading config file: {str(e)}")
            return False

    def read_global_data(self):
        config = ConfigReader(self.file_path)
        config.load_config()
        self.CofingDataDic = {}
        self.CofingDataDic['database'] = {}
        self.CofingDataDic['database']['host'] = config.get_normal_value('database', 'host')
        self.CofingDataDic['database']['port'] = config.get_normal_value('database', 'port')
        self.CofingDataDic['database']['dbname'] = config.get_normal_value('database', 'dbname')
        self.CofingDataDic['database']['username'] = config.get_normal_value('database', 'username')
        self.CofingDataDic['database']['password'] = config.get_normal_value('database', 'password')
        self.CofingDataDic['config'] = {}
        self.CofingDataDic['config']['projectDic'] = config.get_dict_value('config', 'projectDic')
        self.CofingDataDic['config']['basicKeywordProjectMap'] = config.get_dict_value('config', 'basicKeywordProjectMap')
        self.CofingDataDic['config']['WORK_SPACE_INFO'] = config.get_dict_value('config', 'WORK_SPACE_INFO')
        self.CofingDataDic['config']['testPathSplitMap'] = config.get_dict_value('config', 'testPathSplitMap')
        self.CofingDataDic['config']['llmItems'] = config.get_dict_value('config', 'llmItems')
        self.CofingDataDic['config']['llmNameDic'] = config.get_dict_value('config', 'llmNameDic')
        self.CofingDataDic['config']['TEST_CASE_PATH_INFO'] = config.get_dict_value('config', 'TEST_CASE_PATH_INFO')

    def run(self):
        try:
            self.load_global_data()
        except Exception as e:
            print(f"Error loading global config: {str(e)}")
        self.config_loaded.emit(self.CofingDataDic)


class ConfigReader:
    def __init__(self, config_path):
        self.config = configparser.ConfigParser()
        self.config_path = Path(config_path)
        
    def load_config(self):
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config file not found: {self.config_path}")
        self.config.read(self.config_path, encoding='utf-8')
        
    def get_normal_value(self, section, option, default=None):
        if not self.config.has_option(section, option):
            return default
        return self.config.get(section, option)

    def get_dict_value(self, section, option, default={}):
        if self.config.has_option(section, option):
            return ast.literal_eval(self.config.get(section, option))
        else:
            return default
        

if __name__ == "__main__":
    thread = LoadGlobalConfigThread()
    thread.start()
    
    try:
        while thread.isRunning():
            QThread.msleep(100)
    except KeyboardInterrupt:
        print("Shutting down...")
        thread.quit()
        thread.wait()
