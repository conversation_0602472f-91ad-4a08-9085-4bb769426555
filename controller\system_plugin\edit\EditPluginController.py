# encoding=utf-8
'''
Created on 2019年11月12日

@author: 10247557
'''

from _functools import partial
import time
import traceback

from PyQt5.Qt import Qt
from PyQt5.QtWidgets import QWidget

from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.table.Table import _table
from model.CurrentItem import CurrentItem
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from utility.UIRepository import UIRepository
from view.explorer.tree_item.PyItem import PyItem
from settings.SystemConfig import RF_ASSISTANT_SLOT

EDIT = LanguageLoader().get('EDIT')
TEXT_EDIT = LanguageLoader().get('TEXT_EDIT')
RUN = LanguageLoader().get('RUN')
BASH = LanguageLoader().get('BASH')
RF_ASSISTANT = LanguageLoader().get('RF_ASSISTANT')


class TextEditScrollManager:
    """TEXT_EDIT页面滚动位置管理器"""

    def __init__(self):
        self.scroll_positions = {}  # 存储每个文件的滚动位置 {file_path: (scroll_pos, cursor_pos)}

    def save_scroll_position(self, file_path):
        """保存指定文件的滚动位置"""
        try:
            text_editor = PluginRepository().find('TEXT_EDIT')
            if text_editor and hasattr(text_editor, 'SendScintilla'):
                scroll_pos = text_editor.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE
                cursor_pos = text_editor.getCursorPosition()
                self.scroll_positions[file_path] = (scroll_pos, cursor_pos)
                return True
        except Exception as e:
            print(f"保存滚动位置失败: {e}")
        return False

    def restore_scroll_position(self, file_path):
        """恢复指定文件的滚动位置"""
        try:
            if file_path in self.scroll_positions:
                text_editor = PluginRepository().find('TEXT_EDIT')
                if text_editor and hasattr(text_editor, 'SendScintilla'):
                    scroll_pos, cursor_pos = self.scroll_positions[file_path]

                    # 使用QTimer延迟恢复，确保文件内容已完全加载
                    from PyQt5.QtCore import QTimer
                    def restore_position():
                        try:
                            text_editor.SendScintilla(2613, scroll_pos)  # SCI_SETFIRSTVISIBLELINE
                            text_editor.setCursorPosition(cursor_pos[0], cursor_pos[1])
                            print(f"恢复文件 {file_path} 的滚动位置: {scroll_pos}, 光标位置: {cursor_pos}")

                            # 立即触发当前可见区域的语法高亮
                            _trigger_visible_area_highlighting(text_editor)

                        except Exception as e:
                            print(f"恢复滚动位置失败: {e}")

                    QTimer.singleShot(100, restore_position)
                    return True
        except Exception as e:
            print(f"恢复滚动位置失败: {e}")
        return False


# 全局滚动位置管理器实例
_scroll_manager = TextEditScrollManager()


def _trigger_visible_area_highlighting(text_editor):
    """触发当前可见区域的语法高亮"""
    try:
        if not text_editor or not hasattr(text_editor, 'SendScintilla'):
            return

        # 获取当前可见区域
        first_visible = text_editor.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE
        lines_on_screen = text_editor.SendScintilla(2370)  # SCI_LINESONSCREEN
        total_lines = text_editor.lines()

        # 计算需要高亮的范围（包含缓冲区）
        buffer_lines = 10
        start_line = max(0, first_visible - buffer_lines)
        end_line = min(total_lines, first_visible + lines_on_screen + buffer_lines)

        # 获取lexer并触发语法高亮
        lexer = text_editor.lexer
        if lexer and hasattr(lexer, 'styleText'):
            # 计算字节位置
            start_pos = text_editor.SendScintilla(2167, start_line)  # SCI_POSITIONFROMLINE
            if end_line >= total_lines:
                end_pos = text_editor.SendScintilla(2006)  # SCI_GETLENGTH
            else:
                end_pos = text_editor.SendScintilla(2167, end_line)  # SCI_POSITIONFROMLINE

            # 强制重新高亮可见区域
            lexer.styleText(start_pos, end_pos)

            # 强制刷新显示
            text_editor.update()

    except Exception as e:
        print(f"触发可见区域语法高亮失败: {e}")


class EditPluginController(object):

    def __init__(self, tab_obj, parent):
        self._tab_obj = tab_obj
        self._parent = parent
        self._edit_tab = None
        self._last_click_tab = EDIT
        self._tab_obj.tabBarClicked['int'].connect(partial(self._tab_click, self))
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.show_item.connect(self._show_editor, Qt.QueuedConnection)
        self._signal_distributor.show_item.connect(self._set_edit_widget)
        self._signal_distributor.show_only_in_edit.connect(self._show_editor, Qt.QueuedConnection)
        self._signal_distributor.show_only_in_edit.connect(self._set_edit_widget)

    @staticmethod
    def _tab_click(this, index):
        if PluginRepository().find('LAST_CLICK_TAB'):
            this._last_click_tab = PluginRepository().find('LAST_CLICK_TAB')
        if this._tab_obj.tabText(index) == EDIT and this._last_click_tab != EDIT:  # 从其他页面切换到edit页面
            this._show_editor(CurrentItem().get_current_item(), index, 0)
        if this._last_click_tab == TEXT_EDIT and this._tab_obj.tabText(index) != TEXT_EDIT:  # 从textedit切换到其他页面
            if this._update_data_file(CurrentItem()):
                if isinstance(CurrentItem().get_current_item(), PyItem):
                    this._signal_distributor.update(CurrentItem().get_current_item(), CurrentItem().get_current_item())
                else:
                    this._signal_distributor.update(CurrentItem().get_current_item(), CurrentItem().get_current_item().parent())
            try:
                # 保存当前TEXT_EDIT的滚动位置
                current_item = CurrentItem().get()
                if current_item and current_item.get("path"):
                    _scroll_manager.save_scroll_position(current_item.get("path"))
            except Exception as e:
                print(f"保存TEXT_EDIT滚动位置时出错: {e}")

        if this._last_click_tab == EDIT and this._tab_obj.tabText(index) != EDIT:  # 从EDIT页面切换到text edit页面
            try:
                path = CurrentItem().get().get("path")

                # 加载文件（load_file方法内部已经包含滚动位置恢复逻辑，无需重复调用）
                PluginRepository().find('TEXT_EDIT').load_file(path)

            except Exception:
                traceback.print_exc()
        this._last_click_tab = this._tab_obj.tabText(index)

    @staticmethod
    def _show_editor(view_tree_item, is_add, current_index=None):
        edit_plugin_controller = UIRepository().find('edit_plugin_controller')
        if not view_tree_item:
            edit_plugin_controller.set_widget('BlankEdit', None)
        else:
            print(CurrentItem().get())
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.query(CurrentItem().get_current_item())
            edit_plugin_controller.set_widget(CurrentItem().get()['type'] + 'Edit', parsed_item, current_index)

    def _set_edit_widget(self, item, is_add):
        current_index = self._tab_obj.currentIndex()
        if is_add:
            if current_index == SystemSettings().get_value('TEXT_EDIT_SLOT'):
                self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))
        if current_index == SystemSettings().get_value('RUN_SLOT') or current_index == SystemSettings().get_value('RF_ASSISTANT_SLOT'):
            self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))

    @staticmethod
    def _update_data_file(text_edit_item):
        data_file = None
        view_tree_item = text_edit_item.get_current_item()
        if view_tree_item:
            data_file = ItemParserFacory().create(view_tree_item.__class__.__name__ + 'Parser').get_cur_data_file(view_tree_item)
        if not data_file:
            return False
#         current_item_path = text_edit_item.get().get("path")
        screen_content = PluginRepository().find('TEXT_EDIT').get()
        if screen_content != data_file.get_content():
            data_file.update(screen_content)
            return True
        return False

    def set_edit_tab(self, edit_tab):
        self._edit_tab = edit_tab

    def get_edit_tab(self):
        return self._edit_tab

    def set_widget(self, edit_type, parsed_item, current_index=None):
        if current_index is None:
            current_index = self._tab_obj.currentIndex()
        if self._edit_tab:
            self._tab_obj.removeTab(self._tab_obj.indexOf(self._edit_tab))
        self._edit_tab = QWidget()
        self._editor = self._get_editor(edit_type)
        self._edit_tab.setLayout(self._editor.load())
        self._tab_obj.insertTab(SystemSettings().get_value('EDIT_SLOT'), self._edit_tab, EDIT)
        if current_index == SystemSettings().get_value('EDIT_SLOT'):
            self._tab_obj.setCurrentIndex(SystemSettings().get_value('EDIT_SLOT'))
            if parsed_item:
                print('parsed_item:', parsed_item)
                print('parsed_item.table:', parsed_item.table)
                self.fill_data(parsed_item)
        return self._editor

    def _get_editor(self, edit_type):
        if edit_type == 'VariableItemEdit':
            if CurrentItem().get_parent():
                parent_type = CurrentItem().get_parent().split('Item')[0]
                editor = UIRepository().find(parent_type + edit_type)
            else:
                editor = UIRepository().find('BlankEdit')
        else:
            editor = UIRepository().find(edit_type)
        return editor

    def fill_data(self, parsed_item):
        self._editor.fill_data(parsed_item)
