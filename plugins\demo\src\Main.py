# coding=utf8
'''
Created on 2019年10月17日

@author: 10140129
'''
import time

from plugins.Plugin import AbstractPlugin
from plugins.SystemLibrary import SystemLibrary


class a():
    def activate(self):
        pass


class Main(AbstractPlugin):

    def activate(self):
        SystemLibrary.test()
        time.sleep(5)
        SystemLibrary.log('aaaa', 'demo')

    def deactivate(self):
        print("demo plugin deactivate successfully")
