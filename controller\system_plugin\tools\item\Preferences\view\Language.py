# coding=utf-8
'''
Created on 2020年1月6日

@author: 10247557
'''
from PyQt5.Qt import Qt
from PyQt5.QtWidgets import QHBoxLayout, QRadioButton

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.Singleton import Singleton
from view.common.MessageBox import MessageBox


@Singleton
class Language():

    def __init__(self):
        self._fist_load = False

    def load(self):
        self._set_layout()

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._english = QRadioButton('English')
        self._chinese = QRadioButton('Chinese')
        self._english.toggled.connect(self._set_language)
        self._layout.addWidget(self._english, 0, Qt.AlignTop)
        self._layout.addWidget(self._chinese, 0, Qt.AlignTop)
        self._set_checked()

    def get_layout(self):
        return self._layout

    def _set_checked(self):
        self._fist_load = True
        try:
            language = SystemSettings().read('LANGUAGE')
        except:
            language = 'EN'
        if language == 'EN':
            self._english.setChecked(True)
        else:
            self._chinese.setChecked(True)
        self._fist_load = False

    def _set_language(self):
        if self._english.isChecked():
            SystemSettings().write('LANGUAGE', 'EN')
        else:
            SystemSettings().write('LANGUAGE', 'ZH')
        if not self._fist_load:
            MessageBox().show_information(LanguageLoader().get('RESTART_INFO'))
