import requests 
import httpx, time
from openai import OpenAI

class SiliconFlowAPI:
    def __init__(self, base_url, api_key, model):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.proxies = {
        "http://": "http://proxyhk.zte.com.cn:80",
        "https://": "http://proxyhk.zte.com.cn:80",
        }

    def call_endpoint(self, endpoint, params=None):
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }
        response = requests.get(f'{self.base_url}/{endpoint}', headers=headers, params=params)
        return response.json()

    def chat(self, content, callback=None):
        http_client = httpx.Client(proxies=self.proxies)
        client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            http_client=http_client
        )
        response = client.chat.completions.create(
            model=self.model,
            messages=[
                {'role': 'user', 
                'content': content}
            ],
            stream=True
        )
        res = ''
        docId = ''
        for chunk in response:
            if callback:
                callback(chunk.choices[0].delta.content)
                if chunk.choices[0].delta.content:
                    print(chunk.choices[0].delta.content)
                    res += chunk.choices[0].delta.content
            else:
                time.sleep(0.2)
                if chunk.choices[0].delta.content:
                    print(chunk.choices[0].delta.content)
                    res += chunk.choices[0].delta.content
        return {'bo': {'docId': docId, 'result': res}}

if __name__ == "__main__":
    # base_url = "https://api.siliconflow.cn/v1"
    # api_key = "sk-ihnegbftkynylxenlbbrxksdladnraavqdycbzfkzyddroyr"
    # model = "deepseek-ai/DeepSeek-V3"
    # api = SiliconFlowAPI(base_url, api_key, model)
    # response = api.chat('甚至有什么好玩的，说3地点')
    # print(response)

    base_url = "https://api.deepseek.com"
    api_key = "***********************************"
    model = "deepseek-reasoner"
    api = SiliconFlowAPI(base_url, api_key, model)
    response = api.chat('深圳有什么好玩的，说3地点')
    print(response)
