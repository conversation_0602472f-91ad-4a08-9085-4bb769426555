# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from prettytable import PrettyTable
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3

TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'


class parserKeywordsThread(QThread):
    parser_keywords = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.db = MongoDb3()



    def run(self):
        self.parent.basicKeywordList = []
        self.parent.testKeywordList = []
        self.parent.basicKeywordDic = {}
        self.parent.testKeywordDic = {}

        basicKeywordInfo = self.db.query({'project': self.parent.basicKeywordProjectMap.get(self.parent.project, 'Qcell')}, BASIC_KEAYWORDS_DB_TABLE)
        basicKeyword = sorted(basicKeywordInfo, key=lambda x: x['num'].get(self.parent.project, float('inf')), reverse=True)
        allKeywordsNum = len(basicKeyword)
        usedKeywordsNum = 0        
        pt = PrettyTable()
        pt.field_names = ["基础关键字", "引用次数", "所在文件"]
        for keyword in basicKeyword:
            if not keyword.get('num'):
                continue
            if not keyword['num'].get(self.parent.project):
                continue
            if keyword['num'].get(self.parent.project, 0) == 0:
                break
            if keyword['name'] == '...':
                continue
            usedKeywordsNum = usedKeywordsNum + 1
            pt.add_row([keyword['name'], keyword['num'][self.parent.project], keyword['path']])
            self.parent.basicKeywordList.append(keyword['name'])
        text = '关键字总数：{0}\n项目已使用关键字数：{1}\n'.format(allKeywordsNum, usedKeywordsNum)
        text = text + str(pt)
        
        suiteKeywordInfo = self.db.query({'project': self.parent.project}, SUITE_KEYWORDS_DB_TABLE)
        testKeyword = sorted(suiteKeywordInfo, key=lambda x: x['num'].get(self.parent.project, float('inf')), reverse=True)
        allKeywordsNum = len(testKeyword)
        usedKeywordsNum = 0        
        pt = PrettyTable()
        pt.field_names = ["测试套关键字", "引用次数", "所在文件"]
        for keyword in testKeyword:
            if keyword['num'][self.parent.project] == 0:
                break
            if keyword['name'] == '...':
                continue
            usedKeywordsNum = usedKeywordsNum + 1
            pt.add_row([keyword['name'], keyword['num'][self.parent.project], keyword['path']])
            self.parent.testKeywordList.append(keyword['name'])
        text = text + str(pt)
        self.parent.basicKeywordDic = {item['name']: item for item in basicKeyword}
        self.parent.testKeywordDic = {item['name']: item for item in testKeyword}
        self.parent._update_keywords_info()
        print(text)
        self.parser_keywords.emit(text)


#     def run1(self):
#         self.parent.basicKeywordList = []
#         self.parent.testKeywordList = []
#         self.parent.basicKeywordDic = {}
#         self.parent.testKeywordDic = {}
#         ukr = UserKeyword(self.parent.keywordsDir)
#         ukr.generate_all_keyword(ukr._keywordDir)
#         ukr.replace_keywords(self.parent.projectDir)
#         basicKeyword = sorted(ukr.keywordCountDict.items(), key=lambda item: item[1][0], reverse=True)
#         allKeywordsNum = len(basicKeyword)
#         usedKeywordsNum = 0        
#         pt = PrettyTable()
#         pt.field_names = ["基础关键字", "引用次数", "所在文件"]
#         print(pt)
#         for k, v in basicKeyword:
#             if v[0] == 0:
#                 break
#             if k == '...':
#                 continue
#             usedKeywordsNum = usedKeywordsNum + 1
#             pt.add_row([k, v[0], v[1]])
#             self.parent.basicKeywordList.append(k)
#         text = '关键字总数：{0}\n项目已使用关键字数：{1}\n'.format(allKeywordsNum, usedKeywordsNum)
#         text = text + str(pt)
#          
#         ukr = UserKeyword(self.parent.projectDir)
#         ukr.generate_all_keyword(ukr._keywordDir)
#         ukr.replace_keywords(self.parent.projectDir)
#         testKeyword = sorted(ukr.keywordCountDict.items(), key=lambda item: item[1][0], reverse=True)
#         allKeywordsNum = len(testKeyword)
#         usedKeywordsNum = 0        
#         pt = PrettyTable()
#         pt.field_names = ["测试套关键字", "引用次数", "所在文件"]
#         for k, v in testKeyword:
#             if v[0] == 0:
#                 break
#             if k == '...':
#                 continue
#             usedKeywordsNum = usedKeywordsNum + 1
#             pt.add_row([k, v[0], v[1]])
#             self.parent.testKeywordList.append(k)
#         text = text + str(pt)
#         self.parent.basicKeywordDic = dict(basicKeyword)
#         self.parent.testKeywordDic = dict(testKeyword)
#         self.parent._update_keywords_info()
#         self.parser_keywords.emit(text)