# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''

from _functools import partial
import webbrowser

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction

from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from view.common.dialog.IntroductionDialog import IntroductionDialog


class IntroductionPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        file_menu = self._parent_window.addMenu('&' + LanguageLoader().get('INTRODUCTION'))
        self._add_item_open_directory(file_menu)
        self._add_item_software_instruction(file_menu)

    def _add_item_open_directory(self, file_menu):
        about_action = QAction(QIcon(''), LanguageLoader().get('ABOUT_RFCODE'), self._parent_window)
        about_action.triggered.connect(partial(self.open_dialog, self))
        file_menu.addAction(about_action)

    def _add_item_software_instruction(self, file_menu):
        about_action = QAction(QIcon(''), LanguageLoader().get('PRODUCTION_INSTRUCTION'), self._parent_window)
        about_action.triggered.connect(self.open_instruction)
        file_menu.addAction(about_action)

    def get_icon(self):
        return Loader().get_relative_path('FILE')

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)

    def show_dialog(self):
        self._dialog.show()

    @staticmethod
    def open_dialog(this):
        message = LanguageLoader().get('ABOUT_RFCODE_DES')
        this._dialog = IntroductionDialog(LanguageLoader().get('ABOUT_RFCODE'), message)
        this._dialog.show()

    @staticmethod
    def open_instruction():
        webbrowser.open(Loader().get_path('PRODUCTION_INSTRUCTION'))

