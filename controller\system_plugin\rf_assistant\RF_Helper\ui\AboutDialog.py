# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *


class AboutDialog(QDialog):
    def __init__(self, parent, x, y):
        super().__init__()
        self.parent = parent
        self.setWindowTitle("关于我们")
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setGeometry(x, y, 400, 300)  # 增加高度以适应布局

        self.setWindowIcon(QIcon('icon.png'))

        # 应用主题样式
        self._apply_theme()

    def _apply_theme(self):
        """应用主题样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.apply_dialog_theme(self)
        except Exception as e:
            print(f"应用关于对话框主题失败: {e}")
            # 如果主题应用失败，使用默认样式
            self.setStyleSheet("""
                QDialog {
                    background-color: #f9f9f9;
                    border-radius: 10px;
                }
                QLabel {
                    font-size: 14px;
                    color: #333;
                }
                QComboBox {
                    font-size: 14px;
                    padding: 5px;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    background-color: white;
                }
                QDialogButtonBox {
                    background-color: #4CAF50;
                    color: white;
                    padding: 10px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QDialogButtonBox:hover {
                    background-color: #45a049;
                }
                QGridLayout {
                    margin: 20px;
                }
            """)

        layout = QVBoxLayout()

        # 上半部分 - 图标和应用信息
        top_layout = QHBoxLayout()

        # 应用程序图标
        icon_label = QLabel(self)
        pixmap = QPixmap('icon.png').scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        icon_label.setPixmap(pixmap)
        icon_label.setAlignment(Qt.AlignLeft)

        # 应用程序名称和版本
        info_layout = QVBoxLayout()
        button_layout = QVBoxLayout()
        title = QLabel("RF助手")
        title.setStyleSheet("font-size: 22px; font-weight: bold;")
        version = QLabel("Version 3.3")
        version.setStyleSheet("font-size: 16px;")
        info_layout.addWidget(title)
        info_layout.addWidget(version)
        info_layout.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)
        self.updateButton = QPushButton("检查更新")
        self.updateInfoLabel = QLabel("")
        self.updateButton.clicked.connect(self.on_button_clicked)
        top_layout.addWidget(icon_label, 0, Qt.AlignLeft)
        top_layout.addLayout(info_layout, 1)
        button_layout.addWidget(self.updateButton)
        button_layout.addWidget(self.updateInfoLabel)
        top_layout.addLayout(button_layout, 0)
        # 分隔线
        separator = QLabel()
        separator.setFrameStyle(QLabel.HLine | QLabel.Sunken)

        # 开发者信息
        developer_info = QLabel("作者：刘卫菠10124054\n团队：深圳自动化二队")
        developer_info.setAlignment(Qt.AlignCenter)
        developer_info.setStyleSheet("font-size: 14px;")

        # 版权信息
        copyright_info = QLabel(
            "RAN开发三部/无线研究院/无线产品经营部/中兴通讯股份有限公司\n"
            "RAN Development Dept.Ⅲ/Wireless Product R&D Institute/Wireless Product Operation Division"
        )
        copyright_info.setAlignment(Qt.AlignCenter)
        copyright_info.setStyleSheet("font-size: 12px; color: #777;")

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)

        # 布局
        layout.addLayout(top_layout)
        layout.addWidget(separator)
        layout.addSpacing(20)
        layout.addWidget(developer_info)
        layout.addSpacing(10)
        layout.addWidget(copyright_info)
        layout.addStretch()
        layout.addWidget(button_box, 1, Qt.AlignCenter)
        self.setLayout(layout)

    def on_button_clicked(self):
        self.updateButton.setEnabled(False)
        self.updateInfoLabel.setText('正在检查更新版本')
        self.parent.statusBar.showMessage('正在检查更新版本...', 10000)