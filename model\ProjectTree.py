# encoding=utf-8
'''
Create on  2019年10月14日

@author: 10244931, 102443352, 10154402
'''
import os
import shutil
import threading

from PyQt5.Qt import QThread, pyqtSignal

from controller.parser.reader.ReaderFactory import SUFFIX_READER_MAP
from model.data_file.DataFile import SuiteDataFile, \
    PyDataFile, ResourceDataFile, DictionaryDataFile
from model.data_file.Repository import DataFileRepository, ProjectTreeRepository
from settings.SystemSettings import SystemSettings


class DataFileParseThread(QThread):

    parse_finished = pyqtSignal()
    parse_finished_2 = pyqtSignal()

    def __init__(self, data_file):
        super().__init__()
        self._data_file = data_file
        self.is_emit_signal = True
        self.is_emit_signal_2 = False

    def run(self):
        if not hasattr(self._data_file, "path"):
            return
        if not DataFileRepository().find(self._data_file.path):
            self._data_file.parse()
        else:
            self._data_file = DataFileRepository().find(self._data_file.path)
        if self.is_emit_signal:
            self.parse_finished.emit()
        if self.is_emit_signal_2:
            self.parse_finished_2.emit()

    def run_in_main_thread(self):
        if not hasattr(self._data_file, "path"):
            return
        if not DataFileRepository().find(self._data_file.path):
            self._data_file.parse()
        else:
            self._data_file = DataFileRepository().find(self._data_file.path)

class ProjectTree(object):

    __slots__ = [
        "_lock",
        "_path",
        "_name",
        "_files",
        "_suffix",
        "is_dirty",
        "init_file",
        "suffix"
    ]

    def __init__(self, path):
        self.is_dirty = False
        self._path = path
        self._name = os.path.split(path)[-1]
        self._files = []
        self._suffix = None
        self.init_file = None
        self._lock = threading.Lock()
        python_ver = SystemSettings().read('PYTHON_VER')
        print("python_ver", python_ver)
        if python_ver == '3':
            self.suffix = 'robot'
        else:
            self.suffix = 'tsv'
        ProjectTreeRepository().add(os.path.abspath(path), self)

    @property
    def path(self):
        return os.path.abspath(self._path)

    def is_delete(self):
        return not os.path.exists(self.path)

    def open(self):
        if self.init_file:
            self.init_file.open()

    def set_dirty(self):
        self.is_dirty = True

    def parse(self, force_parse=True):
        with self._lock:
            self._files = []
            subItems = os.listdir(self._path)
            subItems.sort()
            for subItem in subItems:
                if self._is_ignore(subItem):
                    continue
                path = os.path.join(self._path, subItem)
                suffix = os.path.splitext(path)[-1][1:]
                if os.path.isdir(path) and subItem != "__pycache__":
                    file = ProjectTree(path)
                elif self._is_py(suffix):
                    file = PyDataFile(path)
                    DataFileRepository().add(os.path.abspath(path), file)
                elif self._is_init_file(path):
                    file = DataFileRepository().find(path)
                    if (not file) or force_parse:
                        file = DictionaryDataFile(path)
                        file.parse()
                    self.init_file = file
                    DataFileRepository().add(os.path.abspath(path), file)
                    continue
                elif self._is_parsed(path):
                    file = DataFileRepository().find(path)
                    DataFileRepository().add(os.path.abspath(path), file)
                elif self._is_rf_file(self._get_suffix(subItem)):
                    file = SuiteDataFile(path)
                    file.parse()
                    if not hasattr(file, "testcases") and not isinstance(DataFileRepository().find(os.path.abspath(path)), SuiteDataFile):
                        file = ResourceDataFile(path)
                        file.parse()
                    DataFileRepository().add(os.path.abspath(path), file)
                else:
                    continue
                self._add_child(file)
            self._sort_files()

    def _sort_files(self):
        dirs = filter(lambda file: os.path.isdir(file.path), self._files)
        resources = filter(lambda file: isinstance(file, ResourceDataFile), self._files)
        suites = filter(lambda file: isinstance(file, SuiteDataFile), self._files)
        pys = filter(lambda file: isinstance(file, PyDataFile), self._files)
        self._files = []
        self._files.extend(dirs)
        self._files.extend(suites)
        self._files.extend(resources)
        self._files.extend(pys)

    def rename(self, name):
        cur_dir, target_dir = self.path, os.path.abspath(os.path.join(os.path.split(self.path)[0], name))
        self._copy_dir(cur_dir, target_dir)
        for file in os.listdir(self.path):
            path = os.path.abspath(os.path.join(self.path, file))
            if os.path.isfile(path) and DataFileRepository().find(path):
                self._rename_data_file(path, cur_dir, target_dir)
            if os.path.isdir(path) and ProjectTreeRepository().find(path):
                ProjectTreeRepository().find(path)._rename(path, cur_dir, target_dir)
        shutil.rmtree(cur_dir)
        self._update_repo(cur_dir, cur_dir, target_dir)
        self._path = os.path.abspath(target_dir)

    def save(self):
        for file in self._files:
            if file.is_dirty:
                if isinstance(file, ProjectTree):
                    if not os.path.exists(file._path):
                        os.mkdir(file._path)
                file.save()
        if self.init_file:
            self.init_file.save()
        self.is_dirty = False

    def delete(self):
        if os.path.exists(self._path):
            shutil.rmtree(self._path)
        self._clear_sub_data_files()
        self._clear_sub_trees()
        ProjectTreeRepository().delete(self._path)

    def del_child(self, index):
        file = self._files[index]
        file.delete()
        self._files.remove(file)

    def add_suite(self, name, suffix="tsv"):
        suffix = self.suffix
        res = self.add_file(name, SuiteDataFile("").__class__.__name__)
        with open(os.path.join(self.path, "{0}.{1}".format(name, suffix)), "w", encoding="UTF-8") as f:
            f.write("*Test Cases*")
        return res

    def add_resource(self, name, suffix="tsv"):
        suffix = self.suffix
        return self.add_file(name, ResourceDataFile("").__class__.__name__)

    def add_dictionary(self, name, suffix="tsv"):
        suffix = self.suffix
        os.mkdir(os.path.join(self._path, name))
        project_tree = self._add_project_tree(name)
        project_tree.add_file("__init__", DictionaryDataFile("").__class__.__name__)
        project_tree._suffix = suffix
        return project_tree

    def add_file(self, name, file_type, suffix="tsv"):
        suffix = self.suffix
        path = os.path.abspath(os.path.join(self._path, name + "." + suffix))
        with open(path, "w") as f:
            f.flush()
        file = eval(file_type)(path)
        if self._is_child(file):
            raise Exception("file {} already exists.".format(name))
        DataFileRepository().add(path, file)
        self._append_file(file)
        return file

    def _append_file(self, file):
        dirs = [file for file in self._files if os.path.isdir(file.path)]
        resources = [file for file in self._files if isinstance(file, ResourceDataFile)]
        suites = [file for file in self._files if isinstance(file, SuiteDataFile)]
        pys = [file for file in self._files if isinstance(file, PyDataFile)]
        if os.path.isdir(file.path):
            self._files.insert(len(dirs), file)
        elif isinstance(file, SuiteDataFile):
            self._files.insert(len(dirs) + len(suites), file)
        elif isinstance(file, ResourceDataFile):
            self._files.insert(len(dirs) + len(resources) + len(suites), file)
        elif isinstance(file, PyDataFile):
            self._files.insert(len(dirs) + len(resources) + len(suites) + len(pys), file)

    def add_keyword(self, name, args):
        if self.init_file is None:
            self.add_init_file(self.suffix)
        self.is_dirty = True
        return self.init_file.add_keyword(name, args).content[-1]

    def delete_keyword(self, index):
        self.is_dirty = True
        return self.init_file.delete_keyword(index)

    def add_list(self, name, values):
        if self.init_file is None:
            self.add_init_file(self.suffix)
        self.is_dirty = True
        res = self.init_file.add_list(name, values)
        return "repeat" if isinstance(res, str) else res.content[-1]

    def add_scalar(self, name, value):
        if self.init_file is None:
            self.add_init_file(self.suffix)
        self.is_dirty = True
        res = self.init_file.add_scalar(name, value)
        return "repeat" if isinstance(res, str) else res.content[-1]

    def add_dict(self, name, value):
        if self.init_file is None:
            self.add_init_file(self.suffix)
        self.is_dirty = True
        res = self.init_file.add_dict(name, value)
        return "repeat" if isinstance(res, str) else res.content[-1]

    def delete_variable(self, index):
        self.is_dirty = True
        return self.init_file.delete_variable(index)

    def add_testcase(self, name):
        self.init_file.add_testcase(name)
        self.is_dirty = True

    def add_init_file(self, suffix):
        suffix = suffix if suffix.startswith(".") else "." + suffix.strip()
        init_file_path = os.path.join(self._path, "__init__" + suffix)
        with open(init_file_path, "w") as f:
            f.flush()
        self.init_file = DictionaryDataFile(init_file_path)
        DataFileRepository().add(os.path.abspath(init_file_path), self.init_file)
        self.is_dirty = True

    def _add_project_tree(self, name):
        path = os.path.abspath(os.path.join(self._path, name))
        file = ProjectTree(path)
        if self._is_child(file):
            raise Exception("dictionary {} already exists.".format(name))
        ProjectTreeRepository().add(path, file)
        file.set_dirty()
        self._append_file(file)
        return file

    def _is_data_file(self, file):
        return isinstance(file, ResourceDataFile) or \
            isinstance(file, SuiteDataFile)

    def _is_child(self, file):
        return file.path in [item.path for item in self._files]

    def _is_py(self, suffix):
        return suffix == "py"

    def _get_suffix(self, file_name):
        return os.path.splitext(file_name)[-1][1:]

    def _is_rf_file(self, suffix):
        return suffix in SUFFIX_READER_MAP.keys()

    def _is_parsed(self, path):
        return path in DataFileRepository().keys()

    def _is_ignore(self, file_name):
        return file_name.startswith(".")

    def _add_child(self, file):
        for _file in self._files:
            if file.path == _file.path:
                return
        self._files.append(file)

    def get_path(self):
        return self._path

    def _is_init_file(self, path):
        if self._is_rf_file(self._get_suffix(path)):
            if os.path.split(os.path.splitext(path)[0])[-1] == "__init__":
                self._suffix = self._get_suffix(path)
                return True
        return False

    def _has_init_file(self):
        return self._suffix is not None

    def _clear_sub_data_files(self):
        paths = [path for path in DataFileRepository().keys()]
        for path in set(paths):
            if path.startswith(self._path + os.path.sep):
                DataFileRepository().find(path).delete()

    def _clear_sub_trees(self):
        dir_paths = [dir_path for dir_path in ProjectTreeRepository().keys()]
        for dir_path in dir_paths:
            if dir_path.startswith(self._path + os.path.sep):
                ProjectTreeRepository().find(dir_path).delete()

    def _copy_dir(self, src, target):
        if os.path.exists(target):
            shutil.rmtree(target, ignore_errors=True)
        shutil.copytree(src, target)

    def _rename(self, cur_dir, pre_head, change_head):
        for file in os.listdir(cur_dir):
            path = os.path.abspath(os.path.join(cur_dir, file))
            if os.path.isfile(path) and DataFileRepository().find(path):
                self._rename_data_file(path, pre_head, change_head)
            elif os.path.isdir(path) and ProjectTreeRepository().find(path):
                self._rename(path, pre_head, change_head)
        self._update_repo(cur_dir, pre_head, change_head)

    def _rename_data_file(self, cur_file_path, pre_head, change_head):
        data_file = DataFileRepository().find(os.path.abspath(cur_file_path))
        path = cur_file_path.replace(pre_head + os.path.sep, change_head + os.path.sep)
        data_file.change_path(os.path.abspath(path))

    def _update_repo(self, cur_dir, pre_head, change_head):
        self._path = os.path.abspath((cur_dir + os.path.sep).replace(pre_head + os.path.sep, change_head + os.path.sep))
        project_tree = ProjectTreeRepository().find(cur_dir)
        ProjectTreeRepository().delete(cur_dir)
        ProjectTreeRepository().add(os.path.abspath((cur_dir + os.path.sep).replace(pre_head + os.path.sep, change_head + os.path.sep)), project_tree)

if __name__ == '__main__':
    tree = ProjectTree(r"D:\Demo\rf-ide\rf-ide\testcases\ut\test_file\test_dic")
    #     tree.parse()
    #     tree.add_dictionary("gupanTest")
    #     tree.add_suite("gupan")
    #     tree.add_resource("gupan002")
    #     tree.save()
    tree.delete()
