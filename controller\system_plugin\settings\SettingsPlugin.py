# encoding=utf-8
'''
Created on 2024年12月19日

@author: AI Assistant
'''

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader


class SettingsPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        settings_menu = self._parent_window.addMenu('&设置')
        self._add_item_preferences(settings_menu)

    def _add_item_preferences(self, settings_menu):
        try:
            title = LanguageLoader().get('PREFERENCES')
            print(f"创建偏好设置菜单项，标题: {title}")

            preferences_action = QAction(QIcon(''), title, self._parent_window)

            # 使用lambda确保信号连接正确
            preferences_action.triggered.connect(lambda: self._open_preferences_dialog())

            settings_menu.addAction(preferences_action)
            print("偏好设置菜单项已添加")

        except Exception as e:
            print(f"创建偏好设置菜单项失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)

    def _open_preferences_dialog(self):
        print("=== 偏好设置菜单项被点击 ===")
        try:
            print("步骤1: 开始打开偏好设置对话框...")

            # 直接创建简单的偏好设置对话框
            self._create_preferences_dialog()

        except Exception as e:
            print(f"打开偏好设置对话框失败: {e}")
            import traceback
            traceback.print_exc()


    def _create_preferences_dialog(self):
        """创建偏好设置对话框"""
        print("步骤2: 尝试导入原始Preferences类...")
        try:
            from controller.system_plugin.tools.item.Preferences.Preferences import Preferences
            print("步骤3: Preferences类导入成功")

            title = LanguageLoader().get('PREFERENCES')
            print(f"步骤4: 对话框标题: {title}")

            self._dialog = Preferences(title)
            print("步骤5: Preferences对象创建成功")

            self._dialog.show()
            print("步骤6: 偏好设置对话框已显示并置顶")

        except Exception as e:
            print(f"步骤4-6失败: 创建或显示对话框失败: {e}")
            import traceback
            traceback.print_exc()