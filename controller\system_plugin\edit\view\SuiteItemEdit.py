# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
from PyQt5.QtWidgets import QVBoxLayout
from controller.system_plugin.edit.view.component.Tag import Tag
from controller.system_plugin.edit.view.DirectoryEdit import DirectoryEdit


class SuiteItemEdit(DirectoryEdit):

    def __init__(self, parent):
        super().__init__(parent)
        self._layout = None

    def load(self):
        if self._layout:
            return self._layout
        super().load()
        self._default_tags = self._set_default_tags()
        self._set_layout()
        return self._layout

    def _set_layout(self):
        self._layout = QVBoxLayout()
        self._layout.addLayout(self._name)
        self._layout.addLayout(self._path)
        self._layout.addLayout(self._settings)
        self._layout.addLayout(self._documentation.get_layout())
        self._layout.addLayout(self._suite_setup.get_layout())
        self._layout.addLayout(self._suite_teardown.get_layout())
        self._layout.addLayout(self._test_setup.get_layout())
        self._layout.addLayout(self._test_teardown.get_layout())
        self._layout.addLayout(self._force_tags.get_layout())
        self._layout.addLayout(self._default_tags.get_layout())
        self._layout.addLayout(self._import_area.get_layout())
        self._layout.addLayout(self._variable_area.get_layout())
        self._layout.addLayout(self._metadata_area.get_layout())

    def _set_default_tags(self):
        tag = Tag(self._parent_window, 'Default Tags')
        tag.load()
        return tag

    def fill_data(self, parsed_item):
        super().fill_data(parsed_item)
        self._default_tags.fill_data(parsed_item.default_tags)

    def _set_visible_area(self):
        super()._set_visible_area()
        self._default_tags.set_visible(self._is_visible)
