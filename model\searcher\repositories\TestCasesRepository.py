'''
Created on 2019年12月18日

@author: 10243352
'''
from utility.ObjectRepository import ObjectRepository
from utility.Singleton import Singleton


@Singleton
class TestCasesRepository(ObjectRepository):

    def add(self, path, testcase):
        results = self.find(path)
        if not results:
            self._objDict[path] = [testcase]
            return
        if testcase not in results:
            results.append(testcase)

    def delete(self, path):
        if path not in self.keys():
            return
        del self._objDict[path]
