# encoding=utf-8
'''
Created on 2024年10月10日

@author: 10124054
'''
from functools import partial
from utility.Singleton import Singleton
from controller.system_plugin.SignalDistributor import SignalDistributor
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from utility.PluginRepository import PluginRepository
from controller.system_plugin.rf_assistant.RF_Helper.RfHelper import RFHelperMainFrame


@Singleton
class RFAssistantPlugin():

    def __init__(self, tab_obj):
        self._tab_obj = tab_obj
        self._widget = self._create_widget()
        PluginRepository().add('RFAssistantPlugin', self)
        self.check_token()
 

    def _create_widget(self):
        widget = QWidget()
        layout = QVBoxLayout()
        self.main_window = RFHelperMainFrame(self)
        PluginRepository().add('RF_HELPER', self.main_window)
        centralWidget = self.main_window.centralWidget()
        
        # 添加账户信息显示栏
        account_container = QWidget()
        account_layout = QHBoxLayout()
        account_container.setLayout(account_layout)
        
        self.account_label = QLabel()
        self.account_label.setTextFormat(Qt.RichText)
        
        # 设置艺术字效果
        font = QFont()
        font.setBold(True)
        font.setPointSize(10)
        self.account_label.setFont(font)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        shadow.setOffset(2, 2)
        self.account_label.setGraphicsEffect(shadow)
        
        self.account_label.setText(f"""
            <span style="color: #FFFFEFD5; vertical-align: middle;">&nbsp;*RF开发助手*</span>
            <span style="color: #FFF0FFFF; vertical-align: middle;">&nbsp;（当前所选项目：{self.main_window.project}，当前登录账户：{self.main_window.account}）</span>
        """)
        self.account_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #6A5ACD, stop:1 #87CEEB);
                padding: 5px 15px;
                border-radius: 10px;
                border: 1px solid #4A90E2;
                min-height: 20px;
                line-height: 20px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7B68EE, stop:1 #87CEFA);
            }
        """)
        self.account_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        account_layout.addWidget(self.account_label)
        account_layout.setContentsMargins(0, 0, 0, 0)
        account_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        self._tool_bar = QToolBar()
        self.initToolBar()
        
        layout.addWidget(account_container)
        layout.addWidget(self._tool_bar)
        layout.addWidget(centralWidget)
        widget.setLayout(layout)
        self.reset_status_bar()
        return widget

    def get_widget(self):
        return self._widget

    @staticmethod
    def add_rf_assistant_tab_by_thread(self):
        print("add_rf_assistant_tab_by_thread")
        

    @staticmethod
    def del_rf_assistant_tab_by_thread(self):
        self._tab_obj.removeTab(SystemSettings().get_value('RF_ASSISTANT_SLOT'))

    def initToolBar(self):
        self.not_login_icon = QApplication.style().standardIcon(QStyle.SP_DialogCloseButton)
        self.login_icon = QApplication.style().standardIcon(QStyle.SP_DialogApplyButton)
        self.open_icon = QApplication.style().standardIcon(QStyle.SP_MessageBoxInformation)
        self.setup_icon = QApplication.style().standardIcon(QStyle.SP_DialogSaveButton)
        self.feedback_icon = QApplication.style().standardIcon(QStyle.SP_FileDialogDetailedView)
        
        self.login_action = QAction(QIcon(self.not_login_icon),"未登录", self._tool_bar)
        setup = QAction(QIcon(self.setup_icon),"设置", self._tool_bar)
        feedback = QAction(QIcon(self.feedback_icon),"反馈", self._tool_bar)
        
        self._tool_bar.addAction(self.login_action)
        self._tool_bar.addAction(setup)
        self._tool_bar.addAction(feedback)
        
        self._tool_bar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        self.login_action.triggered.connect(self.open_login_dialog)
        setup.triggered.connect(self.open_setup_dialog)
        feedback.triggered.connect(self.open_feedback_dialog)

    def check_token(self):
        result = (len(self.main_window.token) > 20)
        if result:
            self.login_action.setIcon(QIcon(self.login_icon))
            self.login_action.setText("已登录")
            return True
        else:
            self.login_action.setIcon(QIcon(self.not_login_icon))
            self.login_action.setText("未登录")
            return False

    def open_setup_dialog(self):
        cursor_pos = QCursor.pos()
        self.main_window.open_setup_dialog(cursor_pos.x(), cursor_pos.y())

    def open_feedback_dialog(self):
        cursor_pos = QCursor.pos()
        self.main_window.open_feedback_dialog(cursor_pos.x(), cursor_pos.y())
 

    def open_login_dialog(self):
        cursor_pos = QCursor.pos()
        self.main_window.open_login_dialog(cursor_pos.x(), cursor_pos.y())

    def on_setup_dialog_close(self, para):
        self.main_window.on_setup_dialog_close(para)

    def on_login_dialog_close(self, para):
        if para and len(para.get("token", "")) > 20:
            self.login_action.setIcon(QIcon(self.login_icon))
            self.login_action.setText("已登录")
            self.account_label.setText(f"""
                <span style="color: #FFFFEFD5; vertical-align: middle;">&nbsp;*RF开发助手*</span>
                <span style="color: #FFF0FFFF; vertical-align: middle;">&nbsp;（当前所选项目：{self.main_window.project}，当前登录账户：{para.get("account")}）</span>
            """)
            self.account_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #6A5ACD, stop:1 #87CEEB);
                    padding: 5px 15px;
                    border-radius: 10px;
                    border: 1px solid #4A90E2;
                    min-height: 20px;
                    line-height: 20px;
                }
                QLabel:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #7B68EE, stop:1 #87CEFA);
                }
            """)
        else:
            self.login_action.setIcon(QIcon(self.not_login_icon))
            self.login_action.setText("未登录")
            self.account_label.setText(f"""
                <span style="color: #FFFFEFD5; vertical-align: middle;">&nbsp;*RF开发助手*</span>
                <span style="color: #FFF0FFFF; vertical-align: middle;">&nbsp;（当前所选项目：{self.main_window.project}，当前登录账户：null）</span>
            """)
            self.account_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #6A5ACD, stop:1 #87CEEB);
                    padding: 5px 15px;
                    border-radius: 10px;
                    border: 1px solid #4A90E2;
                    min-height: 20px;
                    line-height: 20px;
                }
                QLabel:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #7B68EE, stop:1 #87CEFA);
                }
            """)

    def start_parser_keywords(self):
        self.main_window.start_parser_keywords()

    def on_parser_keywords_finish(self, text):
        self.main_window.on_parser_keywords_finish(text)
    
    def reset_status_bar(self):
        self.main_window.resetStatusBar(PluginRepository().find("STATUS_BAR"))
