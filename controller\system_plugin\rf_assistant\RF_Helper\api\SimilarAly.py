import os
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class SimilarAly(object):

    def read_scripts(self, caseInfo, env):
        scripts = []
        caseIds = []
        for caseId, case in caseInfo.items():
            if env == case.get('env', ''):
                scripts.append('\n'.join(case.get('case_content', '')))
                caseIds.append(caseId)
        return scripts, caseIds
    
    def preprocess_script(self, script):
        lines = script.split('\n')
        lines = [line for line in lines if not line.strip().startswith('#') and line.strip()]
        return ' '.join(lines)
    
    def calculate_similarity(self, scripts):
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(scripts)
        similarity_matrix = cosine_similarity(tfidf_matrix)
        return similarity_matrix
    
    def find_duplicates(self, similarity_matrix, filenames, threshold=0.9):
        duplicates = []
        for i in range(len(similarity_matrix)):
            for j in range(i + 1, len(similarity_matrix)):
                if similarity_matrix[i][j] > threshold:
                    duplicates.append((filenames[i], filenames[j], similarity_matrix[i][j]))
        return duplicates


if __name__ == "__main__":
    SA = SimilarAly()
    scripts, caseId = SA.read_scripts('5190')
    preprocessed_scripts = [SA.preprocess_script(script) for script in scripts]
    similarity_matrix = SA.calculate_similarity(preprocessed_scripts)
    duplicates = SA.find_duplicates(similarity_matrix, caseId)
    for file1, file2, similarity in duplicates:
        print(f"重复脚本: {file1} 和 {file2}, 相似度: {similarity}")
