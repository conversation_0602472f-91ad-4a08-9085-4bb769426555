# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     TextEdit
   Description :
   Author :       10140129
   date：          2019/10/23
-------------------------------------------------
   Change Activity:
                   2019/10/23:
-------------------------------------------------
"""
import keyword
import os
import sys
import traceback

from PyQt5.Qsci import QsciScintilla, QsciAPIs
from PyQt5.QtCore import Qt, QTimer, QEvent
from PyQt5.QtGui import QFont, QColor, QCursor, QKeyEvent
from PyQt5.QtWidgets import QApplication, QVBoxLayout, Q<PERSON><PERSON><PERSON>, QAction

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.text_edit.SearchArea import Search<PERSON>rea
from iplatform.highlight.PythonHighlighter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from iplatform.highlight.RobotHighlighter import RobotHighlighter
from model.CurrentItem import CurrentItem
from model.data_file.Repository import DataFileRepository, ProjectTreeRepository, LocalKeyWordRepository, BuildInKeyWordRepository
from settings.UserSettings import UserSettings
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from controller.system_plugin.style.ThemeManager import ThemeManager
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper
TEXT_EDIT = 1


class TextEditor(QsciScintilla):

    def __init__(self):
        super(TextEditor, self).__init__()
        self.setWindowTitle('TextEditor')
        self.setWrapMode(self.WrapNone)
        self._connect_signals()
        self.last_file_path = None
        self.last_file_content = ""
        # 设置UTF-8编码和中文字符支持
        self.setUtf8(True)

        # 设置字符编码模式，确保正确处理多字节字符
        try:
            self.SendScintilla(QsciScintilla.SCI_SETCODEPAGE, QsciScintilla.SC_CP_UTF8)
        except AttributeError:
            print("警告: 当前QsciScintilla版本不支持SCI_SETCODEPAGE")

        # 设置制表符宽度
        self.setTabWidth(4)

        # 设置双字节字符支持
        try:
            self.SendScintilla(QsciScintilla.SCI_SETIMEINTERACTION, QsciScintilla.SC_IME_WINDOWED)
        except AttributeError:
            print("警告: 当前QsciScintilla版本不支持SCI_SETIMEINTERACTION")

        self._set_caret_line()
        self._set_auto_completion()
        
        # 浮动提示相关初始化
        self._alt_pressed = False
        self._tooltip = None
        self._tooltip_window = None  # 浮动提示窗口
        self._current_hover_keyword = None  # 当前悬停的关键字
        # 根据文件类型选择高亮器
        self.lexer = None  # 初始化为None，在load_file时根据文件类型设置
        #self._prepare_keywords()
        self._set_scroll()

        # 初始化行号边距
        self._setup_line_numbers()
        self.theme_manager = ThemeManager()

        # 关键字跳转相关初始化
        self._ctrl_pressed = False
        self._current_keyword = None
        self._keyword_jumper = SpecifiedKeywordJumper()
        self._underlined_indicators = []  # 存储下划线指示器

        # 主题相关初始化
        self._current_theme = "white"  # 默认白色主题
        self._load_initial_theme()

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 确保双击事件能够正常工作
        self.setFocusPolicy(Qt.StrongFocus)

        # 设置中文字符处理
        self._setup_chinese_character_support()

        # 设置字体以改善中文字符显示
        self._setup_font_for_chinese()

        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

        # 添加快捷键绑定
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence
        self.comment_shortcut = QShortcut(QKeySequence("Ctrl+3"), self)
        self.comment_shortcut.activated.connect(self._comment_selection)
        self.uncomment_shortcut = QShortcut(QKeySequence("Ctrl+4"), self)
        self.uncomment_shortcut.activated.connect(self._uncomment_selection)

        # 滚动事件相关初始化
        self._scroll_timer = None
        self._last_scroll_position = 0
        self._setup_scroll_monitoring()

        # 初始化文件相关属性
        self._file_path = None
        self._data_file_obj = None

    def _set_auto_completion(self):
        self.setAutoCompletionSource(QsciScintilla.AcsAll)
        self.setAutoCompletionCaseSensitivity(True)
        self.setAutoCompletionReplaceWord(False)
        self.setAutoCompletionThreshold(1)
        self.setAutoCompletionUseSingle(QsciScintilla.AcusExplicit)

    def _prepare_keywords(self):
        self.__api = QsciAPIs(self.lexer)
        autocompletions = keyword.kwlist + UserSettings().get_value('PYTHON_KEYWORDS')
        for ac in autocompletions:
            self.__api.add(ac)
        self.__api.prepare()

    def _connect_signals(self):
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.show_item.connect(self._show_content)
        self._signal_distributor.highlight_keyword.connect(self._highlight_py_keyword)
        self._signal_distributor.refresh_text_edit.connect(self._refresh_content)
        self.textChanged.connect(self.set_star)

    def _set_caret_line(self):
        self.setCaretLineVisible(True)
        lineColor = QColor(Qt.yellow).lighter(160)
        self.setCaretLineBackgroundColor(lineColor)

    def _set_scroll(self):
        self.SendScintilla(self.SCI_SETSCROLLWIDTH, 300)
        self.SendScintilla(self.SCI_SETSCROLLWIDTHTRACKING, True)

    def _setup_scroll_monitoring(self):
        """设置滚动监控"""
        try:
            # 获取垂直滚动条
            v_scrollbar = self.verticalScrollBar()
            if v_scrollbar:
                # 连接滚动条的valueChanged信号
                v_scrollbar.valueChanged.connect(self._on_scroll_value_changed)
                print("已设置垂直滚动监控")

            # 获取水平滚动条
            h_scrollbar = self.horizontalScrollBar()
            if h_scrollbar:
                # 连接滚动条的valueChanged信号
                h_scrollbar.valueChanged.connect(self._on_scroll_value_changed)
                print("已设置水平滚动监控")

        except Exception as e:
            print(f"设置滚动监控失败: {e}")

    def _on_scroll_value_changed(self, value):
        """滚动条值改变时的处理"""
        try:
            # 如果已经有定时器在运行，先停止它
            if self._scroll_timer and self._scroll_timer.isActive():
                self._scroll_timer.stop()

            # 创建新的定时器，延迟触发语法高亮
            from PyQt5.QtCore import QTimer
            self._scroll_timer = QTimer()
            self._scroll_timer.setSingleShot(True)
            self._scroll_timer.timeout.connect(self._on_scroll_finished)
            self._scroll_timer.start(150)  # 150ms后触发，避免滚动过程中频繁触发

        except Exception as e:
            print(f"处理滚动事件失败: {e}")

    def _on_scroll_finished(self):
        """滚动结束后的处理"""
        try:
            # 获取当前滚动位置
            current_scroll_pos = self.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE

            # 只有当滚动位置真正改变时才触发语法高亮
            if current_scroll_pos != self._last_scroll_position:
                self._last_scroll_position = current_scroll_pos
                print(f"滚动结束，当前可见行: {current_scroll_pos}")

                # 触发可见区域的语法高亮
                self._trigger_visible_area_highlighting()

        except Exception as e:
            print(f"滚动结束处理失败: {e}")

    def _setup_line_numbers(self):
        """初始化行号边距设置"""
        try:
            # 设置行号边距类型为数字边距
            self.setMarginType(0, QsciScintilla.NumberMargin)

            # 设置行号边距宽度（初始宽度，会在set_star中动态调整）
            self.setMarginWidth(0, "0000")

            # 启用行号显示
            self.setMarginLineNumbers(0, True)

            # 设置行号字体
            self.setMarginsFont(QFont(UserSettings().get_value("FONT"), 10))

            print("行号边距初始化完成")

        except Exception as e:
            print(f"初始化行号边距时出错: {e}")

    def _setup_chinese_character_support(self):
        """设置中文字符支持"""
        try:
            # 设置双字节字符支持
            try:
                self.SendScintilla(QsciScintilla.SCI_SETIMEINTERACTION, QsciScintilla.SC_IME_WINDOWED)
            except AttributeError:
                print("警告: 当前QsciScintilla版本不支持SCI_SETIMEINTERACTION")

            # 设置字符编码页为UTF-8
            try:
                self.SendScintilla(QsciScintilla.SCI_SETCODEPAGE, QsciScintilla.SC_CP_UTF8)
            except AttributeError:
                print("警告: 当前QsciScintilla版本不支持SCI_SETCODEPAGE")

            # 设置选择模式，确保中文字符选择正确
            try:
                self.SendScintilla(QsciScintilla.SCI_SETSELECTIONMODE, QsciScintilla.SC_SEL_STREAM)
            except AttributeError:
                print("警告: 当前QsciScintilla版本不支持SCI_SETSELECTIONMODE")

            # 基本的UTF-8设置（这个应该总是可用的）
            self.setUtf8(True)

            print("中文字符支持设置完成")

        except Exception as e:
            print(f"设置中文字符支持时出错: {e}")

    def _setup_font_for_chinese(self):
        """设置适合中文字符显示的字体"""
        try:
            # 获取用户设置的字体
            font_family = UserSettings().get_value("FONT")
            font_size = UserSettings().get_value("TEXT_EDIT_FONT_SIZE")

            # 创建字体对象
            font = QFont()
            font.setFamily(font_family)
            font.setPointSize(font_size)

            # 设置字体属性以改善中文字符显示
            font.setStyleHint(QFont.Monospace)  # 使用等宽字体
            font.setFixedPitch(True)  # 固定字符宽度
            font.setKerning(False)  # 禁用字符间距调整

            # 应用字体设置
            # 注意：不直接设置编辑器字体，因为语法高亮器会覆盖它
            # 字体设置将在语法高亮器中处理

            print(f"字体设置完成: {font_family}, {font_size}pt")

        except Exception as e:
            print(f"设置字体时出错: {e}")

    def load(self):
        self._layout = QVBoxLayout()
        search_area = SearchArea(self)
        search_area.load()
        self._layout.addLayout(search_area.get_layout())
        self._layout.addWidget(self)

    def get_layout(self):
        return self._layout

    def set_last_file_path(self, path):
        self.last_file_path = path

    def load_file(self, path):
        if path:
            path = os.path.abspath(path)

            try:
                # 保存当前文件的滚动位置
                scroll_pos = self.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE
                cursor_pos = self.getCursorPosition()
                # 将滚动位置信息存储到PluginRepository中，供EditPluginController使用
                scroll_info = PluginRepository().find('TEXT_EDIT_SCROLL_INFO')
                if not scroll_info:
                    scroll_info = {}
                    PluginRepository().add('TEXT_EDIT_SCROLL_INFO', scroll_info)
                scroll_info[self._file_path] = (scroll_pos, cursor_pos)
            except Exception as e:
                print(f"保存滚动位置失败: {e}")

            self._file_path = path
            self._data_file_obj = None
            if os.path.isdir(path):
                data_file_obj = ProjectTreeRepository().find(path).init_file
            else:
                data_file_obj = DataFileRepository().find(path)

            # 检查是否需要恢复滚动位置
            target_scroll_pos = 0
            target_cursor_pos = (0, 0)
            should_restore_position = False

            try:
                scroll_info = PluginRepository().find('TEXT_EDIT_SCROLL_INFO')
                if scroll_info and path in scroll_info:
                    target_scroll_pos, target_cursor_pos = scroll_info[path]
                    should_restore_position = True
                    print(f"准备恢复文件 {path} 的滚动位置: {target_scroll_pos}, 光标位置: {target_cursor_pos}")
            except Exception as e:
                print(f"检查滚动位置信息时出错: {e}")

            # 根据文件类型设置高亮器
            if path.endswith('.robot') or path.endswith('.tsv'):
                print('设置Robot语法高亮')
                self.lexer = RobotHighlighter(self)
            else:
                print('设置Python语法高亮')
                self.lexer = PythonHighlighter(self)
            self.last_file_content = self.get()

            # 设置文本内容
            if data_file_obj:
                self.setReadOnly(False)
                self.setText(data_file_obj.get_content())
            else:
                self.setText("")
                self.setReadOnly(True)
            self._data_file_obj = data_file_obj

            # 如果需要恢复位置，在设置文本后立即恢复，避免跳动
            if should_restore_position:
                try:
                    # 立即恢复滚动位置，不使用延迟
                    self.SendScintilla(2613, target_scroll_pos)  # SCI_SETFIRSTVISIBLELINE
                    self.setCursorPosition(target_cursor_pos[0], target_cursor_pos[1])
                    print(f"已立即恢复文件 {path} 的滚动位置: {target_scroll_pos}, 光标位置: {target_cursor_pos}")
                except Exception as e:
                    print(f"立即恢复滚动位置失败: {e}")

            # 根据当前主题应用语法高亮颜色
            self._apply_syntax_highlighting_for_theme()
            # 在语法高亮设置完成后，重新设置行号区域颜色（防止被setLexer覆盖）
            self.reset_margin_color()

            # 初始化滚动位置记录
            self._last_scroll_position = self.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE

            # 延迟触发语法高亮，确保界面稳定后再进行
            if should_restore_position:
                from PyQt5.QtCore import QTimer
                def delayed_highlight():
                    try:
                        self._trigger_visible_area_highlighting()
                    except Exception as e:
                        print(f"延迟触发语法高亮失败: {e}")

                QTimer.singleShot(100, delayed_highlight)

    def _trigger_visible_area_highlighting(self):
        """触发当前可见区域的语法高亮"""
        try:
            if not hasattr(self, 'SendScintilla'):
                return

            # 获取当前可见区域
            first_visible = self.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE
            lines_on_screen = self.SendScintilla(2370)  # SCI_LINESONSCREEN
            total_lines = self.lines()

            # 计算需要高亮的范围（包含缓冲区）
            buffer_lines = 10
            start_line = max(0, first_visible - buffer_lines)
            end_line = min(total_lines, first_visible + lines_on_screen + buffer_lines)

            # 获取lexer并触发语法高亮
            if self.lexer and hasattr(self.lexer, 'styleText'):
                # 计算字节位置
                start_pos = self.SendScintilla(2167, start_line)  # SCI_POSITIONFROMLINE
                if end_line >= total_lines:
                    end_pos = self.SendScintilla(2006)  # SCI_GETLENGTH
                else:
                    end_pos = self.SendScintilla(2167, end_line)  # SCI_POSITIONFROMLINE

                # 强制重新高亮可见区域
                self.lexer.styleText(start_pos, end_pos)

                # 强制刷新显示
                self.update()

        except Exception as e:
            print(f"触发可见区域语法高亮失败: {e}")

    def get(self):
        return self.text()

    def _show_content(self, item):
        LocalRepoUpdater(item).update()
        tab = PluginRepository().find('EDIT_TAB')
        path = CurrentItem().get().get("path")
        if not path:
            return
        print('self.last_file_path: ', self.last_file_path)
        print('path:', path)
        if not self.last_file_path or path != self.last_file_path:
            self.load_file(path)
        # 高亮并滚动到item名称
        text = CurrentItem().get().get("name")
        if not text:
            return
        if path.endswith('.robot') or path.endswith('.tsv'):
            line_number = self.find_line_number(self.text(), text)
            if line_number != -1:
                self._scroll_to_center(line_number)
                self.setCursorPosition(line_number, 0)
                # 选中整行内容
                line_text = self.text(line_number)
                self.setSelection(line_number, 0, line_number, len(line_text))
        self._trigger_visible_area_highlighting()
        self.reset_margin_color()

    def reset_margin_color(self):
        # 在语法高亮设置完成后，重新设置行号区域颜色（防止被setLexer覆盖）
        if self._current_theme == "dark":
            colors = self.theme_manager.get_theme_colors('dark')
        elif self._current_theme == "cool_blue":
            colors = self.theme_manager.get_theme_colors('cool_blue')
        elif self._current_theme == "eye_protect_green":
            colors = self.theme_manager.get_theme_colors('eye_protect_green')
        else:
            colors = self.theme_manager.get_theme_colors('light')
        margin_bg = colors['margin_bg']
        margin_text = colors['margin_text']
        self.setMarginsBackgroundColor(QColor(margin_bg))
        self.setMarginsForegroundColor(QColor(margin_text))
        # 强制刷新显示
        self.update()
        self.repaint()

    def _highlight_py_keyword(self, text):
        line_number = self.find_py_keyword_line_number(self.text(), text)
        print(line_number)
        if line_number:
            # 将目标行滚动到屏幕中间
            self._scroll_to_center(line_number)
            # 只定位光标，不选中文字（避免与Ctrl+点击跳转冲突）
            self.setCursorPosition(line_number, 8)

    def find_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if line.startswith(target):  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def find_py_keyword_line_number(self, text, target):
        lines = text.splitlines()
        for i, line in enumerate(lines):
            if target in line and 'def ' in line:  # 判断是否以目标字符串开头
                return i
        return -1  # 如果找不到，返回 -1

    def _refresh_content(self, item):
        path = CurrentItem().get().get("path")
        if not path:
            return
        self.load_file(path)

    def _scroll_to_center(self, target_line):
        """将目标行滚动到屏幕三分之一处"""
        try:
            # 获取编辑器的可见区域信息
            lines_on_screen = self.SendScintilla(QsciScintilla.SCI_LINESONSCREEN)

            if lines_on_screen > 0:
                # 计算屏幕三分之一处的行号
                third_offset = lines_on_screen // 3

                # 计算目标行应该滚动到的位置（目标行在屏幕三分之一处）
                target_first_visible_line = target_line - third_offset

                # 确保滚动位置在合理范围内
                total_lines = self.lines()
                max_line = max(0, total_lines - lines_on_screen)
                target_first_visible_line = max(0, min(target_first_visible_line, max_line))

                # 执行滚动
                self.SendScintilla(QsciScintilla.SCI_SETFIRSTVISIBLELINE, target_first_visible_line)
                print(f"已将第 {target_line} 行滚动到屏幕三分之一处")
            else:
                # 如果无法获取屏幕行数，使用默认滚动
                self.ensureLineVisible(target_line)

        except Exception as e:
            print(f"滚动到屏幕三分之一处时出错: {e}")
            # 出错时使用默认滚动
            self.ensureLineVisible(target_line)

    def _clear_selection(self):
        """清除文字选中的具体实现"""
        try:
            # 使用QsciScintilla的正确方法清除选中
            self.SendScintilla(QsciScintilla.SCI_CLEARSELECTIONS)
        except Exception as e:
            print(f"清除选中时出错: {e}")

    def keyPressEvent(self, event):
        """处理键盘按下事件"""
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = True
            # 如果鼠标在关键字上，立即显示下划线
            self._check_keyword_under_cursor()
        elif event.key() == Qt.Key_Alt:
            self._alt_pressed = True
            # 如果鼠标在关键字上，立即显示浮动提示
            self._show_keyword_tooltip()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # 处理回车键，根据文件类型自动缩进
            self._handle_enter_key()
        elif event.key() == Qt.Key_Backspace:
            # 处理退格键，确保正确删除中文字符
            self._handle_backspace_key()
        elif event.key() == Qt.Key_Delete:
            # 处理删除键，确保正确删除中文字符
            self._handle_delete_key()
        elif event.key() in [Qt.Key_PageUp, Qt.Key_PageDown, Qt.Key_Up, Qt.Key_Down,
                           Qt.Key_Home, Qt.Key_End]:
            # 处理可能导致滚动的按键
            super().keyPressEvent(event)
            # 延迟触发滚动处理，因为按键可能导致滚动
            self._on_scroll_value_changed(0)
        else:
            super().keyPressEvent(event)

    def _handle_backspace_key(self):
        """处理退格键，确保正确删除中文字符"""
        try:
            # 如果有选中文本，直接删除选中内容
            if self.hasSelectedText():
                self.removeSelectedText()
                return

            # 使用Scintilla的原生删除功能，它能正确处理UTF-8字符
            self.SendScintilla(QsciScintilla.SCI_DELETEBACK)

        except Exception as e:
            print(f"处理退格键时出错: {e}")
            # 如果出错，使用默认处理
            super().keyPressEvent(QKeyEvent(QEvent.KeyPress, Qt.Key_Backspace, Qt.NoModifier))

    def _handle_delete_key(self):
        """处理删除键，确保正确删除中文字符"""
        try:
            # 如果有选中文本，直接删除选中内容
            if self.hasSelectedText():
                self.removeSelectedText()
                return

            # 使用Scintilla的原生删除功能，它能正确处理UTF-8字符
            self.SendScintilla(QsciScintilla.SCI_CLEAR)

        except Exception as e:
            print(f"处理删除键时出错: {e}")
            # 如果出错，使用默认处理
            super().keyPressEvent(QKeyEvent(QEvent.KeyPress, Qt.Key_Delete, Qt.NoModifier))

    def _handle_enter_key(self):
        """处理回车键，根据文件类型自动插入缩进"""
        # 获取当前行号和列号
        line, index = self.getCursorPosition()
        line_text = self.text(line)
        
        # 计算当前行的缩进
        if len(line_text.lstrip()) == 0:
            indent = line_text[0:-1]
        else:
            indent = line_text[0:len(line_text) - len(line_text.lstrip())]
        # 根据文件类型决定缩进方式
        if hasattr(self, '_file_path') and self._file_path:
            if self._file_path.endswith('.py'):
                # Python文件使用4个空格缩进
                indent_str = ' ' * 4
            else:
                # Robot/TSV文件使用tab缩进
                indent_str = '\t'
        else:
            # 默认使用tab缩进
            indent_str = '\t'
        # 插入新行和缩进
        self.insert('\n')
        self.setCursorPosition(line + 1, 0)
        self.insert(indent)
        # 如果上一行以冒号结尾，增加一级缩进
        if line_text.rstrip().endswith(':')  or line_text.lstrip().startswith('FOR') or line_text.lstrip().startswith(':'):
            self.insert(indent_str)
            # 设置光标到新行缩进后
            print('FOR:设置光标到新行缩进后')
            self.setCursorPosition(line + 1, len(indent) + len(indent_str))
        else:
            # 设置光标到新行缩进后
            print('设置光标到新行缩进后')
            self.setCursorPosition(line + 1, len(indent))

    def keyReleaseEvent(self, event):
        """处理键盘释放事件"""
        if event.key() == Qt.Key_Control:
            self._ctrl_pressed = False
            # 清除下划线
            self._clear_keyword_underline()
        elif event.key() == Qt.Key_Alt:
            self._alt_pressed = False
            # 隐藏浮动提示
            self._hide_keyword_tooltip()
        super().keyReleaseEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        if self._ctrl_pressed:
            self._check_keyword_under_cursor()
        elif self._alt_pressed:
            self._show_keyword_tooltip()
        super().mouseMoveEvent(event)

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        print(f"mousePressEvent: button={event.button()}, ctrl_pressed={self._ctrl_pressed}")

        # 先调用父类的处理，确保基本的鼠标事件处理正常
        super().mousePressEvent(event)

        # 然后处理我们的自定义逻辑
        if event.button() == Qt.LeftButton and self._ctrl_pressed:
            keyword = self._get_keyword_at_cursor()
            if keyword and self._is_keyword_in_repository(keyword):
                print(f"mousePressEvent: 跳转到关键字 {keyword}")
                self._jump_to_keyword(keyword)

    def wheelEvent(self, event):
        """处理鼠标滚轮事件"""
        # 先调用父类的滚轮事件处理
        super().wheelEvent(event)

        # 然后触发我们的滚动处理
        self._on_scroll_value_changed(0)  # 参数不重要，主要是触发延迟处理

    def mouseDoubleClickEvent(self, event):
        """处理鼠标双击事件"""
        print(f"mouseDoubleClickEvent triggered: button={event.button()}, pos={event.pos()}")

        # 检查编辑器是否为只读状态
        if self.isReadOnly():
            print("编辑器处于只读状态，双击事件可能受限")

        super().mouseDoubleClickEvent(event)

        if event.button() == Qt.LeftButton:
            try:
                # 获取当前行文本
                line, _ = self.getCursorPosition()
                line_text = self.text(line)
                print(f"双击行号: {line}, 行内容: '{line_text}'")

                # 检查是否是有效的测试用例/关键字行（不以空格/tab开头）
                if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                    item_name = line_text.strip()
                    if item_name:
                        print(f"准备展开路径: {item_name}")
                        self.expand_by_path(False)
                        
                    else:
                        print("行内容为空，跳过处理")
                else:
                    print("行以空格/tab/[开头，跳过处理")
            except Exception as e:
                print(f"处理双击事件时出错: {e}")
                import traceback
                traceback.print_exc()

    def set_star(self):
        if self._data_file_obj and self._file_path == CurrentItem().get().get("path"):
            SignalDistributor().text_editor_modify(CurrentItem().get_current_item())
        self.setMarginWidth(0, len(str(self.lines())) * 11)

        # 确保高亮更新
        self.SendScintilla(QsciScintilla.SCI_COLOURISE, 0, -1)

    def notice_update(self):
        if CurrentItem().name:
            current_item_path = CurrentItem().get().get("path")
            last_item = ProjectTreeItemRepository().query(self.last_file_path)
            tab_obj = PluginRepository().find('EDIT_TAB')
            if last_item and tab_obj.tabText(tab_obj.currentIndex()) == 'Text Edit' and \
                    self.last_file_path and self.last_file_path != current_item_path and last_item.text(0).startswith('*'):
                SignalDistributor().text_editor_update(last_item, {'text_edit': self.last_file_content})

    def _check_keyword_under_cursor(self):
        """检查光标下的关键字并显示下划线"""
        keyword = self._get_keyword_at_cursor()
        if keyword and self._is_keyword_in_repository(keyword):
            if self._current_keyword != keyword:
                self._clear_keyword_underline()
                self._add_keyword_underline(keyword)
                self._current_keyword = keyword
        else:
            if self._current_keyword:
                self._clear_keyword_underline()
                self._current_keyword = None

    def _get_keyword_at_cursor(self):
        """获取光标位置的关键字"""
        try:
            # 获取鼠标位置
            cursor_pos = QCursor.pos()
            widget_pos = self.mapFromGlobal(cursor_pos)

            # 使用 SendScintilla 获取鼠标位置对应的字符位置
            char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
            if char_pos < 0:
                return None

            # 获取行号和列号
            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
            col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)

            # 获取该行的文本
            line_text = self.text(line)
            if not line_text or col >= len(line_text):
                return None

            # 查找关键字边界
            start = col
            end = col

            # 向前查找关键字开始位置
            while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] in '_ '):
                start -= 1

            # 向后查找关键字结束位置
            while end < len(line_text) and (line_text[end].isalnum() or line_text[end] in '_ '):
                end += 1

            # 提取关键字
            keyword = line_text[start:end].strip()
            return keyword if keyword else None

        except Exception as e:
            print(f"获取光标位置关键字时出错: {e}")
            return None

    def _is_keyword_in_repository(self, keyword):
        """检查关键字是否在仓库中"""
        try:
            # 检查内置关键字
            builtin_result = BuildInKeyWordRepository().query(keyword)
            if builtin_result:
                if isinstance(builtin_result, list):
                    return any(item.get('name') == keyword for item in builtin_result)
                else:
                    return builtin_result.get(keyword) is not None

            # 检查本地关键字（包括用户关键字和库关键字）
            local_result = LocalKeyWordRepository().query(keyword)
            if local_result:
                if isinstance(local_result, list):
                    return any(item.get('name') == keyword for item in local_result)
                else:
                    return local_result.get(keyword) is not None

            return False
        except Exception as e:
            print(f"检查关键字时出错: {e}")
            return False

    def _add_keyword_underline(self, keyword):
        """为关键字添加下划线"""
        try:
            # 获取鼠标位置
            cursor_pos = QCursor.pos()
            widget_pos = self.mapFromGlobal(cursor_pos)

            # 使用 SendScintilla 获取鼠标位置对应的字符位置
            char_pos = self.SendScintilla(QsciScintilla.SCI_POSITIONFROMPOINTCLOSE, widget_pos.x(), widget_pos.y())
            if char_pos < 0:
                return

            # 获取行号和列号
            line = self.SendScintilla(QsciScintilla.SCI_LINEFROMPOSITION, char_pos)
            col = char_pos - self.SendScintilla(QsciScintilla.SCI_POSITIONFROMLINE, line)

            # 获取该行的文本
            line_text = self.text(line)
            if not line_text:
                return

            # 查找关键字在行中的位置
            start = col
            end = col

            # 向前查找关键字开始位置
            while start > 0 and (line_text[start - 1].isalnum() or line_text[start - 1] in '_ '):
                start -= 1

            # 向后查找关键字结束位置
            while end < len(line_text) and (line_text[end].isalnum() or line_text[end] in '_ '):
                end += 1

            # 设置蓝色下划线指示器
            indicator_id = 1  # 使用指示器ID 1
            self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)
            self.SendScintilla(QsciScintilla.SCI_INDICSETSTYLE, indicator_id, 1)  # 1 = INDIC_UNDERLINE
            self.SendScintilla(QsciScintilla.SCI_INDICSETFORE, indicator_id, 0x0000FF)  # 蓝色
            self.SendScintilla(QsciScintilla.SCI_INDICSETALPHA, indicator_id, 255)  # 设置透明度

            # 计算字符位置
            start_pos = self.positionFromLineIndex(line, start)
            end_pos = self.positionFromLineIndex(line, end)

            # 添加下划线
            self.SendScintilla(QsciScintilla.SCI_INDICATORFILLRANGE, start_pos, end_pos - start_pos)

            # 记录下划线位置
            self._underlined_indicators.append((start_pos, end_pos - start_pos))

        except Exception as e:
            print(f"添加关键字下划线时出错: {e}")

    def _clear_keyword_underline(self):
        """清除关键字下划线"""
        try:
            indicator_id = 1
            self.SendScintilla(QsciScintilla.SCI_SETINDICATORCURRENT, indicator_id)

            # 清除所有下划线
            for start_pos, length in self._underlined_indicators:
                self.SendScintilla(QsciScintilla.SCI_INDICATORCLEARRANGE, start_pos, length)

            self._underlined_indicators.clear()

        except Exception as e:
            print(f"清除关键字下划线时出错: {e}")

    def _show_keyword_tooltip(self):
        """显示关键字浮动提示"""
        # try:
        keyword = self._get_keyword_at_cursor()
        if not keyword or not self._is_keyword_in_repository(keyword):
            self._hide_keyword_tooltip()
            return

        if keyword != self._current_hover_keyword:
            self._current_hover_keyword = keyword
            html_content = self._generate_tooltip_html(keyword)
            
            # 创建或更新浮动提示窗口
            if not self._tooltip_window:
                from PyQt5.QtWidgets import QLabel
                self._tooltip_window = QLabel(self, Qt.ToolTip)
                self._tooltip_window.setWordWrap(True)
                self._tooltip_window.setMargin(5)
            
            self._tooltip_window.setText(html_content)
            self._tooltip_window.adjustSize()
            
            # 获取鼠标位置并调整提示窗口位置
            cursor_pos = QCursor.pos()
            self._tooltip_window.move(cursor_pos.x() + 15, cursor_pos.y() + 15)
            self._tooltip_window.show()

        # except Exception as e:
        #     print(f"显示关键字浮动提示时出错: {e}")

    def _hide_keyword_tooltip(self):
        """隐藏关键字浮动提示"""
        try:
            if self._tooltip_window:
                self._tooltip_window.hide()
                self._tooltip_window = None
            self._current_hover_keyword = None
        except Exception as e:
            print(f"隐藏关键字浮动提示时出错: {e}")

    def _generate_tooltip_html(self, keyword):
        """生成关键字提示的HTML内容"""
        from controller.system_plugin.style.ThemeManager import ThemeManager
        theme_manager = ThemeManager()
        colors = theme_manager.get_theme_colors()

        # 设置默认颜色值
        default_colors = {
            'editor_text': '#000000',
            'editor_bg': '#FFFFFF',
            'header_text': '#333333',
            'highlight_text': '#0066CC',
            'link_color': '#0066CC',
            'error_text': '#FF0000'
        }

        # 合并主题颜色和默认值
        final_colors = {**default_colors, **colors}

        # 检查是否为内置关键字
        builtin_result = BuildInKeyWordRepository().query(keyword)
        if builtin_result:
            # 处理列表或字典返回类型
            if isinstance(builtin_result, list):
                keyword_info = next((item for item in builtin_result if item.get('name') == keyword), None)
            else:
                keyword_info = builtin_result.get(keyword)
            
            if keyword_info:
                return f"""
                <div style="color: {final_colors['editor_text']}; background-color: {final_colors['editor_bg']};
                            padding: 5px; border: 1px solid #ccc; border-radius: 3px;">
                    <h3 style="color: {final_colors['header_text']}; margin: 0 0 5px 0;">内置关键字</h3>
                    <p style="margin: 0;"><b>名称:</b> <span style="color: {final_colors['highlight_text']}">{keyword}</span></p>
                </div>
                """

        # 检查是否为本地关键字
        local_result = LocalKeyWordRepository().query(keyword)
        if local_result:
            # 处理列表或字典返回类型
            if isinstance(local_result, list):
                keyword_info = next((item for item in local_result if item.get('name') == keyword), None)
            else:
                keyword_info = local_result.get(keyword)
            
            if not keyword_info:
                return ""

            # 安全获取路径和引用文件
            path = keyword_info.get('path', '未知') if isinstance(keyword_info, dict) else '未知'
            used_files = []
            if isinstance(keyword_info, dict):
                used_files = keyword_info.get('usedFile', {}).get(self.project, []) if hasattr(self, 'project') else []

            html = f"""
            <div style="color: {final_colors['editor_text']}; background-color: {final_colors['editor_bg']};
                        padding: 5px; border: 1px solid #ccc; border-radius: 3px;">
                <h3 style="color: {final_colors['header_text']}; margin: 0 0 5px 0;">用户关键字</h3>
                <p style="margin: 0;"><b>名称:</b> <span style="color: {final_colors['highlight_text']}">{keyword}</span></p>
                <p style="margin: 0;"><b>路径:</b> <span style="color: {final_colors['link_color']}">{path}</span></p>
            """

            # 添加引用文件列表
            if used_files:
                html += '<p style="margin: 5px 0 0 0;"><b>引用文件:</b></p><ul style="margin: 0 0 0 15px; padding: 0;">'
                for file_path in used_files:
                    html += f'<li style="color: {final_colors["link_color"]}">{file_path}</li>'
                html += '</ul>'

            html += '</div>'
            return html

        return ""

    def _jump_to_keyword(self, keyword):
        """跳转到关键字定义"""
        try:
            # 获取关键字路径
            path = self._keyword_jumper.get_keyword_path_from_local_repository(keyword)
            if path:
                # 使用SpecifiedKeywordJumper进行跳转
                target_item = self._keyword_jumper.get_keyword_item(path, keyword)
                print(target_item)
                if target_item:
                    # 跳转成功后，延迟选中目标关键字
                    QTimer.singleShot(100, lambda: self._select_keyword_in_editor(keyword))
                    print(f"跳转到关键字: {keyword} at {path}")
            else:
                print(f"未找到关键字定义: {keyword}")

        except Exception as e:
            print(f"跳转到关键字时出错: {e}")

    def _select_keyword_in_editor(self, keyword):
        print(f'_select_keyword_in_editor: 查找关键字 "{keyword}"')
        """在编辑器中选中目标关键字/测试用例"""
        try:
            # 获取当前编辑器
            current_editor = self._get_current_editor()
            if not current_editor:
                print("未找到当前编辑器")
                return

            # 在当前编辑器中查找并选中关键字
            if hasattr(current_editor, 'findFirst'):
                # 首先尝试精确匹配（整个单词）
                found = current_editor.findFirst(keyword, False, True, True, True)

                if not found:
                    # 如果精确匹配失败，尝试部分匹配
                    found = current_editor.findFirst(keyword, False, True, False, True)

                if found:
                    # 获取选中的文本位置
                    line_from, index_from, line_to, index_to = current_editor.getSelection()

                    # 确保关键字居中显示
                    current_editor.ensureLineVisible(line_from)

                    # 设置光标位置到行首
                    current_editor.setCursorPosition(line_from, 0)

                    # 选中整行（如果是测试用例名称）
                    line_text = current_editor.text(line_from).strip()
                    if line_text == keyword or line_text.startswith(keyword):
                        # 选中整行
                        current_editor.setSelection(line_from, 0, line_from, len(current_editor.text(line_from).rstrip()))
                    else:
                        # 只选中关键字部分
                        current_editor.setSelection(line_from, index_from, line_to, index_to)

                    print(f"已选中关键字: {keyword} at line {line_from + 1}")
                else:
                    print(f"在编辑器中未找到关键字: {keyword}")
                    # 如果没找到，至少滚动到文件开头
                    current_editor.setCursorPosition(0, 0)

        except Exception as e:
            print(f"选中关键字时出错: {e}")
            import traceback
            traceback.print_exc()

    def _get_current_editor(self):
        """获取当前活动的编辑器"""
        try:
            # 尝试获取文本编辑器
            text_edit = PluginRepository().find('TEXT_EDIT')
            if text_edit and hasattr(text_edit, 'isVisible') and text_edit.isVisible():
                return text_edit

            # 尝试获取编辑页签中的编辑器
            edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
            if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
                editor = edit_plugin_controller._editor
                if editor and hasattr(editor, 'findFirst'):
                    return editor

            return None

        except Exception as e:
            print(f"获取当前编辑器时出错: {e}")
            return None

    def _show_context_menu(self, position):
        """显示右键菜单"""
        try:
            # 创建菜单
            menu = QMenu(self)


            # 检查是否有选中文本且是测试用例/关键字行
            show_in_tree = False
            item_name = ""
            if self.hasSelectedText():
                line_from, _, line_to, _ = self.getSelection()
                if line_from == line_to:  # 单行选中
                    line_text = self.text(line_from)
                    if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                        item_name = line_text.strip()
                        if item_name:
                            show_in_tree = True

            # 添加"在导航树显示"选项
            if show_in_tree:
                show_in_tree_action = QAction(f"在左侧导航树中显示...", self)
                show_in_tree_action.triggered.connect(self.expand_by_path, True)
                menu.addAction(show_in_tree_action)
                menu.addSeparator()

            # 添加标准编辑菜单选项
            self._add_standard_menu_actions(menu)

            # 分隔符
            menu.addSeparator()
            # 添加注释菜单选项
            comment_action = QAction("注释 (Ctrl+3)", self)
            comment_action.setEnabled(self.hasSelectedText())
            comment_action.triggered.connect(self._comment_selection)
            comment_action.setShortcut("Ctrl+3")
            menu.addAction(comment_action)

            # 添加去注释菜单选项
            uncomment_action = QAction("去注释 (Ctrl+4)", self)
            uncomment_action.setEnabled(self.hasSelectedText())
            uncomment_action.triggered.connect(self._uncomment_selection)
            uncomment_action.setShortcut("Ctrl+4")
            menu.addAction(uncomment_action)

            # 显示菜单
            menu.exec_(self.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单时出错: {e}")

    def expand_by_path(self, is_reload_file=True):
        """展开路径并选中对应的项目，滚动到中间位置"""
        item_name = ''
        try:
            # 获取当前光标位置的行文本（双击时通常没有选中文本，使用光标位置）
            current_line, _ = self.getCursorPosition()
            line_text = self.text(current_line)
            print(f"expand_by_path: 当前行号 {current_line}, 行文本 '{line_text.strip()}'")

            # 检查是否是有效的测试用例/关键字行（不以空格/tab开头）
            if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                item_name = line_text.strip()
                print(f"expand_by_path: 提取的项目名称 '{item_name}'")

                if item_name:
                    project_explorer = PluginRepository().find('PROJECT_EXPLORER')
                    if project_explorer:
                        print(f"expand_by_path: 调用 expand_item_by_path({self._file_path}, {item_name}, {is_reload_file}, True)")
                        # 传递case_name参数，并设置is_update_editor=True以便更新编辑器
                        project_explorer.expand_item_by_path(self._file_path, item_name, is_reload_file, True)
                        print(f"expand_by_path: 成功展开并选中项目 '{item_name}'")
                    else:
                        print("expand_by_path: 未找到 PROJECT_EXPLORER")
                else:
                    print("expand_by_path: 项目名称为空，跳过处理")
            else:
                print("expand_by_path: 行以空格/tab/[开头，不是有效的测试用例/关键字行")

        except Exception as e:
            print(f"expand_by_path 执行时出错: {e}")
            import traceback
            traceback.print_exc()

    def _comment_selection(self):
        """注释选中文本"""
        if not self.hasSelectedText():
            return
            
        line_from, index_from, line_to, index_to = self.getSelection()
        
        # 处理多行注释
        for line in range(line_from, line_to + 1):
            line_text = self.text(line)
            if not line_text.lstrip().startswith('#'):
                # 在行首插入#
                self.setSelection(line, 0, line, 0)
                self.insert('#')
        
        # 恢复原始选区
        self.setSelection(line_from, index_from + 1 if index_from > 0 else 0, 
                         line_to, index_to + 1 if index_to > 0 else 0)

    def _uncomment_selection(self):
        """去除选中文本的注释"""
        if not self.hasSelectedText():
            return
            
        line_from, index_from, line_to, index_to = self.getSelection()
        
        # 处理多行去注释
        for line in range(line_from, line_to + 1):
            line_text = self.text(line)
            if line_text.lstrip().startswith('#'):
                # 找到第一个#的位置
                pos = line_text.find('#')
                self.setSelection(line, pos, line, pos + 1)
                self.removeSelectedText()
        
        # 恢复原始选区
        new_from = index_from - 1 if index_from > 0 and line_text.startswith('#') else index_from
        new_to = index_to - 1 if index_to > 0 and line_text.startswith('#') else index_to
        self.setSelection(line_from, max(0, new_from), 
                         line_to, max(0, new_to))

    def _add_standard_menu_actions(self, menu):
        """添加标准编辑菜单选项"""
        try:
            # 撤销
            undo_action = QAction("撤销 (Ctrl+Z)", self)
            undo_action.setEnabled(self.isUndoAvailable())
            undo_action.triggered.connect(self.undo)
            undo_action.setShortcut("Ctrl+Z")
            menu.addAction(undo_action)

            # 重做
            redo_action = QAction("重做 (Ctrl+Y)", self)
            redo_action.setEnabled(self.isRedoAvailable())
            redo_action.triggered.connect(self.redo)
            redo_action.setShortcut("Ctrl+Y")
            menu.addAction(redo_action)

            # 分隔符
            menu.addSeparator()

            # 剪切
            cut_action = QAction("剪切 (Ctrl+X)", self)
            cut_action.setEnabled(self.hasSelectedText())
            cut_action.triggered.connect(self.cut)
            cut_action.setShortcut("Ctrl+X")
            menu.addAction(cut_action)

            # 复制
            copy_action = QAction("复制 (Ctrl+C)", self)
            copy_action.setEnabled(self.hasSelectedText())
            copy_action.triggered.connect(self.copy)
            copy_action.setShortcut("Ctrl+C")
            menu.addAction(copy_action)

            # 粘贴
            paste_action = QAction("粘贴 (Ctrl+V)", self)
            paste_action.setEnabled(QApplication.clipboard().mimeData().hasText())
            paste_action.triggered.connect(self.paste)
            paste_action.setShortcut("Ctrl+V")
            menu.addAction(paste_action)

            # 分隔符
            menu.addSeparator()

            # 全选
            select_all_action = QAction("全选 (Ctrl+A)", self)
            select_all_action.triggered.connect(lambda: self.selectAll())
            select_all_action.setShortcut("Ctrl+A")
            menu.addAction(select_all_action)

            # 分隔符
            menu.addSeparator()

            # 添加缩进菜单选项
            indent_action = QAction("缩进 (Tab)", self)
            indent_action.setEnabled(self.hasSelectedText())
            indent_action.triggered.connect(self._indent_selection)
            indent_action.setShortcut("Tab")
            menu.addAction(indent_action)

            # 添加反缩进菜单选项
            unindent_action = QAction("反缩进 (Shift+Tab)", self)
            unindent_action.setEnabled(self.hasSelectedText())
            unindent_action.triggered.connect(self._unindent_selection)
            unindent_action.setShortcut("Shift+Tab")
            menu.addAction(unindent_action)

            # 分隔符
            menu.addSeparator()

            # 添加刷新菜单选项
            refresh_action = QAction("刷新", self)
            refresh_action.triggered.connect(self._refresh_current_content)
            menu.addAction(refresh_action)

        except Exception as e:
            print(f"添加标准菜单选项时出错: {e}")

    def _refresh_current_content(self):
        """刷新当前文件内容并保持滚动位置"""
        # 保存当前滚动位置
        first_visible_line = self.SendScintilla(QsciScintilla.SCI_GETFIRSTVISIBLELINE)
        current_line = self.getCursorPosition()[0]
        # 刷新内容
        current_item = CurrentItem().get()
        if current_item and 'path' in current_item:
            self._refresh_content(current_item)
        # 恢复滚动位置
        self.SendScintilla(QsciScintilla.SCI_SETFIRSTVISIBLELINE, first_visible_line)
        self.setCursorPosition(current_line, 0)


    def refresh_content_and_select_testcase(self, keyword, is_reload_file=True):
        current_item = CurrentItem().get()
        print(current_item)
        if current_item and 'path' in current_item and is_reload_file:
            self._refresh_content(current_item)
        self._select_keyword_in_editor(keyword)

    def _indent_selection(self):
        """缩进选中文本"""
        if not self.hasSelectedText():
            return
            
        line_from, index_from, line_to, index_to = self.getSelection()
        
        # 处理多行缩进
        for line in range(line_from, line_to + 1):
            # 在行首插入4个空格
            self.setSelection(line, 0, line, 0)
            self.insert('    ')
        
        # 恢复原始选区并调整位置
        self.setSelection(line_from, index_from + 4, 
                         line_to, index_to + 4)

    def _unindent_selection(self):
        """反缩进选中文本"""
        if not self.hasSelectedText():
            return
            
        line_from, index_from, line_to, index_to = self.getSelection()
        
        # 处理多行反缩进
        for line in range(line_from, line_to + 1):
            line_text = self.text(line)
            # 检查行首是否有空格
            if line_text.startswith(' '):
                # 计算要移除的空格数（最多4个）
                spaces_to_remove = min(4, len(line_text) - len(line_text.lstrip()))
                if spaces_to_remove > 0:
                    # 移除空格
                    self.setSelection(line, 0, line, spaces_to_remove)
                    self.removeSelectedText()
        
        # 恢复原始选区并调整位置
        new_from = max(0, index_from - 4) if index_from > 0 else 0
        new_to = max(0, index_to - 4) if index_to > 0 else 0
        self.setSelection(line_from, new_from, 
                         line_to, new_to)

    def _set_theme(self, theme):
        """设置主题（内部方法，由主题管理器调用）"""
        print('TextEditor:_set_theme')
        try:
            self._current_theme = theme

            if theme == "white":
                print('_apply_white_theme')
                self._apply_white_theme()
            elif theme == "dark":
                print('_apply_dark_theme')
                self._apply_dark_theme()
            elif theme == "cool_blue":
                print('_apply_cool_blue_theme')
                self._apply_cool_blue_theme()
            elif theme == "eye_protect_green":
                print('_apply_eye_protect_green_theme')
                self._apply_eye_protect_green_theme()

            print(f"文本编辑器已切换到{theme}主题")

        except Exception as e:
            print(f"设置主题时出错: {e}")

    def _load_initial_theme(self):
        """加载初始主题"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()

            # 根据主题管理器的主题设置编辑器主题
            if current_theme == "cool_blue":
                self._set_theme("cool_blue")
            elif current_theme == "eye_protect_green":
                self._set_theme("eye_protect_green")
            elif theme_manager.is_dark_theme(current_theme):
                self._set_theme("dark")
            else:
                self._set_theme("white")

        except Exception as e:
            print(f"加载初始主题时出错: {e}")
            # 默认使用白色主题
            self._set_theme("white")

    def _apply_theme_from_manager(self, theme_id):
        """从主题管理器接收主题变更通知"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if theme_id == 'dark':
                # 保存具体的主题ID，以便语法高亮器能够区分不同的深色主题
                self._current_theme_id = theme_id
                self._set_theme("dark")
            elif theme_id == 'cool_blue':
                self._current_theme_id = theme_id
                self._set_theme("cool_blue")
            elif theme_id == 'eye_protect_green':
                self._current_theme_id = theme_id
                self._set_theme("eye_protect_green")
            else:
                self._current_theme_id = theme_id
                self._set_theme("white")

            # 确保行号区域颜色更新
            self.reset_margin_color()
            # 强制重新应用语法高亮
            self._apply_syntax_highlighting_for_theme()
            # 强制刷新界面
            self.update()
            self.repaint()
            print(f"文本编辑器主题已更新为: {theme_id}")

        except Exception as e:
            print(f"应用主题管理器主题时出错: {e}")

    def _get_current_tab_name(self):
        """获取当前活动页签的名称"""
        try:
            from utility.PluginRepository import PluginRepository
            from settings.i18n.Loader import LanguageLoader

            # 获取页签对象
            edit_tab = PluginRepository().find('EDIT_TAB')
            if not edit_tab:
                return None

            # 获取当前页签索引
            current_index = edit_tab.currentIndex()
            if current_index < 0:
                return None

            # 获取页签文本
            tab_text = edit_tab.tabText(current_index)

            # 将显示文本转换为内部标识符
            language_loader = LanguageLoader()
            if tab_text == language_loader.get('EDIT'):
                return 'EDIT'
            elif tab_text == language_loader.get('TEXT_EDIT'):
                return 'TEXT_EDIT'
            elif tab_text == language_loader.get('RUN'):
                return 'RUN'
            elif tab_text == language_loader.get('BASH'):
                return 'BASH'
            elif tab_text == language_loader.get('RF_ASSISTANT'):
                return 'RF_ASSISTANT'
            else:
                return tab_text  # 返回原始文本作为备用

        except Exception as e:
            print(f"获取当前页签名称时出错: {e}")
            return None

    def _apply_white_theme(self):
        """应用白色主题"""
        try:
            # 获取主题颜色配置
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors('light')

            # 清除样式表，恢复默认样式
            self.setStyleSheet("")

            # 恢复默认调色板
            self.setPalette(QApplication.palette())

            # 设置编辑器背景和文字颜色
            self.setColor(QColor(colors['editor_text']))
            self.setPaper(QColor(colors['editor_bg']))

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor(colors['editor_selection_bg']))
            self.setSelectionForegroundColor(QColor(colors['editor_selection_text']))

            # 设置光标颜色
            self.setCaretForegroundColor(QColor(colors['editor_caret']))

            # 设置行号区域（从主题配置获取颜色）
            self.setMarginsBackgroundColor(QColor(colors['margin_bg']))
            self.setMarginsForegroundColor(QColor(colors['margin_text']))

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor(colors['editor_current_line']))
            self.setCaretLineVisible(True)

            # 设置边距线颜色
            self.setEdgeColor(QColor(colors['edge_color']))

            # 设置折叠区域颜色
            self.setFoldMarginColors(QColor(colors['fold_margin_bg']), QColor(colors['fold_margin_fg']))

            # 重新应用语法高亮
            self._apply_syntax_highlighting_for_theme()

            # 在语法高亮设置完成后，重新设置行号区域颜色（防止被setLexer覆盖）
            self.setMarginsBackgroundColor(QColor(colors['margin_bg']))
            self.setMarginsForegroundColor(QColor(colors['margin_text']))
            print(f"重新设置白色主题行号背景色: {colors['margin_bg']}")

        except Exception as e:
            print(f"应用白色主题时出错: {e}")

    def _apply_dark_theme(self):
        """应用黑色主题"""
        try:
            # 获取主题颜色配置
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors('dark')

            # 从主题配置获取颜色
            editor_bg = colors['editor_bg']
            text_color = colors['editor_text']
            margin_bg = colors['margin_bg']
            margin_text = colors['margin_text']
            caret_line_bg = colors['editor_current_line']
            selection_bg = colors['editor_selection_bg']
            selection_text = colors['editor_selection_text']
            caret_color = colors['editor_caret']

            # 设置整个编辑器及其父容器的样式表
            self.setStyleSheet("""
                QsciScintilla {
                    background-color: #2B2B2B;
                    color: #E0E0E0;
                    border: none;
                }
                QsciScintilla QScrollBar:vertical {
                    background-color: #3C3C3C;
                    border: none;
                    width: 12px;
                }
                QsciScintilla QScrollBar::handle:vertical {
                    background-color: #555555;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QsciScintilla QScrollBar::handle:vertical:hover {
                    background-color: #666666;
                }
                QsciScintilla QScrollBar:horizontal {
                    background-color: #3C3C3C;
                    border: none;
                    height: 12px;
                }
                QsciScintilla QScrollBar::handle:horizontal {
                    background-color: #555555;
                    border-radius: 6px;
                    min-width: 20px;
                }
                QsciScintilla QScrollBar::handle:horizontal:hover {
                    background-color: #666666;
                }
                QsciScintilla QScrollBar::add-line, QsciScintilla QScrollBar::sub-line {
                    background: none;
                    border: none;
                }
                QsciScintilla QScrollBar::add-page, QsciScintilla QScrollBar::sub-page {
                    background: none;
                }
            """)

            # 设置文本区域的背景和前景色（与RobotHighlighter保持一致）
            self.setColor(QColor(text_color))  # 文字颜色
            self.setPaper(QColor(editor_bg))  # 编辑器背景色

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor(selection_bg))  # 蓝色选中背景
            self.setSelectionForegroundColor(QColor(selection_text))

            # 设置光标颜色
            self.setCaretForegroundColor(QColor(caret_color))

            # 设置行号区域（根据主题调整背景色）
            self.setMarginsBackgroundColor(QColor(margin_bg))
            self.setMarginsForegroundColor(QColor(margin_text))

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor(caret_line_bg))
            self.setCaretLineVisible(True)

            # 设置边距线颜色（从主题配置获取）
            self.setEdgeColor(QColor(colors['edge_color']))

            # 设置折叠区域颜色（从主题配置获取）
            self.setFoldMarginColors(QColor(colors['fold_margin_bg']), QColor(colors['fold_margin_fg']))

            # 强制设置整个控件的背景色（与RobotHighlighter保持一致）
            palette = self.palette()
            palette.setColor(palette.Base, QColor(editor_bg))
            palette.setColor(palette.Window, QColor(editor_bg))
            palette.setColor(palette.AlternateBase, QColor(editor_bg))
            palette.setColor(palette.Text, QColor(text_color))
            palette.setColor(palette.WindowText, QColor(text_color))
            self.setPalette(palette)

            # 应用黑色主题的语法高亮
            self._apply_syntax_highlighting_for_theme()

            # 在语法高亮设置完成后，重新设置行号区域颜色（防止被setLexer覆盖）
            self.setMarginsBackgroundColor(QColor(margin_bg))
            self.setMarginsForegroundColor(QColor(margin_text))
            print(f"重新设置行号背景色: {margin_bg}")

            # 强制刷新显示
            self.update()
            self.repaint()

        except Exception as e:
            print(f"应用黑色主题时出错: {e}")

    def _apply_syntax_highlighting_for_theme(self):
        """根据当前主题应用语法高亮"""
        try:
            if not hasattr(self, 'lexer') or not self.lexer:
                print("没有找到语法高亮器，跳过语法高亮设置")
                return

            # 获取当前具体的主题ID
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme_id = theme_manager.get_current_theme()

            # 检查是否是Robot语法高亮器
            if isinstance(self.lexer, RobotHighlighter):
                # 使用RobotHighlighter的主题刷新方法，它会自动检测当前主题
                if hasattr(self.lexer, 'refresh_theme'):
                    self.lexer.refresh_theme()
                    print(f"Robot语法高亮器已刷新主题: {current_theme_id}")
                else:
                    # 兼容旧版本的方法
                    if theme_manager.is_dark_theme(current_theme_id):
                        self._apply_robot_dark_colors()
                    else:
                        self._apply_robot_light_colors()
            # 检查是否是Python语法高亮器
            elif hasattr(self.lexer, 'refresh_theme'):
                # 使用PythonHighlighter的主题刷新方法，它会自动检测当前主题
                self.lexer.refresh_theme()
                print(f"Python语法高亮器已刷新主题: {current_theme_id}")
            else:
                # 兼容旧版本的语法高亮器
                if theme_manager.is_dark_theme(current_theme_id):
                    self._apply_dark_syntax_highlighting()
                else:
                    self._apply_light_syntax_highlighting()

            # 重新设置词法分析器以应用更改
            self.setLexer(self.lexer)

        except Exception as e:
            print(f"应用主题语法高亮时出错: {e}")

    def _apply_cool_blue_theme(self):
        """应用酷炫淡蓝色主题"""
        try:
            # 设置整个编辑器及其父容器的样式表
            self.setStyleSheet("""
                QsciScintilla {
                    background-color: #E0EFFF;
                    color: #333333;
                    border: none;
                }
                QsciScintilla QScrollBar:vertical {
                    background-color: #D9EBFF;
                    border: none;
                    width: 12px;
                }
                QsciScintilla QScrollBar::handle:vertical {
                    background-color: #B3D1FF;
                    border-radius: 6px;
                    min-height: 20px;
                    margin: 2px;
                }
                QsciScintilla QScrollBar::handle:vertical:hover {
                    background-color: #A0C8FF;
                }
                QsciScintilla QScrollBar:horizontal {
                    background-color: #D9EBFF;
                    border: none;
                    height: 12px;
                }
                QsciScintilla QScrollBar::handle:horizontal {
                    background-color: #B3D1FF;
                    border-radius: 6px;
                    min-width: 20px;
                    margin: 2px;
                }
                QsciScintilla QScrollBar::handle:horizontal:hover {
                    background-color: #A0C8FF;
                }
            """)

            # 设置文本区域的背景和前景色
            self.setColor(QColor("#333333"))  # 文字颜色
            self.setPaper(QColor("#E0EFFF"))  # 编辑器背景色

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor("#4A90E2"))  # 蓝色选中背景
            self.setSelectionForegroundColor(QColor("#FFFFFF"))

            # 设置光标颜色
            self.setCaretForegroundColor(QColor("#333333"))

            # 设置行号区域
            self.setMarginsBackgroundColor(QColor("#D9EBFF"))
            self.setMarginsForegroundColor(QColor("#333333"))

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor("#E6F2FF"))
            self.setCaretLineVisible(True)

            # 设置边距线颜色
            self.setEdgeColor(QColor("#A0C0FF"))

            # 设置折叠区域颜色
            self.setFoldMarginColors(QColor("#D9EBFF"), QColor("#B3D1FF"))

            # 强制设置整个控件的背景色
            palette = self.palette()
            palette.setColor(palette.Base, QColor("#E0EFFF"))
            palette.setColor(palette.Window, QColor("#E0EFFF"))
            palette.setColor(palette.AlternateBase, QColor("#E0EFFF"))
            palette.setColor(palette.Text, QColor("#333333"))
            palette.setColor(palette.WindowText, QColor("#333333"))
            self.setPalette(palette)

            # 应用酷炫淡蓝色主题的语法高亮
            self._apply_syntax_highlighting_for_theme()

            # 在语法高亮设置完成后，重新设置行号区域颜色
            self.setMarginsBackgroundColor(QColor("#D9EBFF"))
            self.setMarginsForegroundColor(QColor("#333333"))

            # 强制刷新显示
            self.update()
            self.repaint()

        except Exception as e:
            print(f"应用酷炫淡蓝色主题时出错: {e}")

    def _apply_eye_protect_green_theme(self):
        """应用酷炫护眼·墨绿色主题"""
        try:
            # 获取主题颜色配置
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors('eye_protect_green')

            # 从主题配置获取颜色
            editor_bg = colors['editor_bg']
            text_color = colors['editor_text']
            margin_bg = colors['margin_bg']
            margin_text = colors['margin_text']
            caret_line_bg = colors['editor_current_line']
            selection_bg = colors['editor_selection_bg']
            selection_text = colors['editor_selection_text']
            caret_color = colors['editor_caret']

            # 设置整个编辑器及其父容器的样式表
            self.setStyleSheet("""
                QsciScintilla {
                    background-color: #1B2B1B;
                    color: #C8E6C9;
                    border: none;
                }
                QsciScintilla QScrollBar:vertical {
                    background-color: #223322;
                    border: none;
                    width: 12px;
                }
                QsciScintilla QScrollBar::handle:vertical {
                    background-color: #388E3C;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QsciScintilla QScrollBar::handle:vertical:hover {
                    background-color: #4CAF50;
                }
                QsciScintilla QScrollBar:horizontal {
                    background-color: #223322;
                    border: none;
                    height: 12px;
                }
                QsciScintilla QScrollBar::handle:horizontal {
                    background-color: #388E3C;
                    border-radius: 6px;
                    min-width: 20px;
                }
                QsciScintilla QScrollBar::handle:horizontal:hover {
                    background-color: #4CAF50;
                }
                QsciScintilla QScrollBar::add-line, QsciScintilla QScrollBar::sub-line {
                    background: none;
                    border: none;
                }
                QsciScintilla QScrollBar::add-page, QsciScintilla QScrollBar::sub-page {
                    background: none;
                }
            """)

            # 设置文本区域的背景和前景色
            self.setColor(QColor(text_color))  # 文字颜色
            self.setPaper(QColor(editor_bg))  # 编辑器背景色

            # 设置选中文本的颜色
            self.setSelectionBackgroundColor(QColor(selection_bg))  # 墨绿色选中背景
            self.setSelectionForegroundColor(QColor(selection_text))

            # 设置光标颜色
            self.setCaretForegroundColor(QColor(caret_color))

            # 设置行号区域
            self.setMarginsBackgroundColor(QColor(margin_bg))
            self.setMarginsForegroundColor(QColor(margin_text))

            # 设置当前行高亮
            self.setCaretLineBackgroundColor(QColor(caret_line_bg))
            self.setCaretLineVisible(True)

            # 设置边距线颜色
            self.setEdgeColor(QColor(colors['edge_color']))

            # 设置折叠区域颜色
            self.setFoldMarginColors(QColor(colors['fold_margin_bg']), QColor(colors['fold_margin_fg']))

            # 强制设置整个控件的背景色
            palette = self.palette()
            palette.setColor(palette.Base, QColor(editor_bg))
            palette.setColor(palette.Window, QColor(editor_bg))
            palette.setColor(palette.AlternateBase, QColor(editor_bg))
            palette.setColor(palette.Text, QColor(text_color))
            palette.setColor(palette.WindowText, QColor(text_color))
            self.setPalette(palette)

            # 应用墨绿色主题的语法高亮
            self._apply_syntax_highlighting_for_theme()

            # 在语法高亮设置完成后，重新设置行号区域颜色（防止被setLexer覆盖）
            self.setMarginsBackgroundColor(QColor(margin_bg))
            self.setMarginsForegroundColor(QColor(margin_text))
            print(f"重新设置墨绿色主题行号背景色: {margin_bg}")

            # 强制刷新显示
            self.update()
            self.repaint()

        except Exception as e:
            print(f"应用墨绿色主题时出错: {e}")

    def _apply_cool_blue_syntax_highlighting(self):
        """应用酷炫淡蓝色主题的语法高亮"""
        print('_apply_cool_blue_syntax_highlighting')
        try:
            # 检查是否是Robot语法高亮器
            if isinstance(self.lexer, RobotHighlighter):
                # 使用RobotHighlighter的主题刷新方法
                if hasattr(self.lexer, 'refresh_theme'):
                    self.lexer.refresh_theme()
                else:
                    # 兼容旧版本的方法
                    self._apply_robot_cool_blue_colors()
            # 检查是否是Python语法高亮器
            elif hasattr(self.lexer, 'refresh_theme'):
                # 使用PythonHighlighter的主题刷新方法
                self.lexer.refresh_theme()
            elif hasattr(self.lexer, 'setColor'):
                # 兼容旧版本的Python语法高亮颜色设置
                self.lexer.setColor(QColor("#333333"), 0)  # 默认文字 - 深灰色
                self.lexer.setColor(QColor("#666666"), 1)  # 注释 - 灰色
                self.lexer.setColor(QColor("#0066CC"), 2)  # 关键字 - 蓝色
                self.lexer.setColor(QColor("#008800"), 3)  # 字符串 - 绿色
                self.lexer.setColor(QColor("#880088"), 4)  # 数字 - 紫色
                self.lexer.setColor(QColor("#CC0000"), 5)  # 操作符 - 红色
                self.lexer.setColor(QColor("#FF6600"), 6)  # 函数名 - 橙色
                self.lexer.setColor(QColor("#660066"), 7)  # 类名 - 深紫色

                # 设置语法高亮器的背景色与主题一致
                if hasattr(self.lexer, 'setPaper'):
                    self.lexer.setPaper(QColor("#E0EFFF"))  # 淡蓝色背景
                print(f"应用酷炫淡蓝色主题语法高亮成功！")

        except Exception as e:
            print(f"应用酷炫淡蓝色主题语法高亮时出错: {e}")

    def _apply_dark_syntax_highlighting(self):
        """应用黑色主题的语法高亮"""
        try:
            # 检查是否是Robot语法高亮器
            if isinstance(self.lexer, RobotHighlighter):
                # 使用RobotHighlighter的主题刷新方法
                if hasattr(self.lexer, 'refresh_theme'):
                    self.lexer.refresh_theme()
                else:
                    # 兼容旧版本的方法
                    self._apply_robot_dark_colors()
            # 检查是否是Python语法高亮器
            elif hasattr(self.lexer, 'refresh_theme'):
                # 使用PythonHighlighter的主题刷新方法
                self.lexer.refresh_theme()
            elif hasattr(self.lexer, 'setColor'):
                # 兼容旧版本的Python语法高亮颜色设置
                self.lexer.setColor(QColor("#E0E0E0"), 0)  # 默认文字 - 浅灰色
                self.lexer.setColor(QColor("#75715E"), 1)  # 注释 - 灰色
                self.lexer.setColor(QColor("#66D9EF"), 2)  # 关键字 - 青色
                self.lexer.setColor(QColor("#A6E22E"), 3)  # 字符串 - 绿色
                self.lexer.setColor(QColor("#AE81FF"), 4)  # 数字 - 紫色
                self.lexer.setColor(QColor("#F92672"), 5)  # 操作符 - 红色
                self.lexer.setColor(QColor("#FD971F"), 6)  # 函数名 - 橙色
                self.lexer.setColor(QColor("#E6DB74"), 7)  # 类名 - 黄色

                # 设置语法高亮器的背景色与主题一致
                if hasattr(self.lexer, 'setPaper'):
                    self.lexer.setPaper(QColor("#353535"))  # 深色背景（稍微浅一些）

        except Exception as e:
            print(f"应用黑色主题语法高亮时出错: {e}")

    def _apply_robot_cool_blue_colors(self):
        """为Robot语法高亮器应用酷炫淡蓝色主题颜色（兼容方法）"""
        try:
            self.lexer.setColor(QColor("#333333"), 0)  # 默认文字 - 深灰色
            self.lexer.setColor(QColor("#666666"), 1)  # 注释 - 灰色
            self.lexer.setColor(QColor("#0066CC"), 2)  # 关键字 - 蓝色
            self.lexer.setColor(QColor("#008800"), 3)  # 字符串 - 绿色
            self.lexer.setColor(QColor("#880088"), 4)  # 数字 - 紫色
            self.lexer.setColor(QColor("#CC0000"), 5)  # 操作符 - 红色
            self.lexer.setColor(QColor("#FF6600"), 6)  # 变量 - 橙色
            self.lexer.setColor(QColor("#660066"), 7)  # 测试用例名 - 深紫色

            # 设置背景色
            if hasattr(self.lexer, 'setPaper'):
                self.lexer.setPaper(QColor("#E0EFFF"))  # 淡蓝色背景
        except Exception as e:
            print(f"应用Robot酷炫淡蓝色主题颜色时出错: {e}")

    def _apply_robot_dark_colors(self):
        """为Robot语法高亮器应用深色主题颜色（兼容方法）"""
        try:
            self.lexer.setColor(QColor("#E0E0E0"), 0)  # 默认文字 - 浅灰色
            self.lexer.setColor(QColor("#75715E"), 1)  # 注释 - 灰色
            self.lexer.setColor(QColor("#66D9EF"), 2)  # 关键字 - 青色
            self.lexer.setColor(QColor("#A6E22E"), 3)  # 字符串 - 绿色
            self.lexer.setColor(QColor("#AE81FF"), 4)  # 数字 - 紫色
            self.lexer.setColor(QColor("#F92672"), 5)  # 操作符 - 红色
            self.lexer.setColor(QColor("#FD971F"), 6)  # 变量 - 橙色
            self.lexer.setColor(QColor("#E6DB74"), 7)  # 测试用例名 - 黄色

            # 设置背景色
            if hasattr(self.lexer, 'setPaper'):
                self.lexer.setPaper(QColor("#353535"))  # 深色背景（稍微浅一些）
        except Exception as e:
            print(f"应用Robot深色主题颜色时出错: {e}")

    def _apply_eye_protect_green_syntax_highlighting(self):
        """应用墨绿色主题的语法高亮"""
        print('_apply_eye_protect_green_syntax_highlighting')
        try:
            # 检查是否是Robot语法高亮器
            if isinstance(self.lexer, RobotHighlighter):
                # 使用RobotHighlighter的主题刷新方法
                if hasattr(self.lexer, 'refresh_theme'):
                    self.lexer.refresh_theme()
                else:
                    # 兼容旧版本的方法
                    self._apply_robot_eye_protect_green_colors()
            # 检查是否是Python语法高亮器
            elif hasattr(self.lexer, 'refresh_theme'):
                # 使用PythonHighlighter的主题刷新方法
                self.lexer.refresh_theme()
            elif hasattr(self.lexer, 'setColor'):
                # 兼容旧版本的Python语法高亮颜色设置（优化对比度）
                self.lexer.setColor(QColor("#E8F5E8"), 0)  # 默认文字 - 更亮的浅绿色
                self.lexer.setColor(QColor("#A5D6A7"), 1)  # 注释 - 中等绿色
                self.lexer.setColor(QColor("#81D4FA"), 2)  # 关键字 - 浅蓝色
                self.lexer.setColor(QColor("#C5E1A5"), 3)  # 字符串 - 亮绿色
                self.lexer.setColor(QColor("#80CBC4"), 4)  # 数字 - 青绿色
                self.lexer.setColor(QColor("#FFAB91"), 5)  # 操作符 - 浅橙色
                self.lexer.setColor(QColor("#FFD54F"), 6)  # 函数名 - 金黄色
                self.lexer.setColor(QColor("#F8BBD0"), 7)  # 类名 - 浅粉色

                # 设置语法高亮器的背景色与主题一致
                if hasattr(self.lexer, 'setPaper'):
                    self.lexer.setPaper(QColor("#1B2B1B"))  # 深墨绿色背景
                print(f"应用墨绿色主题语法高亮成功！")

        except Exception as e:
            print(f"应用墨绿色主题语法高亮时出错: {e}")

    def _apply_robot_eye_protect_green_colors(self):
        """为Robot语法高亮器应用墨绿色主题颜色（兼容方法）"""
        try:
            self.lexer.setColor(QColor("#E8F5E8"), 0)  # 默认文字 - 更亮的浅绿色
            self.lexer.setColor(QColor("#A5D6A7"), 1)  # 注释 - 中等绿色
            self.lexer.setColor(QColor("#81D4FA"), 2)  # 关键字 - 浅蓝色
            self.lexer.setColor(QColor("#C5E1A5"), 3)  # 字符串 - 亮绿色
            self.lexer.setColor(QColor("#80CBC4"), 4)  # 数字 - 青绿色
            self.lexer.setColor(QColor("#FFAB91"), 5)  # 操作符 - 浅橙色
            self.lexer.setColor(QColor("#FFAB91"), 6)  # 变量 - 浅橙色
            self.lexer.setColor(QColor("#F8BBD0"), 7)  # 测试用例名 - 浅粉色

            # 设置背景色
            if hasattr(self.lexer, 'setPaper'):
                self.lexer.setPaper(QColor("#1B2B1B"))  # 深墨绿色背景
        except Exception as e:
            print(f"应用Robot墨绿色主题颜色时出错: {e}")

    def _apply_light_syntax_highlighting(self):
        """应用浅色主题的语法高亮"""
        try:
            # 检查是否是Robot语法高亮器
            if isinstance(self.lexer, RobotHighlighter):
                # 使用RobotHighlighter的主题刷新方法
                if hasattr(self.lexer, 'refresh_theme'):
                    self.lexer.refresh_theme()
                else:
                    # 兼容旧版本的方法
                    self._apply_robot_light_colors()
            # 检查是否是Python语法高亮器
            elif hasattr(self.lexer, 'refresh_theme'):
                # 使用PythonHighlighter的主题刷新方法
                self.lexer.refresh_theme()
            elif hasattr(self.lexer, 'setColor'):
                # 兼容旧版本的Python语法高亮颜色设置
                self.lexer.setColor(QColor("#000000"), 0)  # 默认文字 - 黑色
                self.lexer.setColor(QColor("#008000"), 1)  # 注释 - 绿色
                self.lexer.setColor(QColor("#0000FF"), 2)  # 关键字 - 蓝色
                self.lexer.setColor(QColor("#800080"), 3)  # 字符串 - 紫色
                self.lexer.setColor(QColor("#FF0000"), 4)  # 数字 - 红色
                self.lexer.setColor(QColor("#808080"), 5)  # 操作符 - 灰色
                self.lexer.setColor(QColor("#FF8000"), 6)  # 函数名 - 橙色
                self.lexer.setColor(QColor("#800000"), 7)  # 类名 - 深红色

                # 设置语法高亮器的背景色与主题一致
                if hasattr(self.lexer, 'setPaper'):
                    self.lexer.setPaper(QColor("#FFFFFF"))  # 白色背景

        except Exception as e:
            print(f"应用浅色主题语法高亮时出错: {e}")

    def _apply_robot_light_colors(self):
        """为Robot语法高亮器应用浅色主题颜色（兼容方法）"""
        try:
            self.lexer.setColor(QColor("#000000"), 0)  # 默认文字 - 黑色
            self.lexer.setColor(QColor("#008000"), 1)  # 注释 - 绿色
            self.lexer.setColor(QColor("#0000FF"), 2)  # 关键字 - 蓝色
            self.lexer.setColor(QColor("#800080"), 3)  # 字符串 - 紫色
            self.lexer.setColor(QColor("#FF0000"), 4)  # 数字 - 红色
            self.lexer.setColor(QColor("#808080"), 5)  # 操作符 - 灰色
            self.lexer.setColor(QColor("#FF8000"), 6)  # 变量 - 橙色
            self.lexer.setColor(QColor("#800000"), 7)  # 测试用例名 - 深红色

            # 设置背景色
            if hasattr(self.lexer, 'setPaper'):
                self.lexer.setPaper(QColor("#FFFFFF"))  # 白色背景
        except Exception as e:
            print(f"应用Robot浅色主题颜色时出错: {e}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = TextEditor()
    ex.load_file("D:/calc.py")
    ex.show()
    sys.exit(app.exec_())
