# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     PythonHighlighter
   Description :
   Author :       10140129
   date：          2019/10/24
-------------------------------------------------
   Change Activity:
                   2019/10/24:
-------------------------------------------------
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from settings.UserSettings import UserSettings
from PyQt5.Qsci import QsciLexerPython
from PyQt5.QtGui import QFont, QColor, QCursor

class PythonHighlighter(QsciLexerPython):

    def __init__(self,parent):
        QsciLexerPython.__init__(self,parent)

        # 应用主题颜色
        self._apply_theme_colors()

        # 设置字体
        font=QFont()
        font.setStyleName('Normal')
        font.setPointSize(UserSettings().get_value("TEXT_EDIT_FONT_SIZE"))
        font.setFamily((UserSettings().get_value("FONT")))
        self.setFont(font)

    def _apply_theme_colors(self):
        """根据当前主题应用颜色"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()

            if current_theme == 'dark':
                self._apply_dark_theme_colors()
            elif current_theme == 'cool_blue':
                self._apply_cool_blue_theme_colors()
            elif current_theme == 'eye_protect_green':
                self._apply_eye_protect_green_theme_colors()
            elif current_theme == 'light':
                self._apply_light_theme_colors()

        except Exception as e:
            print(f"应用Python语法高亮主题颜色失败，使用默认浅色主题: {e}")
            self._apply_light_theme_colors()

    def _apply_cool_blue_theme_colors(self):
        """应用淡蓝色主题颜色"""
        # 设置默认背景色为白色
        bg_color = QColor("#E0EFFF")
        self.setDefaultPaper(bg_color)
        self.setDefaultColor(QColor("#333333"))

        # 设置各种样式的颜色（淡蓝色主题）
        self.setColor(QColor("#0066CC"), QsciLexerPython.ClassName)  # 深蓝色 - 类名
        self.setPaper(bg_color, QsciLexerPython.ClassName)

        self.setColor(QColor("#0066CC"), QsciLexerPython.Keyword)  # 深蓝色 - 关键字
        self.setPaper(bg_color, QsciLexerPython.Keyword)

        self.setColor(QColor("#666666"), QsciLexerPython.Comment)  # 灰色 - 注释
        self.setPaper(bg_color, QsciLexerPython.Comment)

        self.setColor(QColor("#FF6600"), QsciLexerPython.Number)  # 橙色 - 数字
        self.setPaper(bg_color, QsciLexerPython.Number)

        self.setColor(QColor("#0066CC"), QsciLexerPython.FunctionMethodName)  # 深蓝色 - 函数名 
        self.setPaper(bg_color, QsciLexerPython.FunctionMethodName)

        # 设置字符串颜色
        self.setColor(QColor("#009933"), QsciLexerPython.SingleQuotedString)  # 绿色 - 单引号字符串
        self.setPaper(bg_color, QsciLexerPython.SingleQuotedString)

        self.setColor(QColor("#009933"), QsciLexerPython.DoubleQuotedString)  # 绿色 - 双引号字符串
        self.setPaper(bg_color, QsciLexerPython.DoubleQuotedString)

        self.setColor(QColor("#009933"), QsciLexerPython.TripleSingleQuotedString)  # 绿色 - 三引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleSingleQuotedString)

        self.setColor(QColor("#009933"), QsciLexerPython.TripleDoubleQuotedString)  # 绿色 - 三引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleDoubleQuotedString)

        # 设置其他样式
        self.setColor(QColor("#CC0000"), QsciLexerPython.Operator)  # 红色 - 操作符
        self.setPaper(bg_color, QsciLexerPython.Operator)

        self.setColor(QColor("#000000"), QsciLexerPython.Identifier)  # 黑色 - 标识符
        self.setPaper(bg_color, QsciLexerPython.Identifier)

        self.setColor(QColor("#666666"), QsciLexerPython.CommentBlock)  # 灰色 - 块注释
        self.setPaper(bg_color, QsciLexerPython.CommentBlock)

        self.setColor(QColor("#CC0000"), QsciLexerPython.UnclosedString)  # 红色 - 未闭合字符串
        self.setPaper(bg_color, QsciLexerPython.UnclosedString)

        self.setColor(QColor("#FF6600"), QsciLexerPython.HighlightedIdentifier)  # 橙色 - 高亮标识符
        self.setPaper(bg_color, QsciLexerPython.HighlightedIdentifier)

    def _apply_light_theme_colors(self):
        """应用浅色主题颜色"""
        # 设置默认背景色为白色
        bg_color = QColor("#FFFFFF")
        self.setDefaultPaper(bg_color)
        self.setDefaultColor(QColor("#000000"))

        # 设置各种样式的颜色和背景色（浅色主题）
        self.setColor(Qt.darkBlue, QsciLexerPython.ClassName)
        self.setPaper(bg_color, QsciLexerPython.ClassName)

        self.setColor(Qt.darkBlue, QsciLexerPython.Keyword)
        self.setPaper(bg_color, QsciLexerPython.Keyword)

        self.setColor(Qt.darkGreen, QsciLexerPython.Comment)
        self.setPaper(bg_color, QsciLexerPython.Comment)

        self.setColor(Qt.darkMagenta, QsciLexerPython.Number)
        self.setPaper(bg_color, QsciLexerPython.Number)

        self.setColor(Qt.black, QsciLexerPython.FunctionMethodName)
        self.setPaper(bg_color, QsciLexerPython.FunctionMethodName)

        # 为其他常用样式设置背景色
        self.setPaper(bg_color, QsciLexerPython.Default)
        self.setPaper(bg_color, QsciLexerPython.SingleQuotedString)
        self.setPaper(bg_color, QsciLexerPython.DoubleQuotedString)
        self.setPaper(bg_color, QsciLexerPython.TripleSingleQuotedString)
        self.setPaper(bg_color, QsciLexerPython.TripleDoubleQuotedString)
        self.setPaper(bg_color, QsciLexerPython.Operator)
        self.setPaper(bg_color, QsciLexerPython.Identifier)
        self.setPaper(bg_color, QsciLexerPython.CommentBlock)
        self.setPaper(bg_color, QsciLexerPython.UnclosedString)
        self.setPaper(bg_color, QsciLexerPython.HighlightedIdentifier)
        self.setPaper(bg_color, QsciLexerPython.Decorator)

    def _apply_dark_theme_colors(self):
        """应用深色主题颜色"""
        # 设置默认背景色为深色（与RobotHighlighter保持一致）
        bg_color = QColor("#353535")
        self.setDefaultPaper(bg_color)
        self.setDefaultColor(QColor("#E0E0E0"))

        # 设置各种样式的颜色和背景色（深色主题）
        self.setColor(QColor("#66D9EF"), QsciLexerPython.ClassName)  # 青色 - 类名
        self.setPaper(bg_color, QsciLexerPython.ClassName)

        self.setColor(QColor("#F92672"), QsciLexerPython.Keyword)  # 红色 - 关键字
        self.setPaper(bg_color, QsciLexerPython.Keyword)

        self.setColor(QColor("#75715E"), QsciLexerPython.Comment)  # 灰色 - 注释
        self.setPaper(bg_color, QsciLexerPython.Comment)

        self.setColor(QColor("#AE81FF"), QsciLexerPython.Number)  # 紫色 - 数字
        self.setPaper(bg_color, QsciLexerPython.Number)

        self.setColor(QColor("#A6E22E"), QsciLexerPython.FunctionMethodName)  # 绿色 - 函数名
        self.setPaper(bg_color, QsciLexerPython.FunctionMethodName)

        # 设置字符串颜色
        self.setColor(QColor("#E6DB74"), QsciLexerPython.SingleQuotedString)  # 黄色 - 字符串
        self.setPaper(bg_color, QsciLexerPython.SingleQuotedString)

        self.setColor(QColor("#E6DB74"), QsciLexerPython.DoubleQuotedString)  # 黄色 - 字符串
        self.setPaper(bg_color, QsciLexerPython.DoubleQuotedString)

        self.setColor(QColor("#E6DB74"), QsciLexerPython.TripleSingleQuotedString)  # 黄色 - 三引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleSingleQuotedString)

        self.setColor(QColor("#E6DB74"), QsciLexerPython.TripleDoubleQuotedString)  # 黄色 - 三引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleDoubleQuotedString)

        # 设置其他样式
        self.setColor(QColor("#F92672"), QsciLexerPython.Operator)  # 红色 - 操作符
        self.setPaper(bg_color, QsciLexerPython.Operator)

        self.setColor(QColor("#E0E0E0"), QsciLexerPython.Identifier)  # 浅灰色 - 标识符
        self.setPaper(bg_color, QsciLexerPython.Identifier)

        self.setColor(QColor("#75715E"), QsciLexerPython.CommentBlock)  # 灰色 - 块注释
        self.setPaper(bg_color, QsciLexerPython.CommentBlock)

        self.setColor(QColor("#F92672"), QsciLexerPython.UnclosedString)  # 红色 - 未闭合字符串
        self.setPaper(bg_color, QsciLexerPython.UnclosedString)

        self.setColor(QColor("#FD971F"), QsciLexerPython.HighlightedIdentifier)  # 橙色 - 高亮标识符
        self.setPaper(bg_color, QsciLexerPython.HighlightedIdentifier)

        self.setColor(QColor("#66D9EF"), QsciLexerPython.Decorator)  # 青色 - 装饰器
        self.setPaper(bg_color, QsciLexerPython.Decorator)

        # 为默认样式设置背景色
        self.setPaper(bg_color, QsciLexerPython.Default)

    def _apply_eye_protect_green_theme_colors(self):
        """应用墨绿色护眼主题颜色"""
        # 设置默认背景色为深墨绿色
        bg_color = QColor("#1B2B1B")
        self.setDefaultPaper(bg_color)
        self.setDefaultColor(QColor("#E8F5E8"))  # 更亮的浅绿色作为默认文字

        # 设置各种样式的颜色和背景色（墨绿色主题 - 优化对比度）
        self.setColor(QColor("#F8BBD0"), QsciLexerPython.ClassName)  # 浅粉色 - 类名，醒目
        self.setPaper(bg_color, QsciLexerPython.ClassName)

        self.setColor(QColor("#81D4FA"), QsciLexerPython.Keyword)  # 浅蓝色 - 关键字，清晰对比
        self.setPaper(bg_color, QsciLexerPython.Keyword)

        self.setColor(QColor("#A5D6A7"), QsciLexerPython.Comment)  # 中等绿色 - 注释，保持护眼
        self.setPaper(bg_color, QsciLexerPython.Comment)

        self.setColor(QColor("#80CBC4"), QsciLexerPython.Number)  # 青绿色 - 数字，清新对比
        self.setPaper(bg_color, QsciLexerPython.Number)

        self.setColor(QColor("#FFD54F"), QsciLexerPython.FunctionMethodName)  # 金黄色 - 函数名，突出显示
        self.setPaper(bg_color, QsciLexerPython.FunctionMethodName)

        self.setColor(QColor("#C5E1A5"), QsciLexerPython.SingleQuotedString)  # 亮绿色 - 单引号字符串
        self.setPaper(bg_color, QsciLexerPython.SingleQuotedString)

        self.setColor(QColor("#C5E1A5"), QsciLexerPython.DoubleQuotedString)  # 亮绿色 - 双引号字符串
        self.setPaper(bg_color, QsciLexerPython.DoubleQuotedString)

        self.setColor(QColor("#C5E1A5"), QsciLexerPython.TripleSingleQuotedString)  # 亮绿色 - 三引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleSingleQuotedString)

        self.setColor(QColor("#C5E1A5"), QsciLexerPython.TripleDoubleQuotedString)  # 亮绿色 - 三双引号字符串
        self.setPaper(bg_color, QsciLexerPython.TripleDoubleQuotedString)

        self.setColor(QColor("#FFAB91"), QsciLexerPython.Operator)  # 浅橙色 - 操作符，温暖对比
        self.setPaper(bg_color, QsciLexerPython.Operator)

        self.setColor(QColor("#E8F5E8"), QsciLexerPython.Identifier)  # 浅绿白色 - 标识符，清晰可读
        self.setPaper(bg_color, QsciLexerPython.Identifier)

        self.setColor(QColor("#A5D6A7"), QsciLexerPython.CommentBlock)  # 中等绿色 - 注释块
        self.setPaper(bg_color, QsciLexerPython.CommentBlock)

        self.setColor(QColor("#FF8A65"), QsciLexerPython.UnclosedString)  # 橙色 - 未闭合字符串（错误提示）
        self.setPaper(bg_color, QsciLexerPython.UnclosedString)

        self.setColor(QColor("#90CAF9"), QsciLexerPython.HighlightedIdentifier)  # 天蓝色 - 高亮标识符
        self.setPaper(bg_color, QsciLexerPython.HighlightedIdentifier)

        self.setColor(QColor("#CE93D8"), QsciLexerPython.Decorator)  # 淡紫色 - 装饰器，优雅对比
        self.setPaper(bg_color, QsciLexerPython.Decorator)

        # 为默认样式设置背景色
        self.setPaper(bg_color, QsciLexerPython.Default)

    def refresh_theme(self):
        """刷新主题（供外部调用）"""
        self._apply_theme_colors()

