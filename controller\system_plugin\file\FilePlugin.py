# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction

from controller.file.ProjectHandler import ProjectHandler
from controller.file.Save import Save
from controller.system_plugin.SignalDistributor import SignalDistributor
from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from _functools import partial


class FilePlugin(object):

    _forward_items = []

    def __init__(self, parent):
        self._parent_window = parent
        self._signal_distributor = SignalDistributor()
        self._signal_distributor.all_item_save.connect(Save.save_all)

    def load(self):
        file_menu = self._parent_window.addMenu('&' + LanguageLoader().get('FILE'))
        self._add_item_new_project(file_menu)
        self._add_item_open_directory(file_menu)
        self._add_item_clear_history_project(file_menu)
        self._add_item_save(file_menu)
        self._add_item_save_all(file_menu)

    def _add_item_new_project(self, file_menu):
        new_project_action = QAction(QIcon(''), LanguageLoader().get("NEW_PROJECT"), self._parent_window)
        new_project_action.setShortcut(self._get_shortcut_key('NEW_PROJECT'))
        new_project_action.triggered.connect(partial(ProjectHandler.create_project, self))
        file_menu.addAction(new_project_action)

    def _add_item_open_directory(self, file_menu):
        open_directory_action = QAction(QIcon(''), LanguageLoader().get("OPEN_DIRECTORY"), self._parent_window)
        open_directory_action.setShortcut(self._get_shortcut_key('OPEN_DIRECTORY'))
        open_directory_action.triggered.connect(ProjectHandler.find_project)
        file_menu.addAction(open_directory_action)

    def _add_item_clear_history_project(self, file_menu):
        open_directory_action = QAction(QIcon(''), LanguageLoader().get("CLEAR_HISTORY_PROJECT"), self._parent_window)
        open_directory_action.setShortcut(self._get_shortcut_key('CLEAR_HISTORY_PROJECT'))
        open_directory_action.triggered.connect(ProjectHandler._clear_history_project)
        file_menu.addAction(open_directory_action)

    def _add_item_save(self, file_menu):
        save_action = QAction(QIcon(''), LanguageLoader().get("SAVE"), self._parent_window)
        save_action.setShortcut(self._get_shortcut_key('SAVE'))
        save_action.triggered.connect(Save.save)
        file_menu.addAction(save_action)

    def _add_item_save_all(self, file_menu):
        save_action = QAction(QIcon(''), LanguageLoader().get("SAVE_ALL"), self._parent_window)
        save_action.setShortcut(self._get_shortcut_key('SAVE_ALL'))
        save_action.triggered.connect(Save.save_all)
        file_menu.addAction(save_action)

    def get_icon(self):
        return Loader().get_relative_path('FILE')

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)
