# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import re, random
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiStu<PERSON>A<PERSON>
from controller.system_plugin.rf_assistant.RF_Helper.api.SiliconFlowAPI import SiliconFlowAPI
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3
TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'

class AiThread(QThread):
    rf_finished = pyqtSignal(str)
    caseInfoDic = {}

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

    def get_test_setting(self, case_info):
        text = ''
        keywordText = ''
        keywordsList = []
        if case_info.get('suite_setup'):
            text = text + 'Suite Setup    ' + case_info.get('suite_setup') + '\n'
            k = case_info.get('suite_setup').split(' ')[0]
            keywordsList.append(k)
        if case_info.get('suite_teardown'):
            text = text + 'Suite Teardown    ' + case_info.get('suite_teardown') + '\n'
            k = case_info.get('suite_teardown').split(' ')[0]
            keywordsList.append(k)
        for v in case_info.get('variables'):
            text = text + v + '\n'
        for r in case_info.get('resource'):
            text = text + r + '\n'
        for key in keywordsList:
            if case_info.get('suite_keywords_detail'):
                if case_info.get('suite_keywords_detail').get(key):
                    keywordText = keywordText + key + '\n'.join(case_info.get('suite_keywords_detail').get(key)) + '\n\n'
        return text, keywordText

    def get_sample_test_from_dnstudio(self, testcasePath, testcaseName, maxTerms=10):
        asa= AiStudioApi({'account': self.parent.account, "token": self.parent.token})
        ret = asa.get_similar_test('__'+ self.parent.project + '__' + testcasePath + '/' + testcaseName)
        print('-----get_sample_test_from_dnstudio-1092')
        print(ret['bo']['result'])
        testCase = ret['bo']['result']
        testCaseList = testCase.split('\n\n ')
        print('-----get_sample_test_from_dnstudio-1096')
        print(testCaseList)
        db = MongoDb3()
        similarTestDic = {}
        similarTestList = []
        firstCaseInfo = {}
        i = 0
        random_num = random.randint(1, 4)
        for test in testCaseList:
            i = i + 1
            if i > maxTerms:
                break
            tempDic = {}
            testInfo = test.split('__')
            if len(testInfo) > 4:
                case_id = testInfo[-1]
                case_info = db.query({'id': case_id}, TEST_CASE_INFO_DB_TABLE)
                if case_info:
                    if i == random_num:
                        firstCaseInfo = case_info[0]
                    if len(case_info[0].get('case_content')) < 4:
                        continue
                    tempDic['id0'] = case_info[0].get('id0')
                    tempDic['id'] = case_info[0].get('id')
                    text = ''
                    for c in case_info[0].get('case_content'):
                        text = text + c + '\n'
                    tempDic['script'] = text
                    testName, testStep, _ = self.parent.get_rdc_test_info(case_info[0].get('id0'))
                    print('-----5555')
                    print(case_id)
                    print(case_info[0])
                    tempDic['step'] = testStep
                    tempDic['name'] = testName
                    similarTestDic[case_id] = tempDic
                    similarTestList.append(case_id)
        print(similarTestDic.keys())
        return similarTestList, similarTestDic, firstCaseInfo

    def get_similar_test_from_db(self, sampleCaseIdList):
        db = MongoDb3()
        similarTestDic = {}
        similarTestList = []
        firstCaseInfo = None
        for case_id in sampleCaseIdList:
            tempDic = {}
            case_info = db.query({'id': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
            if not case_info:
                case_info = db.query({'id0': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
            if not firstCaseInfo:
                firstCaseInfo = case_info[0]
            if case_info:
                tempDic['id0'] = case_info[0].get('id0')
                tempDic['id'] = case_info[0].get('id')
                text = ''
                for c in case_info[0].get('case_content'):
                    text = text + c + '\n'
                tempDic['script'] = text
                print(case_info)
                testName, testStep, _ = self.parent.get_rdc_test_info(case_info[0].get('id0'))
                tempDic['step'] = testStep
                tempDic['name'] = testName
                similarTestDic[case_id] = tempDic
                similarTestList.append(case_id)
        return similarTestList, similarTestDic, firstCaseInfo

    def run(self):
        similarTestCaseText = ''
        similarTestCaseName = 'XXXXXXXXXXXXX'
        asa= AiStudioApi({'account': self.parent.account, "token": self.parent.token})
        # base_url = "https://api.siliconflow.cn/v1"
        # api_key = "sk-ihnegbftkynylxenlbbrxksdladnraavqdycbzfkzyddroyr"
        # model = "deepseek-ai/DeepSeek-V3"

        # base_url = "https://api.deepseek.com"
        # api_key = "***********************************"
        # model = "deepseek-chat"
        # sfa = SiliconFlowAPI(base_url, api_key, model)
        testcasePath = self.parent.testcasePath
        testcaseName = self.parent.testcaseNameEdit3.text()
        testcaseStep = self.parent.testcaseStepEdit3.toPlainText()
        if not testcaseName or not testcaseStep:
            self.rf_finished.emit('未查询到用例名称或者用例步骤信息！')
            return
        longText = False
        testId = self.parent.queryEdit3.text()
        text = self.parent.queryEdit31.text()
        scriptTestcaseName = testcaseName + '__' + testId if self.parent.project != 'SSP' else testId + ' ' + testcaseName
        if self.parent.devMode == '新手模式':
            maxTerms = 1
        else:
            maxTerms = 10
        if text:
            sampleCaseIdList = text.split(',')
            print('----1183')
            similarTestList, similarTestDic, firstCaseInfo = self.get_similar_test_from_db(sampleCaseIdList)
            if not similarTestList:
                print('----1186')
                similarTestList, similarTestDic, firstCaseInfo, filePathList = self.get_sample_test_from_dnstudio(testcasePath, testcaseName, maxTerms)
        else:
            print('----1187')
            similarTestList, similarTestDic, firstCaseInfo, filePathList = self.get_sample_test_from_dnstudio(testcasePath, testcaseName, maxTerms)
        print(self.parent.devMode)
        print(len(similarTestDic))
        if self.parent.devMode == '新手模式' and len(similarTestDic) > 0:
            case_id = similarTestDic[similarTestList[0]].get('id')
            db = MongoDb3()
            case_info = db.query({'id': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
            if case_info:
                case_info = case_info[0]
            similarTestCaseName = case_info.get('name', 'XXXXXXXXXXXXXX')
            testCaseText = self.parent.on_item_clicked(None, case_info)
            similarTestCaseText = testCaseText
            if len(testCaseText) > 30000:
                testCaseText = testCaseText[0: 30000]
            prompt = '''

__XY__指令:
## 角色扮演：
你是一名Robot Framework开发新手。
## 任务描述：
- 请完整无遗漏地在代码块里输出我提供的Robot Framework（RF）代码.
- 请将用例标题替换成：{1}。
## 强调：
1.请在Robot Framework代码块里输出代码.
## 代码：
以下是我提供的<Robot Framework代码>：
```Robot
{0}
```
'''.format(testCaseText, scriptTestcaseName)
        else:
            if len(similarTestDic) > 0:
                setting, keywordText = self.get_test_setting(firstCaseInfo)
                prompt = '''
    【背景和指令】：你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
    请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{2}'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
    【强调一】：尽量使用示例脚本里已经存在的关键字，你可以新增关键字，但是要给出其定义和描述。
    【强调二 】：以下内容作为脚本的*** settings ***，不要改变内容和格式：
    {0}
    【强调三】： 以下内容加入到*** Keywords ***里，这是已经定义好的关键字，不要改变内容和格式：
    {1}
    
    【用例步骤】：\n{3}
                '''.format(setting, keywordText, scriptTestcaseName, testcaseStep)
                i = 0
                for caseid in similarTestList:
                    i = i + 1
                    if i > 20:
                        break
                    temp = '''      
    【示例用例步骤】：\n{0}
    【示例脚本输出】：\n{1}
                    '''.format(similarTestDic[caseid]['step'], similarTestDic[caseid]['script'])
                    if len(prompt) + len(temp) < 31000:
                        prompt = prompt + temp
                    else:
                        break
            else:
                prompt = '''
    【背景和指令】：你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
    请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{0}'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
    【强调一】：尽量使用示例脚本里已经存在的关键字，你可以新增关键字，但是要给出其定义和描述。
    【用例步骤】：\n{1}
                '''.format(scriptTestcaseName, testcaseStep)
        indexModel = self.parent.llmComboBox.currentIndex()
        termprature = format(self.parent.sliderTempretrue.value() * 0.1, '.1f')
        MODEL_INFO = {0: 'nebulacoder', 1: 'Qwen-72B-Chat', 2: 'ZTEAIM-Saturn', 3: 'ZTEAIM-Venus-128k'}
        print('正在生成脚本，请稍后...')
        print(prompt)
#         if longText:
#             ret = asa.few_shot_rf_stream(prompt, MODEL_INFO.get(indexModel, 0), termprature, self.parent.stream_genarate_rf_call_back, longText)
#         else:
        ret = asa.few_shot_rf(prompt, MODEL_INFO.get(indexModel, 0), termprature)
        # ret = sfa.chat(prompt, self.parent.stream_genarate_rf_call_back)
        print(ret)
        listOutput = []
        if ret:
            pid = ret['bo']['docId']
            result = ret['bo']['result']
            if 'AuthFailed' in result:
                self.rf_finished.emit('AuthFailed')
                return
            if self.parent.devMode == '新手模式' and similarTestCaseText:
                result = self._restore_text(result, similarTestCaseText, similarTestCaseName, scriptTestcaseName)
            listRet = result.split('\n')
            listOutput = self.append_ai_code_id(listRet, pid)

        self.rf_finished.emit('\n'.join(listOutput))

    def _is_start_with_space(self, line):
        return re.search('^\s+', line) or re.search('^\\ ', line) or re.search('^\n', line) or re.search('^\r\n', line) or re.search('^\.\.\.', line)

    def _restore_text(self, result, similarTestCaseText, similarTestCaseName, tempTestcaseName):
        result = re.sub('```[rR]?obot\n[\s\S]+$', '```Robot\n\n' + similarTestCaseText + '\n```', result)
        print(result)
        print(similarTestCaseName)
        result = result.replace(similarTestCaseName, tempTestcaseName)
        return result
        
    def append_ai_code_id(self, listRet, pid):
        listOutput = []
        isPair = True
        testcaseBegin = 0
        switch = True
        for line in listRet:
            if 'by AICoder' in line or 'by AI-AutoRFTGen' in line:
                continue
            if line == '```':
                print('777')
                switch = False
            if re.search(r'\*\s?Test Cases\s?\*', line) or re.search(r'\*\s?Keywords\s?\*', line):
                if testcaseBegin != 0:
                    if not isPair:
                        listOutput = self._append_aicoder_end(listOutput, pid)
                        listOutput.append('\n\n')
                        isPair = True
                testcaseBegin = 1
                listOutput.append(line)
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 1:
                testcaseBegin = 2
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 2 and len(line) > 2:
                if not isPair:
                    listOutput = self._append_aicoder_end(listOutput, pid)
                    isPair = True
                if re.search(r'\*\s?Variables\s?\*', line) or re.search(r'\*\s?Settings\s?\*', line):
                    listOutput.append(line)
                    testcaseBegin = 0
                    continue
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            listOutput.append(line)
        if not isPair:
            listOutput = self._append_aicoder_end(listOutput, pid)
            isPair = True
        return listOutput

    def _append_aicoder_end(self, listOutput, pid):
        if 'Started by AICoder' in listOutput[-1]:
            return listOutput[0:-1]
        else:
            listOutput.append('    Comment    Ended by AICoder, pid:{0}'.format(pid))
            return listOutput