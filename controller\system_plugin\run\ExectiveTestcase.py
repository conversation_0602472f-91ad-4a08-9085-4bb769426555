# coding=utf-8
'''
Created on 2019年12月23日

@author: 10240349
'''
import os

from PyQt5.Qt import QTextEdit, Qt
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.ProjectTreeItemRepository import ProjectTreeItemRepository
from view.common.MessageBox import MessageBox
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.TestcaseItem import TestcaseItem
CHECKED = 2


class ExectiveTestcase():

    def set(self):
        checked_testcases = self.get()
        self._set_exective_testcases(checked_testcases)

    def get(self):
        root_item = ProjectTreeItemRepository().find("PROJECT_TREE_ITEM")
        if root_item:
            return self._query(root_item)
        else:
            MessageBox().show_warning(LanguageLoader().get("NO_PROJECT"))

    def _set_exective_testcases(self, testcases):
        self._clear_exective_testcases()
        if testcases:
            for t in testcases:
                testcases = [t.parent().get_path(), t.get_name()]
                ExecutiveTestCaseRepository().append('EXECUTIVE_TESTCASES', testcases)
                ExecutiveTestCaseRepository().append('EXECUTIVE_TESTCASES_ITEM',
                                                     {os.path.splitext(t.parent().get_path())[0] + os.path.sep + t.get_name(): t})

    def _clear_exective_testcases(self):
        ExecutiveTestCaseRepository().delete('EXECUTIVE_TESTCASES')
        ExecutiveTestCaseRepository().delete('EXECUTIVE_TESTCASES_ITEM')

    def _query(self, item):
        checked_testcases = []
        if self._is_parse(item):
            for i in range(item.childCount()):
                child = item.child(i)
                checked_testcases = self._parse_child_item(child, checked_testcases)
        return checked_testcases

    def _parse_child_item(self, child, _list):
        if isinstance(child, ProjectTreeItem):
            if self._is_parse(child):
                _list += self._query(child)
        elif isinstance(child, SuiteItem):
            _list += self._parse_suite_item(child)
        return _list

    def _parse_suite_item(self, item):
        _list = []
        if self._is_parse(item):
            for i in range(item.childCount()):
                child = item.child(i)
                if isinstance(child, TestcaseItem) and child.checkState(0) == CHECKED:
                    _list.append(child)
        return _list

    def _is_parse(self, child):
        if child.childCount():
            return True
        else:
            return False

    def show(self, parent):
        parent.dialog = TestcasesDialog()
        parent.dialog.show()


class TestcasesDialog(QWidget):

    def __init__(self):
        super().__init__()
        self._set_ui()
        self._init_ui()

    def _set_ui(self):
        self.setWindowTitle('details')
        self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

    def _init_ui(self):
        self.resize(SystemSettings().get_value('DETAILS_DIALOG_WIDTH'),
                    SystemSettings().get_value('DETAILS_DIALOG_HEIGHT'))
        vbox = QVBoxLayout()
        vbox.addLayout(self._set_line_area())
        self.setLayout(vbox)

    def _set_line_area(self):
        vbox = QVBoxLayout()
        label = QLabel(LanguageLoader().get('DEATILS_TIP'))
        text_edit = QTextEdit()
        self._set_content(text_edit)
        vbox.addWidget(label)
        vbox.addWidget(text_edit)
        return vbox

    def _set_content(self, text_edit):
        testcases = ExectiveTestcase().get()
        for t in testcases:
            text_edit.append(t.get_path() + '\n')


class ticked_testcases_querier(object):

    @staticmethod
    def untick():
        checked_testcases = ExectiveTestcase().get()
        for testcase in checked_testcases:
            testcase.set_unchecked(testcase)
