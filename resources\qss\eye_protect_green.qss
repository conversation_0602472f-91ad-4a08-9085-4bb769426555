/* 酷炫护眼·墨绿色主题样式文件 */

/* 全局样式 */
QWidget {
    color: #C8E6C9;
    background-color: #1B2B1B;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
}

QMainWindow {
    background-color: #1B2B1B;
    color: #C8E6C9;
}

/* 菜单栏 */
QMenuBar {
    background-color: #223322;
    color: #C8E6C9;
    border-bottom: 1px solid #336633;
}
QMenuBar::item {
    background: transparent;
    padding: 4px 8px;
}
QMenuBar::item:selected {
    background-color: #388E3C;
    color: #FFFFFF;
}
QMenuBar::item:pressed {
    background-color: #2E7D32;
    color: #FFFFFF;
}

/* 菜单 */
QMenu {
    background-color: #223322;
    color: #C8E6C9;
    border: 1px solid #336633;
}
QMenu::item {
    padding: 6px 20px;
    background: transparent;
}
QMenu::item:selected {
    background-color: #388E3C;
    color: #FFFFFF;
}
QMenu::separator {
    height: 1px;
    background-color: #336633;
    margin: 2px 0;
}

/* 工具栏 */
QToolBar {
    background-color: #223322;
    border: none;
    spacing: 2px;
}
QToolBar::handle {
    background-color: #336633;
    width: 2px;
    margin: 2px;
}

/* 按钮 */
QPushButton {
    background-color: #2E7D32;
    color: #C8E6C9;
    border: 1px solid #336633;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 60px;
}
QPushButton:hover {
    background-color: #388E3C;
    border-color: #388E3C;
    color: #FFFFFF;
}
QPushButton:pressed {
    background-color: #1B5E20;
    border-color: #1B5E20;
}
QPushButton:disabled {
    background-color: #223322;
    color: #557755;
    border-color: #223322;
}

/* 编辑页签Clear按钮 */
QPushButton[name='clear_button'] {
    border-width: 1px;
    border-color: #336633;
    border-style: solid;
    border-radius: 6;
    padding: 3px;
    font-size: 12px;
    padding-left: 5px;
    padding-right: 5px;
    min-width: 40px;
    background-color: #2E7D32;
    color: #C8E6C9;
}
QPushButton[name='clear_button']:hover {
    color: #388E3C;
    background-color: #233D23;
    border: 1px solid #388E3C;
}
QPushButton[name='clear_button']:pressed {
    background-color: #1B5E20;
    border: 1px solid #1B5E20;
    padding-left:3px;
    padding-top:3px;
}

/* 特殊按钮样式 */
QPushButton[name='primary'] {
    background-color: #388E3C;
    color: #FFFFFF;
    border-color: #2E7D32;
}
QPushButton[name='primary']:hover {
    background-color: #2E7D32;
    border-color: #1B5E20;
}
QPushButton[name='succ'] {
    background-color: #43A047;
    color: #FFFFFF;
    border-color: #388E3C;
}
QPushButton[name='succ']:hover {
    background-color: #388E3C;
    border-color: #2E7D32;
}
QPushButton[name='warn'] {
    background-color: #F0AD4E;
    color: #FFFFFF;
    border-color: #EEA236;
}
QPushButton[name='warn']:hover {
    background-color: #EEA236;
    border-color: #E6982A;
}
QPushButton[name='danger'] {
    background-color: #D9534F;
    color: #FFFFFF;
    border-color: #D43F3A;
}
QPushButton[name='danger']:hover {
    background-color: #D43F3A;
    border-color: #C9302C;
}

/* 输入框 */
QLineEdit, QTextEdit {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致的深墨绿色 */
    color: #E8F5E8;  /* 更亮的浅绿色文字，提高对比度 */
    border: 1px solid #336633;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
}
QLineEdit:focus, QTextEdit:focus {
    border-color: #4CAF50;  /* 焦点时使用更亮的绿色边框 */
}

/* 标签 */
QLabel {
    color: #C8E6C9;
    background-color: transparent;
    font-size: 14px;
}

/* 选项卡 */
QTabWidget::pane {
    border: 1px solid #336633;
    background-color: #223322;
}
QTabBar::tab {
    background-color: #2E7D32;
    color: #C8E6C9;
    border: 1px solid #336633;
    border-bottom: none;
    padding: 8px 16px;
    margin-right: 2px;
}
QTabBar::tab:selected {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致 */
    border-bottom: 1px solid #1B2B1B;
}
QTabBar::tab:hover:!selected {
    background-color: #388E3C;
}

/* 滚动条 */
QScrollBar:vertical {
    background: #223322;
    width: 12px;
    margin: 0px 0px 0px 0px;
    border: 1px solid #336633;
    border-radius: 6px;
}
QScrollBar::handle:vertical {
    background: #388E3C;
    min-height: 20px;
    border-radius: 6px;
}
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    background: #2E7D32;
    height: 0px;
    subcontrol-origin: margin;
}
QScrollBar:horizontal {
    background: #223322;
    height: 12px;
    margin: 0px 0px 0px 0px;
    border: 1px solid #336633;
    border-radius: 6px;
}
QScrollBar::handle:horizontal {
    background: #388E3C;
    min-width: 20px;
    border-radius: 6px;
}
QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    background: #2E7D32;
    width: 0px;
    subcontrol-origin: margin;
}

/* 表格 */
QTableWidget, QTableView {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致 */
    color: #E8F5E8;  /* 更亮的浅绿色文字 */
    gridline-color: #336633;
    selection-background-color: #388E3C;
    selection-color: #FFFFFF;
    border: 1px solid #336633;
    alternate-background-color: #1E2B1E;  /* 稍微调整交替行颜色 */
}

/* 编辑区域空间 */
QPlainTextEdit, QTextEdit {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致的深墨绿色 */
    color: #E8F5E8;  /* 更亮的浅绿色文字，提高对比度 */
    selection-background-color: #388E3C;
    selection-color: #FFFFFF;
}
QHeaderView::section {
    background-color: #223322;
    color: #A5D6A7;
    border: 1px solid #336633;
    padding: 4px;
}

/* 列表 */
QListWidget, QTreeWidget {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致 */
    color: #E8F5E8;  /* 更亮的浅绿色文字 */
    border: 1px solid #336633;
    border-radius: 4px;
    selection-background-color: #388E3C;
    selection-color: #FFFFFF;
}

/* 复选框/单选框 */
QCheckBox, QRadioButton {
    color: #C8E6C9;
    spacing: 8px;
}

/* 分组框 */
QGroupBox {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致 */
    color: #E8F5E8;  /* 更亮的浅绿色文字 */
    border: 1px solid #336633;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 15px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 3px;
    color: #A5D6A7;
}

/* 提示/弹窗 */
QDialog {
    background-color: #1B2B1B;  /* 与编辑器背景保持一致 */
    color: #E8F5E8;  /* 更亮的浅绿色文字 */
    border: 1px solid #336633;
    border-radius: 8px;
}

/* 状态栏 */
QStatusBar {
    background: #223322;
    color: #A5D6A7;
    border-top: 1px solid #336633;
}

/* 编辑器行号区域 */
QWidget#editor_margin {
    background-color: #223322;
    color: #81C784;
}

/* 编辑器当前行高亮 */
QWidget#editor_current_line {
    background-color: #223322;
}

/* 代码折叠区域 */
QWidget#editor_fold_margin {
    background-color: #223322;
    color: #388E3C;
}
