# coding=utf-8
'''
Created on 2019年11月07日

@author: 10240349
'''
import re


class TextToHtml(object):

    FONT = 'font'
    BR = 'br'
    HREF = 'href'
    B = 'b'

    def __init__(self, text):
        self._text = text

    def get_text(self):
        return self._text

    def set_format(self, keys, tag, styles='color="red"'):
        tag = r'<' + tag + r' {0}' + r'>\g<1></' + tag + '>'
        pattern = r'(' + "|".join(keys) + r')'
        self._text = re.sub(pattern, tag.format(styles), self._text)


class LogHighLighter(object):

    def __init__(self, text):
        self._text = text
        self._highlight()

    def _highlight(self):
        t = TextToHtml(self._text)
        t.set_format([r'".[^"]*"|\'.[^\']*\''],TextToHtml.FONT, 'color="#606266"')
        t.set_format([r'error', r'ERROR'], TextToHtml.FONT, 'color="#F56C6C"')
        t.set_format([r'error', r'ERROR'], TextToHtml.B, '')
        t.set_format([r'fail$', r'FAIL'], TextToHtml.FONT, 'color="#F56C6C"')
        t.set_format([r'fail$', r'FAIL'], TextToHtml.B, '')
        t.set_format([r'warn$', r'WARN'], TextToHtml.FONT, 'color="#F56C6C"')
#         t.set_format([r'\r\n', r'\n'], TextToHtml.BR, '')
        t.set_format([r'\r\n', r'\n'], TextToHtml.BR, '')
        self._text = t.get_text()

    def get_text(self):
        return self._text


if __name__ =="__main__":
    text = '''error 你好 ERROR 'nihaodsfdsfsdf' 'sdfdsfdsfsdfdsfsdf' "sdfdsf34324r324"'''
    tag = ''
    t = TextToHtml(text)
    t.set_format([r'".[^"]*"|\'.[^\']*\''],TextToHtml.FONT, 'color="#606266"')
    t.set_format([r'error', r'ERROR'], TextToHtml.FONT, 'color="#F56C6C"')
    t.set_format([r'error', r'ERROR'], TextToHtml.B, '')
    t.set_format([r'fail$', r'FAIL'], TextToHtml.FONT, 'color="#F56C6C"')
    t.set_format([r'fail$', r'FAIL'], TextToHtml.B, '')
    t.set_format([r'warn$', r'WARN'], TextToHtml.FONT, 'color="#F56C6C"')
    t.set_format([r'\r\n', r'\n'], TextToHtml.BR, '')
    print(t.get_text())