# encoding=utf-8
'''
Created on 2020年1月6日

@author: 10247557/10240349
'''
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QTreeWidget, \
    QTreeWidgetItem, QStackedWidget, QStackedLayout

from controller.system_plugin.tools.item.Preferences.Drawer import Drawer
from settings.SystemSettings import SystemSettings


class Preferences(QWidget):
    def __init__(self, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowStaysOnTopHint)
        
        # 窗口缩放相关属性
        self._resize_border = 5
        self._resize_direction = None
        self._mouse_pressed = False
        
        self._init_ui()
        self.set_theme()
        
        # 监听主题变化
        from controller.system_plugin.style.ThemeManager import ThemeManager
        ThemeManager().theme_changed.connect(self._on_theme_changed)

    def set_theme(self, theme_id=None):
        """应用主题样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors(theme_id)
            
            # 应用主题样式到Preferences窗口
            self.setStyleSheet(f"""
                QWidget {{
                    background-color: {colors['dialog_bg']};
                    color: {colors['dialog_text']};
                }}
                QTreeWidget {{
                    background-color: {colors['dialog_input_bg']};
                    color: {colors['dialog_input_text']};
                    border: 1px solid {colors['dialog_border']};
                    border-radius: 4px;
                }}
                QTreeWidget::item:selected {{
                    background-color: {colors['editor_selection_bg']};
                    color: {colors['editor_selection_text']};
                }}
            """)
            
            # 刷新所有子控件
            for child in self.findChildren(QWidget):
                child.setStyleSheet("")
                
        except Exception as e:
            print(f"应用偏好设置主题失败: {e}")

    def _init_ui(self):
        self.resize(SystemSettings().get_value('PREFERENCES_DIALOG_WIDTH'),
                    SystemSettings().get_value('PREFERENCES_DIALOG_HEIGHT'))
        self._set_layout()

    def _load(self):
        pass

    def _set_layout(self):
        from PyQt5.QtWidgets import QVBoxLayout, QSplitter
        from PyQt5.QtGui import QIcon
        
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # 使用分割器使左右区域可调整
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧树形菜单
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(5, 5, 5, 5)
        self.tree = QTreeWidget()
        self._set_style()
        
        # 添加图标
        icon = QIcon(":/icons/preferences.png")
        for item in (self._add_language_item(), self._add_trace_log_item(), self._add_theme_item()):
            item.setIcon(0, icon)
            self.tree.addTopLevelItem(item)
            
        left_layout.addWidget(self.tree)
        left_widget.setLayout(left_layout)
        
        # 右侧内容区域
        right_widget = self._generate_widget()
        right_widget.setContentsMargins(10, 0, 0, 0)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setStretchFactor(1, 2)  # 右侧区域初始占2/3
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)

    def _get_tree_layout(self):
        hbox = QHBoxLayout()
        self.tree = QTreeWidget()
        self._set_style()
        for item in (self._add_language_item(), self._add_trace_log_item(), self._add_theme_item()):
            self.tree.addTopLevelItem(item)
        hbox.addWidget(self.tree)
        return hbox

    def _set_style(self):
        self.tree.header().hide()
        self.tree.clicked.connect(self._show_page)
        self.tree.setStyleSheet("""
            QTreeWidget {
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
            }
            QTreeWidget::item {
                height: 30px;
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #4A90E2;
                color: white;
            }
        """)

    def _add_language_item(self):
        item = QTreeWidgetItem(self.tree)
        item.setText(0, 'Language')
        return item

    def _generate_language_widget(self):
        widget = QWidget()
        layout = Drawer().get_layout('language')
        widget.setLayout(layout)
        return widget

    def _add_trace_log_item(self):
        item = QTreeWidgetItem(self.tree)
        item.setText(0, 'Trace_Log')
        return item

    def _add_theme_item(self):
        item = QTreeWidgetItem(self.tree)
        item.setText(0, 'Theme')
        return item

    def _generate_trace_log_widget(self):
        widget = QWidget()
        layout = Drawer().get_layout('Trace_Log')
        widget.setLayout(layout)
        return widget

    def _generate_theme_widget(self):
        widget = QWidget()
        drawer = Drawer()
        drawer._dialog = self  # 传递当前对话框引用
        layout = drawer.get_layout('Theme')
        widget.setLayout(layout)
        return widget

    def _show_page(self, index):
        item = self.tree.currentItem()
        if item.text(0) == 'Language':
            self._stacked_widget.setCurrentIndex(0)
        elif item.text(0) == 'Trace_Log':
            self._stacked_widget.setCurrentIndex(1)
        elif item.text(0) == 'Theme':
            self._stacked_widget.setCurrentIndex(2)

    def _on_theme_changed(self, theme_id):
        """主题变化事件处理"""
        self.set_theme(theme_id)

    def _generate_widget(self):
        widget = QWidget()
        self._stacked_widget = QStackedWidget(widget)
        self._stacked_widget.addWidget(self._generate_language_widget())
        self._stacked_widget.addWidget(self._generate_trace_log_widget())
        self._stacked_widget.addWidget(self._generate_theme_widget())
        return widget

    def mousePressEvent(self, event):
        """鼠标按下事件处理"""
        if event.button() == Qt.LeftButton:
            self._mouse_pressed = True
            self._resize_direction = self._get_resize_direction(event.pos())
            self._drag_start_pos = event.pos()
            self._drag_start_geometry = self.geometry()

    def mouseMoveEvent(self, event):
        """鼠标移动事件处理"""
        if not self._mouse_pressed:
            # 改变鼠标光标形状
            direction = self._get_resize_direction(event.pos())
            if direction == 'left' or direction == 'right':
                self.setCursor(Qt.SizeHorCursor)
            elif direction == 'top' or direction == 'bottom':
                self.setCursor(Qt.SizeVerCursor)
            elif direction in ('top-left', 'bottom-right'):
                self.setCursor(Qt.SizeFDiagCursor)
            elif direction in ('top-right', 'bottom-left'):
                self.setCursor(Qt.SizeBDiagCursor)
            else:
                self.setCursor(Qt.ArrowCursor)
        else:
            # 执行窗口缩放
            if self._resize_direction:
                rect = self._drag_start_geometry
                pos = event.pos()
                
                if 'left' in self._resize_direction:
                    rect.setLeft(rect.left() + pos.x() - self._drag_start_pos.x())
                if 'right' in self._resize_direction:
                    rect.setRight(rect.right() + pos.x() - self._drag_start_pos.x())
                if 'top' in self._resize_direction:
                    rect.setTop(rect.top() + pos.y() - self._drag_start_pos.y())
                if 'bottom' in self._resize_direction:
                    rect.setBottom(rect.bottom() + pos.y() - self._drag_start_pos.y())
                
                self.setGeometry(rect.normalized())

    def mouseReleaseEvent(self, event):
        """鼠标释放事件处理"""
        self._mouse_pressed = False
        self.setCursor(Qt.ArrowCursor)

    def _get_resize_direction(self, pos):
        """获取当前鼠标位置对应的缩放方向"""
        width = self.width()
        height = self.height()
        
        if pos.x() <= self._resize_border:
            if pos.y() <= self._resize_border:
                return 'top-left'
            elif pos.y() >= height - self._resize_border:
                return 'bottom-left'
            else:
                return 'left'
        elif pos.x() >= width - self._resize_border:
            if pos.y() <= self._resize_border:
                return 'top-right'
            elif pos.y() >= height - self._resize_border:
                return 'bottom-right'
            else:
                return 'right'
        elif pos.y() <= self._resize_border:
            return 'top'
        elif pos.y() >= height - self._resize_border:
            return 'bottom'
        else:
            return None
