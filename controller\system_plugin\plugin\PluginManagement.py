# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''
from _functools import partial

from PyQt5.Qt import QIcon
from PyQt5.QtWidgets import QAction

from iplatform.Core import Core
from settings.i18n.Loader import LanguageLoader
from controller.system_plugin.SignalDistributor import SignalDistributor
from utility.PluginRepository import PluginRepository
from settings.SystemSettings import SystemSettings


class PluginManagement(object):
    def __init__(self, parent):
        self._parent_window = parent
        self._plugins = []
        self._load_plugins()
        PluginRepository().add('PluginManagement', self)

    def load(self):
        plugin_menu = self._parent_window.addMenu('&' + LanguageLoader().get('PLUGIN'))
        self._add_rf_assistant_action(plugin_menu)

    def _add_rf_assistant_action(self, plugin_menu):
        self.rf_assistant_action = QAction(LanguageLoader().get('RF_ASSISTANT'), self._parent_window)
        self.rf_assistant_action.setCheckable(True)
        self.rf_assistant_action.triggered.connect(self._toggle_rf_assistant)
        plugin_menu.addAction(self.rf_assistant_action)
        if SystemSettings().read('IS_LOAD_RF_ASSISTANT') == 'true' or SystemSettings().read('IS_LOAD_RF_ASSISTANT') == 'True' or SystemSettings().read('IS_LOAD_RF_ASSISTANT') == True:
            self.rf_assistant_action.setChecked(True)
        else:
            self.rf_assistant_action.setChecked(False)

    def _toggle_rf_assistant(self, checked):
        if checked:
            SignalDistributor().rf_assistant_load.emit()
            SystemSettings().write('IS_LOAD_RF_ASSISTANT', 'true')
        else:
            SignalDistributor().rf_assistant_unload.emit()
            SystemSettings().write('IS_LOAD_RF_ASSISTANT', 'false')
        print(SystemSettings().read('IS_LOAD_RF_ASSISTANT'))

    def _add_item_open_directory(self, plugin_menu):
        open_directory_action = QAction(QIcon(''), '&Help', self._parent_window)
        open_directory_action.triggered.connect(partial(self._select_plugins, 'help'))
        plugin_menu.addAction(open_directory_action)

    def _load_plugins(self):
        self._core = Core()
        plugin_list = self._core.get_plugins()
#         print(plugin_list)

    @staticmethod
    def _select_plugins(name):
        Core().load('help')
#         for name in names:
