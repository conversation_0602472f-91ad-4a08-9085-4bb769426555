# coding=utf-8
'''
Created on 2019年12月17日

@author: 10240349
'''
import os
import sys

from PyQt5.Qt import QIcon, QThread, pyqtSignal, QCursor, Qt
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, \
    QPushButton, QLabel, QComboBox, QTableWidget, QAbstractItemView, QHeaderView, \
    QTableWidgetItem, QApplication

from controller.system_plugin.tools.search_dialog.ConfigLibDialog import ConfigLibDialog
from controller.system_plugin.tools.search_dialog.LoadingBar import TestcasesLoadingBar
from controller.system_plugin.tools.search_dialog.MyLineEdit import MyLineEdit
from model.searcher.TestCase import TestCase
from resources.Loader import Loader
from settings.HistoryProject import HistoryProject
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.FileHandler import TestcasesFileCounter
from view.common.MessageBox import MessageBox
from view.explorer.tree_item.SpecifiedKeywordJumper import Specified<PERSON><PERSON><PERSON><PERSON>umper
from view.explorer.tree_item.TestcaseItem import TestcaseItem


SEARCH_LINE_WIDTH = 300
SEARCH_BTN = 100
TESTCASE_HEADER_WIDTH = 300


class Testcases(QWidget):

    thread_obj = None
    loading_bar_obj = None

    def __init__(self, title, logo_path):
        super().__init__()
        self._set_ui(title, logo_path)
        self._init_ui()

    def _set_ui(self, title, logo_path):
        self._first_column_width = TESTCASE_HEADER_WIDTH
        self.setWindowTitle(title)
        self.setWindowIcon(QIcon(logo_path))
#         self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

    def _init_ui(self):
        self.resize(SystemSettings().get_value('SEARCH_KEYWORDS_DIALOG_WIDTH'),
                    SystemSettings().get_value('SEARCH_KEYWORDS_DIALOG_HEIGHT'))
        vbox = QVBoxLayout()
        for box in (self._set_line_area(), self._set_list_area()):
            vbox.addLayout(box)
        self.setLayout(vbox)
        self._focus_cursor()

    def _focus_cursor(self):
        self._search_line.setCursorPosition(2)
        self._search_line.setFocus()

    def _set_line_area(self):
        hbox = QHBoxLayout()
        hbox = self._set_widget(hbox)
        hbox.setSpacing(10)
        hbox.addStretch() # 最后一个控件添加伸缩，就会左对齐
        return hbox

    def _set_widget(self, hbox):
        self._search_line = self._set_search_line()
        search_btn = self._set_search_btn()
        _label = self._set_source_label()
        self._keyword_lib = self._set_combox()
        config_btn = self._set_config_btn()
        for c in (self._search_line, search_btn, _label, self._keyword_lib, config_btn):
            hbox.addWidget(c)
        return hbox

    def _set_search_line(self):
        line = MyLineEdit()
        line.enter_pressed.connect(self._search_testcases)
        line.setMaximumWidth(SEARCH_LINE_WIDTH)
        return line

    def _set_search_btn(self):
        search_btn = QPushButton('Search')
        search_btn.setCursor(QCursor(Qt.PointingHandCursor))
        search_btn.setMaximumWidth(SEARCH_BTN)
        search_btn.clicked.connect(self._search_testcases)
        return search_btn

    def _set_source_label(self):
        label = QLabel('Source: ')
        return label

    def _set_combox(self):
        self._combox = QComboBox(self)
        if HistoryProject.read('PROJECT_PATH'):
            self._source_lib = ['testcases']
        else:
            self._source_lib = []
        _dict = SystemSettings().read("SOURCE_TESTCASE_LIB")
        if _dict:
            self._source_lib += _dict.keys()
        self._combox.addItems(self._source_lib)
        return self._combox

    def _set_config_btn(self):
        config_btn = QPushButton('Configure')
        config_btn.setCursor(QCursor(Qt.PointingHandCursor))
        config_btn.clicked.connect(self._open_config_dialog)
        return config_btn

    def _set_list_area(self):
        vbox = QVBoxLayout()
        self._table = QTableWidget()
        self._data = []
        self._set_table_style(len(self._data))
        self._set_data_in_table(self._data)
        self._table.itemDoubleClicked.connect(self._check_item)
        vbox.addWidget(self._table)
        return vbox

    def _set_table_style(self, length):
        self._table.setColumnCount(2)
        self._table.setRowCount(length)
        self._table.verticalHeader().setHidden(True)
        self._table.setColumnWidth(0, TESTCASE_HEADER_WIDTH)
        self._table.setHorizontalHeaderLabels([LanguageLoader().get('TESTCASE_NAME'),
                                               LanguageLoader().get('LIB_PATH')])
        self._table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self._table.setSelectionMode(QAbstractItemView.SingleSelection)
        self._table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self._table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Interactive)
        self._table.setEditTriggers(QAbstractItemView.NoEditTriggers)

    def _set_data_in_table(self, data):
        for row in range(len(data)):
            for column in range(len(data[row])):
                self._table.setItem(row, column, QTableWidgetItem(data[row][column]))

    def _search_testcases(self):
        self._first_column_width = self._table.columnWidth(0)
        _dict = SystemSettings().read("SOURCE_TESTCASE_LIB")
        if self._combox.currentText() == 'testcases' and not self._check_rfcode_file('TESTCASES'):
            MessageBox().show_warning(LanguageLoader().get('SYSTEM_LIB_TIP'))
        else:
            self._kill_old_thread()
            self._dt = SearchThread(_dict, self._combox.currentText(), self._search_line.text())
            Testcases.thread_obj = self._dt
            self._kill_old_loading_bar()
            self.loading_bar = TestcasesLoadingBar(self._dt.get_file_counter())
            Testcases.loading_bar_obj = self.loading_bar
            self._dt.search_finished.connect(self._reload_testcases)
            self._dt.start()
            self.loading_bar.run()

    def _kill_old_thread(self):
        if Testcases.thread_obj:
            Testcases.thread_obj.terminate()

    def _kill_old_loading_bar(self):
        if Testcases.loading_bar_obj:
            Testcases.loading_bar_obj = None

    def _reload_testcases(self, _list):
        self.loading_bar.destroy()
        if not _list:
            MessageBox().show_warning(LanguageLoader().get('SEARCH_RESULT'))
        self._data = _list
        self._set_table_style(len(self._data))
        self._table.clearContents()
        self._table.setColumnWidth(0, self._first_column_width)
        self._set_data_in_table(self._data)

    def _check_item(self):
        name = self._table.selectedItems()[0].text()
        path = self._table.selectedItems()[1].text()
        SpecifiedKeywordJumper().get_keyword_item(path, name.strip(), TestcaseItem) # 用例跳转

    def _open_config_dialog(self):
        self._config_dialog = ConfigLibDialog('SOURCE_TESTCASE_LIB')
        self._config_dialog.show()
        self._config_dialog.ok_pressed.connect(self._update_source_lib)

    def _update_source_lib(self):
        _dict = SystemSettings().read("SOURCE_TESTCASE_LIB")
        if _dict:
            self._source_lib = list(_dict.keys())
            self._source_lib.append('testcases')
            self._combox.clear()
            self._combox.addItems(self._source_lib)

    def _check_rfcode_file(self, key):
        config_file = HistoryProject().read('PROJECT_PATH') + os.path.sep + '.rfcode'
        with open(config_file, 'r', encoding = 'utf8') as f:
            content = f.read()
            if content:
                _dict = eval(content)
                if key in _dict:
                    return True
                else:
                    return False


class SearchThread(QThread):

    search_finished = pyqtSignal(list)

    def __init__(self, _dict, combox_text, search_line_text):
        super().__init__()
        self._dict = _dict
        self._search_line_text = search_line_text
        self._combox_text = combox_text
        self._path = self._get_path()
        self._fc = TestcasesFileCounter(self._path)

    def _get_path(self):
        path = ''
        if self._combox_text == 'testcases':
            self._project_path = HistoryProject().read('PROJECT_PATH')
            if self._project_path and self._get_system_lib('TESTCASES'):
                path = self._project_path + os.path.sep + self._get_system_lib('TESTCASES')
        else:
            path = self._dict[self._combox_text]
        return path

    def run(self):
        _list = TestCase(self._path, self._fc).search(self._search_line_text)
        self.search_finished.emit(_list)

    def get_file_counter(self):
        return self._fc

    def _get_system_lib(self, key):
        config_file = self._project_path + os.path.sep + '.rfcode'
        with open(config_file, 'r', encoding = 'utf8') as f:
            content = f.read()
            if content:
                _dict = eval(content)
                if key in _dict:
                    return _dict[key]


def except_hook(cls, exception, traceback):
    sys.__excepthook__(cls, exception, traceback)


if __name__ == '__main__':
    import cgitb
    cgitb.enable(format = 'text')
    sys.excepthook = except_hook
    app = QApplication(sys.argv)
    ui = Testcases('dialog', Loader().get_path('SEARCH'))
    ui.show()
#     ui._get_system_lib('testcases')
    sys.exit(app.exec_())
