'''
Created on 2019年11月25日

@author: 10247557
'''
DOCUMENTATION = 'documentation'
ARGUMENTS = 'arguments'
TEARDOWN = 'teardown'
TIMEOUT = 'timeout'
RETURN = 'return'
TAGS = 'tags'

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParser import Item<PERSON>arser
from model.CurrentItem import CurrentItem
from utility.Singleton import Singleton
from model.ProjectTree import ProjectTree
from model.data_file.KeyWords import KeyWord


@Singleton
class UserKeywordItemParser(ItemParser):

    def query(self, item):
        self._item = item
        if self._item._data_file is None:
            self._content = {}
        else:
            self._content = self._item._data_file.query()
            self._get_documentation()
            self._get_arguments()
            self._get_teardomn()
            self._get_timeout()
            self._get_return_value()
            self._get_table()

    def requery(self):
        self.query(self._item)

    def get_cur_data_file(self, item):
        return item.parent()._data_file if not isinstance(item.parent()._data_file, ProjectTree) else item.parent()._data_file.init_file

    def _get_documentation(self):
        self.documentation = self._content.documentation if hasattr(self._content, DOCUMENTATION) else None

    def _get_arguments(self):
        self.arguments = self._content.arguments if hasattr(self._content, ARGUMENTS) else None

    def _get_teardomn(self):
        self.teardown = self._content.teardown if hasattr(self._content, TEARDOWN) else None

    def _get_timeout(self):
        self.timeout = self._content.timeout if hasattr(self._content, TIMEOUT) else None

    def _get_return_value(self):
        self.return_value = getattr(self._content, RETURN) if hasattr(self._content, RETURN) else None

    def _get_tags(self):
        self.tags = self._content.tags if hasattr(self._content, TAGS) else None

    def _get_table(self):
        if isinstance(self._content, KeyWord):
            self.table = self._content.body

    def modify(self, area_type, content):
        # 检查_content是否有modify方法
        if hasattr(self._content, 'modify'):
            if area_type == 'return value':
                self._content.modify('return', content)
            else:
                self._content.modify(area_type, content)
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'type': 'other', 'operator': 'modify'})
        else:
            print(f"警告：_content对象没有modify方法，类型为: {type(self._content)}")
            # 如果_content是字典，尝试直接更新
            if isinstance(self._content, dict):
                if area_type == 'return value':
                    self._content['return'] = content
                else:
                    self._content[area_type] = content
                print(f"已直接更新字典内容: {area_type}")
                SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'type': 'other', 'operator': 'modify'})
            else:
                print(f"无法修改_content，不支持的类型: {type(self._content)}")
