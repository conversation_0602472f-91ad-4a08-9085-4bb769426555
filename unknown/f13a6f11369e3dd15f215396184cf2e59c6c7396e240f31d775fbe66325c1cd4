import os
import re

from settings.LogProject import LogProject
from utility.LogPathRepository import LogPathRepository
from utility.timer import get_date_time


class RealTimeXml(object):

    def __init__(self):
        self._start_list = ['<robot>']
        self._end_list = []
        self._error_list = []
        self._suite_id = None
        self._test_id = None
        self._is_add_test = False
        self._has_set_tear = False
        self._set_tear_name = ''
        self._suite_num = 1
        self._test_num = 1

    def write_xml_file(self):
        path = LogPathRepository().find('output_dir') + os.path.sep + 'out.xml'
        if os.path.exists(path):
            with open(path, 'w') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>')
        with open(path, 'a+', encoding='utf-8') as f:
            for s in self._start_list:
                f.write(s)
            for e in reversed(self._end_list):
                f.write(e)
            if not self._is_add_test:
                f.write('<suite><test></test></suite>')
            f.write('<errors>')
            for err in self._error_list:
                f.write(err)
            f.write('</errors></robot>')

    def _replace_xml_char(self, text):
        return text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

    def add_start_suite_element(self, args):
        _id = args[1].get('id', '')
        if not _id:
            _id = self._get_suite_id()
        self._suite_num = 1
        self._suite_id = _id
        suite = '<suite id="{0}" name="{1}" source="{2}">'.format(_id, self._replace_xml_char(args[0]), self._replace_xml_char(args[1].get('source', '')))
        self._start_list.append(suite)
        self._end_list.append('</suite>')

    def _get_suite_id(self):
        if not self._suite_id:
            _id = 's1'
        else:
            _id = self._suite_id + '-s%s' % self._suite_num
        return _id

    def add_end_suite_element(self, args):
        _id = args[1].get('id', '')
        if not _id:
            _id = self._suite_id
            self._suite_num = int(_id.split('-')[-1].lstrip('s')) + 1
        content = self._get_status_element(args) + self._end_list.pop()
        self._start_list.append(content)
        self._test_num = 1

    def _get_status_element(self, args):
        status = '<status status="{0}" starttime="{1}" endtime="{2}"/>'.\
            format(args[1].get('status', ''), args[1].get('starttime', ''), args[1].get('endtime', ''))
        return status

    def add_start_test_element(self, args):
        _id = args[1].get('id')
        if not _id:
            _id = self._suite_id + '-t%s' % self._test_num
        self._start_list.append('<test id="{0}" name="{1}">'.format(_id, self._replace_xml_char(args[0])))
        self._test_id = _id
        self._end_list.append('</test>')
        self._is_add_test = True

    def add_end_test_elements(self, args):
        status = '<status status="{0}" starttime="{1}" endtime="{2}" critical="{3}">'.\
            format(args[1].get('status', ''), args[1].get('starttime', ''), args[1].get('endtime', ''), args[1].get('critical', ''))
        msg = self._replace_xml_char(args[1].get('message', ''))
        test = status + msg + '</status>' + self._end_list.pop()
        self._start_list.append(test)
#         self._test = None
        self._test_num = int(self._test_id.split('-')[-1].lstrip('t')) + 1

    def add_start_keyword_elements(self, args):
        kw = self._add_kw_element(args)
        arg = self._add_arguments_element(args)
        ass = self._add_assign_element(args)
        self._start_list.append('{0}{1}{2}'.format(kw, arg, ass))
        self._end_list.append('</kw>')

    def _add_kw_element(self, args):
        kw_name = self._replace_xml_char(args[0])
        lib_name = self._replace_xml_char(args[1].get('libname', ''))
        _type = args[1].get('type', '')
        kw = ''
        name = self.deal_control_char(self._replace_xml_char(args[1].get('kwname', kw_name)))
        if _type[-3:] == 'For':
            kw = '<kw name="{0}" type="for">'.format(name)
            self._for_var_name = kw_name.split()[0]
        elif _type == 'For Item':
            kw = '<kw name="{0}" type="foritem">'.format(name)
        elif _type == 'Keyword':
            kw = '<kw name="{0}" library="{1}">'.format(name, lib_name)
        elif _type in ('Setup', 'Teardown'):
            kw = '<kw name="{0}" library="{1}" type="{2}">'.format(name, lib_name, _type.lower())
        else:
            kw = self._set_py2_kw_element(_type, args)
        return kw

    def _set_py2_kw_element(self, _type, args):
        kw = ''
        name = self._replace_xml_char(self.deal_control_char(args[0]))
        lib_name = self._replace_xml_char(args[1].get('libname', ''))
        if _type[-7:] == 'Foritem':
            if args[0].startswith(self._for_var_name + ' = '):
                kw = '<kw name="{0}" type="foritem">'.format(name)
            else:
                kw = '<kw name="{0}">'.format(name)
        if _type in ('Suite Setup', 'Suite Teardown', 'Test Setup', 'Test Teardown'):
            if not self._has_set_tear:
                _type = _type.lower().split()[-1]
                self._set_tear_name = args[0]
                self._has_set_tear = True
                kw = '<kw name="{0}" library="{1}" type="{2}">'.format(name, lib_name, _type)
            else:
                kw = '<kw name="{0}" library="{1}">'.format(name, lib_name)
        return kw

    def _add_arguments_element(self, args):
        arg = ''
        if args[1].get('args'):
            arg = '<arguments>'
            for a in args[1].get('args'):
                arg += '<arg>{0}</arg>'.format(self._replace_xml_char(self.deal_control_char(a)))
            arg += '</arguments>'
        return arg

    def _add_assign_element(self, args):
        ass = ''
        if args[1].get('assign'):
            ass = '<assign>'
            for a in args[1].get('assign'):
                ass += '<var>{0}</var>'.format(self._replace_xml_char(self.deal_control_char(a)))
            ass += '</assign>'
        return ass

    def add_end_keyword_element(self, args):
        if args[1].get('type') in ('Suite Setup', 'Suite Teardown', 'Test Setup', 'Test Teardown') and args[0] == self._set_tear_name:
            self._has_set_tear = False
        status = self._get_status_element(args)
        content = status + self._end_list.pop()
        self._start_list.append(content)

    def add_log_message_element(self, args):
        try:
            text = self._replace_xml_char(self.deal_control_char(args[0].get('message', '')))
        except:
            text = '???'
        msg = '<msg timestamp="{0}" level="{1}">{2}</msg>'.format(args[0].get('timestamp'), args[0].get('level', ''), text)
        self._start_list.append(msg)

    def create_error_msg_element(self, error_list):
        for e in error_list:
            try:
                if e.startswith('[ ERROR ]') and e.replace('[ ERROR ] ', '', 1):
                    text = self._replace_xml_char(self.deal_control_char(e.replace('[ ERROR ] ', '', 1)))
                    error = '<msg timestamp="{0}" level="ERROR">{1}</msg>'.format(get_date_time(), text)
                elif e.startswith('[ WARN ]') and e.replace('[ WARN ] ', '', 1):
                    text = self._replace_xml_char(self.deal_control_char(e.replace('[ WARN ] ', '', 1)))
                    error = '<msg timestamp="{0}" level="WARN">{1}</msg>'.format(get_date_time(), text)
            except:
                error = '<msg timestamp="{0}" level="ERROR">???</msg>'.format(get_date_time())
            self._error_list.append(error)

    def deal_control_char(self, s):
        return re.sub('[\x00-\x09|\x0b-\x0c|\x0e-\x1f]', '', s)
