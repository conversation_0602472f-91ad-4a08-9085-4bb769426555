# encoding:utf-8
'''
Created on 20191030

@author: 10225935
'''
import time
from functools import reduce


class ProgressBar(object):

    """A progress bar for the test runner plugin"""

    def __init__(self):
        self._pass = 0
        self._fail = 0
        self._current_keywords = []
        self._start_time = time.time()

    def set_current_keyword(self, name):
        self._current_keywords.append(name)

    def empty_current_keyword(self):
        if self._current_keywords:
            self._current_keywords.pop()

#     def OnTimer(self, event):
#         """A handler for timer events; it updates the statusbar"""
#         self._gauge.Show()
#         self._gauge.Pulse()
#         self._update_message()

#     def Start(self):
#         """Signals the start of a test run; initialize progressbar."""
# #         self._initialize_state()
#         self._start_time = time.time()
#         self._gauge.Show()
#         self._sizer.Layout()
#         self.SetBackgroundColour(self._default_colour)
#         self._timer.Start(50)

#     def Stop(self):
#         """Signals the end of a test run"""
#         self._gauge.Hide()
#         self._timer.Stop()

    def add_pass(self):
        """Add one to the passed count"""
        self._pass += 1
#         print('pass:%d'%self._pass)

    def add_fail(self):
        """Add one to the failed count"""
        self._fail += 1
#         print('fail:%d'%self._fail)

    def update_message(self):
        """Update the displayed elapsed time, passed and failed counts"""
        elapsed = time.time() - self._start_time
        message = "elapsed time: %s     pass: %s     fail: %s" % (
            secondsToString(elapsed), self._pass, self._fail)
        message += self._get_current_keyword_text()
        return message
#         self._label.SetLabel(message)
#         if self._fail > 0:
#             self.SetBackgroundColour("#FF8E8E")
#         elif self._pass > 0:
#             self.SetBackgroundColour("#9FCC9F")
        # not sure why this is required, but without it the background
        # colors don't look right on Windows
#         self.Refresh()

    def _get_current_keyword_text(self):
        if not self._current_keywords:
            return ''
        return '     current keyword: ' + \
               self._fix_size(' -> '.join(self._current_keywords), 50)

    def _fix_size(self, text, max_length):
        if len(text) <= max_length:
            return text
        return '...' + text[3 - max_length:]


def secondsToString(t):
    """Convert a number of seconds to a string of the form HH:MM:SS"""
    return "%d:%02d:%02d" % \
        reduce(lambda ll, b: divmod(ll[0], b) + ll[1:], [(t,), 60, 60])
