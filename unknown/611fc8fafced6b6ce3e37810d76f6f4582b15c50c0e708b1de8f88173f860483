# -*- coding: utf-8 -*-
'''
Created on 2019年10月24日

@author: 10247557
'''
import os


ALLOWED_PACKAGE = [b'from plugins.Plugin import AbstractPlugin\r\n', 
                   b'from plugins.SystemLibrary import SystemLibrary\r\n']

class ValidityChecker(object):

    def __init__(self, plugin_name):
        self._plugin_name = plugin_name
        self._plugin_path = self._get_plugin_path()
        self._python_file_list = []

    def _get_plugin_path(self):
        plugin_path = os.path.abspath(os.path.dirname(__file__) + '/{}'.format(self._plugin_name))
        print (plugin_path)
        return plugin_path

    def check_validity(self):
        self._get_python_file()
        for file in self._python_file_list:
            if not self._check_file_validity(file):
                print (file)
                return False
        return True

    def _get_python_file(self):
        for root,dirs,files in os.walk(self._plugin_path):
            for file in files:
                file = os.path.join(root,file)
                if file.endswith('.py'):
                    self._python_file_list.append(file)

    def _check_file_validity(self, file):
        with open(file, 'rb') as f:
            content = f.readlines()
        for line in content: 
            if not self._is_import_from_plugins(line.decode('utf-8')):
                print (line)
                return False
        return True

    def _is_import_from_plugins(self, line):
        if line.startswith('from') and  not line.startswith('from plugins'):
            return False
        return True

if __name__ == '__main__':
    a = ValidityChecker('demo')
    result = a.check_validity()
    print (result)
    