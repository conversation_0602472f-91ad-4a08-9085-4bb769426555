# coding=utf-8
'''
Created on 2019年10月30日

@author: 10240349
'''
import json
import os
import platform
from threading import Thread

from PyQt5.Qt import QIcon, Qt, QLabel, QCursor
from PyQt5.QtCore import pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QMovie
from PyQt5.QtWidgets import QAction, QDialog, QPushButton
import requests

from controller.system_plugin.UILibrary import UILibrary
from plugins.Plugin import AbstractPlugin
from plugins.SystemLibrary import SystemLibrary


dialog = QDialog()
update_lab = QLabel(dialog)
button = QPushButton("升级", dialog)
button.setCursor(QCursor(Qt.PointingHandCursor))

#VERSION_URL = "http://10.7.213.183:31381/version?operatingSystem={}"
VERSION_URL = "http://10.2.71.213:31381/version?operatingSystem={}"
#VERSION_URL = "http://127.0.0.1:31381/version?operatingSystem={}"


class Main(AbstractPlugin):
    UPDATE_PACKAGE_URL = None

    def activate(self):
        menu_bar = UILibrary.get_obj('MENU_BAR')
        menu = menu_bar.addMenu('&help')
        about_rfcode_action = QAction(QIcon(''), '检查更新', menu)
        about_rfcode_action.triggered.connect(self.show_dialog)
        menu.addAction(about_rfcode_action)

    @staticmethod
    # @pyqtSlot()
    def show_dialog():
        dialog.setWindowTitle("ABOUT")
        dialog.setFixedSize(420, 300)
        lab0 = QLabel(dialog)
        lab0.setPixmap(QPixmap("{}\..\RFCode.png".format(os.path.abspath(os.path.dirname(__file__)))))
        lab0.setGeometry(155, 20, 130, 130)
        # lab1.setStyleSheet("QLabel {background-color: red;}")
        lab1 = QLabel('当前版本：{}'.format(SystemLibrary.get_current_version()), dialog)
        lab1.setGeometry(150, 160, 400, 30)
        # lab2 = QLabel(dialog)
        update_lab.setGeometry(170, 190, 200, 100)
        gif = QMovie("{}\..\waiting.gif".format(os.path.abspath(os.path.dirname(__file__))))
        update_lab.setMovie(gif)
        gif.start()
        check = CheckThread()
        check.checked.connect(Main.update_result)
        check.start()
        button.clicked.connect(Main.update)
        button.move(180, 190)
        button.setVisible(False)
        dialog.setWindowModality(Qt.ApplicationModal)
        dialog.exec_()

    @staticmethod
    def update_result(result):
        global update_lab
        global dialog
        global button
        if not result:
            update_lab.clear()
            update_lab.setText('当前已经是最新版本')
        else:
            update_lab.setVisible(False)
            button.setVisible(True)
            Main.UPDATE_PACKAGE_URL = result

    @staticmethod
    def update():
        global button
        global update_lab
        update_lab.setVisible(True)
        button.setVisible(False)
        if not Main.UPDATE_PACKAGE_URL:
            raise Exception("update error")
        update = UpdateThread(Main.UPDATE_PACKAGE_URL)
        update.start()


class CheckThread(QThread):
    checked = pyqtSignal(str)

    def __int__(self):
        super().__init__()

    def run(self):
        operatingSystem = platform.architecture()[0]
        content = json.loads(requests.get(VERSION_URL.format(operatingSystem)).text)
        print(VERSION_URL.format(operatingSystem))
        print(content)
        print("本地当前版本:", SystemLibrary.get_current_version())
        print("服务器最新版本:", content.get("version"))
        if content and content.get("error_info"):
            self.checked.emit("")
        elif content.get("version") > SystemLibrary.get_current_version():
            print("download path :", content.get("path"))
            self.checked.emit(content.get("path"))
        else:
            self.checked.emit("")


class UpdateThread(Thread):
    def __init__(self, down_load_path):
        super().__init__()
        self.down_load_path = down_load_path

    def run(self):
        resp = requests.get(self.down_load_path)
        name = self.down_load_path.split('/')[-1]
        file_path = "{}/../../../tools/{}".format(os.path.abspath(os.path.dirname(__file__)), name)
        print("down_load_path", file_path)
        if resp.status_code == 200:
            with open(file_path, "wb") as file:
                file.write(resp.content)
            print("update file is ready")
            operatingType = platform.system()
            operatingSystem = platform.architecture()[0]
            print('operatingType is {}'.format(operatingType))
            print('operatingSystem is {}'.format(operatingSystem))
            if operatingType == "Windows":
                self.update_windows(file_path, operatingSystem)
            elif operatingType == "Linux":
                self.update_linux(file_path)
            else:
                print("not support")

    def update_windows(self, file_path, operatingSystem):
        tools_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../tools'))
        updater_name = "RFCODEUpdater_32.exe" if operatingSystem == "32bit" else "RFCODEUpdater.exe"
        updater_path = os.path.join(tools_dir, updater_name)
        if not os.path.exists(updater_path):
            raise FileNotFoundError(f"Updater not found at {updater_path}")
        update_cmd = f'cd "{tools_dir}" && "{updater_path}" "{file_path}"'
        print(f'start update:{update_cmd}')
        os.system(update_cmd)
    
    def update_linux(self, file_path):
        pass

