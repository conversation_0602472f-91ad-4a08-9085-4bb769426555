# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
from PyQt5.QtWidgets import QVBoxLayout
from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.component.Tag import Tag
from controller.system_plugin.edit.view.DirectoryEdit import DirectoryEdit


class TreeItemEdit(DirectoryEdit):

    def __init__(self, parent):
        super().__init__(parent)
        self._layout = None

    def load(self):
        if self._layout:
            return self._layout
        super().load()
        self._set_layout()
        return self._layout

    def _set_layout(self):
        self._layout = QVBoxLayout()
        self._layout.addLayout(self._path)
        self._layout.addLayout(self._settings)
        self._layout.addLayout(self._documentation.get_layout())
        self._layout.addLayout(self._suit_setup.get_layout())
        self._layout.addLayout(self._suit_teardown.get_layout())
        self._layout.addLayout(self._test_setup.get_layout())
        self._layout.addLayout(self._test_teardown.get_layout())
        self._layout.addLayout(self._force_tags.get_layout())
        self._layout.addLayout(self._import_area.get_layout())
        self._layout.addLayout(self._variable_area.get_layout())
        self._layout.addLayout(self._metadata_area.get_layout())

