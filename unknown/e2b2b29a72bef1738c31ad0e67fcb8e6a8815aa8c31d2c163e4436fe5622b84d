# coding=utf8
'''
Created on 2019年10月18日

@author: 10140129/10240349
'''
import sys
from PyQt5.Qt import QIcon, QVBoxLayout, QSizePolicy, QSize
from PyQt5.QtGui import <PERSON><PERSON>indow
from PyQt5.QtWidgets import QAction, QWidget

from controller.system_plugin.edit.EditCreator import EditCreator
from controller.system_plugin.edit.EditPluginController import EditPluginController
from controller.system_plugin.run.RunPlugin import RunPlugin
from controller.system_plugin.text_edit.TextEditPlugin import TextEditPlugin
from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.UIRepository import UIRepository
if sys.platform != "linux":
    import win32gui
    from controller.system_plugin.bash.BashPlugin import BashPlugin
from controller.system_plugin.rf_assistant.RFAssistantPlugin import RFAssistantPlugin


class PluginDrawer(object):


    def add_menu_plugins(self, parent_window):
        pass
#         FilePlugin(parent_window).load()
#         PluginManagement(parent_window).load()

    def add_tool_plugins(self, parent_window, parent):
        pass

    def add_edit_plugins(self, tab_obj, parent):
        edit_plugin_controller = EditPluginController(tab_obj, parent)
        UIRepository().add('edit_plugin_controller', edit_plugin_controller)
        edit_tab = QWidget()
        tab_obj.addTab(edit_tab, LanguageLoader().get('EDIT'))
        edit_plugin_controller.set_edit_tab(edit_tab)

    def add_bash_plugins(self, tab_obj):
        bash_plugin = BashPlugin(tab_obj)
        tab_obj.addTab(bash_plugin.get_widget(), "Bash")
        tab_obj.setTabText(SystemSettings().get_value('BASH_SLOT'), LanguageLoader().get('BASH'))

    def add_rf_helper_plugins(self, tab_obj):
        bash_plugin = RFAssistantPlugin(tab_obj)
        tab_obj.addTab(bash_plugin.get_widget(), LanguageLoader().get('RF_ASSISTANT'))
        tab_obj.setTabText(SystemSettings().get_value('RF_ASSISTANT_SLOT'), LanguageLoader().get('RF_ASSISTANT'))
        tab_obj.setCurrentIndex(SystemSettings().get_value('RF_ASSISTANT_SLOT'))

    def del_rf_helper_plugins(self, tab_obj):
        tab_obj.removeTab(SystemSettings().get_value('RF_ASSISTANT_SLOT'))

    def add_run_plugins(self, parent_tab, parent):
        parent_tab.setLayout(RunPlugin(parent).load())

    def add_text_edit_plugins(self, parent_tab, parent):
        parent_tab.setLayout(TextEditPlugin(parent).load())

    def add_widgets(self):
        pass

    def remove_widgets(self):
        pass
