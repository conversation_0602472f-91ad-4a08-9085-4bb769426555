from transformers import AutoModelForCausalLM, AutoTokenizer
import os
from typing import Dict
import logging
from functools import wraps
import time

def retry(max_retries=3, delay=1, exceptions=(Exception,)):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_retries:
                        raise e
                    print(f"Error: {e}, {delay}秒后第{attempt+1}次重试...")
                    time.sleep(delay)
        return wrapper
    return decorator
def setup_proxy() -> None:
    """Configure proxy settings for HTTP and HTTPS"""
    proxy_url = 'http://proxyhk.zte.com.cn:80'
    os.environ.update({
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url
    })
    # 设置镜像源示例（南京大学镜像）
    os.environ['HF_ENDPOINT'] = 'https://mirror.nju.edu.cn/huggingface'

def load_model(model_name: str = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B'):
    """
    Load the language model with error handling
    
    Args:
        model_name: Name/path of the model to load
    Returns:
        Loaded model instance
    """
    try:
        setup_proxy()

        model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True)
        print(model)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(tokenizer)
        return model, tokenizer
    except Exception as e:
        logging.error(f"Failed to load model {model_name}: {str(e)}")
        raise

def generate_text(prompt: str, model, tokenizer, max_length: int = 150) -> Dict[str, str]:
    """
    Generate text using the loaded model

    Args:
        prompt: Input prompt for text generation
        model: Loaded model instance
        tokenizer: Tokenizer for the model
        max_length: Maximum length of the generated text
    Returns:
        Dictionary containing the generated text
    """
    try:
        print(model)
        input_ids = tokenizer.encode(prompt, return_tensors="pt")
        output = model.generate(input_ids, max_length=max_length)
        generated_text = tokenizer.decode(output[0], skip_special_tokens=True)
        return {"generated_text": generated_text}
    except Exception as e:
        logging.error(f"Failed to generate text: {str(e)}")
        raise

@retry(max_retries=13, delay=2, exceptions=(ConnectionError,))
def main():
    """Main function to demonstrate the usage of the model loading and text generation functions."""    
    # Load the model
    print('--------Loading model--------')
    model, tokenizer = load_model()
    print('--------Model loaded--------')
    # Generate text using the model
    prompt = "你现在是一名资深的软件工程师，你熟悉多种编程语言和开发框架，对软件开发的生命周期有深入的理解。你擅长解决技术问题，并具有优秀的逻辑思维能力。请在这个角色下为我解答以下问题。请帮我写一个python程序，这个程序的功能是：用户输入一个数字，程序会输出这个数字的阶乘。"
    print(prompt)
    generated_text = generate_text(prompt, model, tokenizer)
    print(generated_text)



if __name__ == "__main__":
    print('--------Starting main function--------')
    while True:
        try:
            main()
            break
        except Exception as e:
            print(f"An error occurred: {str(e)}")
            print("Retrying...")


