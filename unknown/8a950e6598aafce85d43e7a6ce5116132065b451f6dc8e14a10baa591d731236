# -*- coding: UTF-8 -*-
import requests
import re
import socket
import hashlib
import json
import time

TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'
SSP_RF_GENERATE_APP = {"app_id": "13dfa7c758a34d92aa1b062461a3e1c4", "api_key": "prod_8fee038815b14ef297892b7d2ae9766c"}

class AiStudioApi(object):

    def __init__(self, para={}):
        self.user = para.get('account', '')
        self.password = para.get('password', '')
        self.token = para.get('token', '')
        self.auth = ''
        self.tokenStatus = 0
        self.hosetIp = ''
        if not self.token:
            print(self.token_ok())
        

    def _get_headers(self):
        headers = {'Content-type': 'application/json',
           'X-Emp-No': self.user,
           'X-Auth-Value': self.token,
           'X-Lang-Id': 'zh_CN'
           }
        return headers

    def token_ok(self):
        if self.tokenStatus == 0:
            ret = self.get_token(self.user, self.password)
            if ret['code']['code'] == '0000' and ret['bo']['code'] == '0000':
                self.token = ret['other']['token']
                self.auth = requests.auth.HTTPDigestAuth(self.user, ret['other']['token'])
                self.tokenStatus = 1
                return True
            else:
                return False
        else:
            return True

    def get_token(self, user, passwd):
        url = "http://uac.zte.com.cn/uaccommauth/auth/comm/verify.serv"
        clientip = self.get_host_ip()
        text =\
            {
                "account": user,
                "passWord": passwd,
                "loginClientIp": clientip,
                "loginSystemCode": 'Portal',
                "originSystemCode": '',
                "other": {
                    "networkArea": '1',
                    "networkAccessType": '1'
                },
                "verifyCode": hashlib.md5(str(user + passwd + clientip + 'Portal').encode(encoding='utf-8')).hexdigest()
            }
        headers = {'Content-type': 'application/json'}
        content = requests.post(url, data=json.dumps(text), headers=headers)
        return content.json()

    def get_host_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            self.hosetIp = s.getsockname()[0]
            return self.hosetIp
        finally:
            s.close()

    def read_json_file(self, json_file):
        with open(json_file, 'r') as f:
            json_data = json.load(f)
        return json_data

    def get_similar_test(self, caseinfo):
        url = 'https://rdcloud.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat'
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyCustomUserAgent/1.0',
            'Authorization': 'Bearer 4b62d6da4a294226bcc1d29146aaa1ed-eb483f811b7340e1b4afac6de04c9b74',
            'X-Emp-No': self.user,
            'X-Auth-Value': self.token
        }
        data = {
            "chatUuid":"",
            "chatName":"",
            "stream": False,
            "keep": True,
            "text": caseinfo,
            "model":"nebulacoder"
        }
        response = requests.post(url, json=data, headers=headers)
        return json.loads(response.text)

    def get_similar_test2(self, caseinfo):
        url = 'https://rdcloud.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat'
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyCustomUserAgent/1.0',
            'Authorization': 'Bearer 7bfd3c535f7f43dc8f80b2e8d432003a-b97e09c4d70741b6aaba87958fad11ef',
            'X-Emp-No': self.user,
            'X-Auth-Value': self.token
        }
        data = {
            "chatUuid":"",
            "chatName":"",
            "stream": False,
            "keep": True,
            "text": caseinfo,
            "model":"nebulacoder"
        }
        response = requests.post(url, json=data, headers=headers)
        return json.loads(response.text)

    def few_shot_rf(self, caseinfo, model='ZTEAIM-Saturn', temperature='0.1'):
        url = 'https://rdcloud.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat'
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyCustomUserAgent/1.0',
            'Authorization': 'Bearer d9688541eb85489dacecc2cb96e9d092-9641394d1b3542979e73d0c0361886ef',
            'X-Emp-No': self.user,
            'X-Auth-Value': self.token
        }
        data = {
            "chatUuid":"",
            "chatName":"",
            "stream": False,
            "keep": False,
            "text": caseinfo,
            "model": model,
            "temperature": temperature
        }
        response = requests.post(url, json=data, headers=headers)
        return json.loads(response.text)

    def few_shot_rf_stream(self, caseinfo, model='ZTEAIM-Saturn', temperature='0.1', callback=None, loneText=None):
        if loneText:
            url = 'https://gerritdb.zte.com.cn/gpt/api/v2/code/chat'
        else:
            url = 'https://rdcloud.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat'
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyCustomUserAgent/1.0',
            'Authorization': 'Bearer d9688541eb85489dacecc2cb96e9d092-9641394d1b3542979e73d0c0361886ef',
            'X-Emp-No': self.user,
            'X-Auth-Value': self.token
        }
        data = {
            "chatUuid":"",
            "chatName":"",
            "stream": True,
            "keep": False,
            "text": caseinfo,
            "model": 'nebulacoder-hp',
            "temperature": temperature
        }
        res = ''
        docId = ''
        response = requests.post(url, json=data, headers=headers, stream=True)
        try:
            for line in response.iter_lines():
                time.sleep(1)
                if line:
                    temp1 = line.decode('utf-8').replace('data: ', '')
                    if "[DONE]" in temp1:
                        break
                    jData = json.loads(temp1)
                    if not jData.get("docId"):
                        if 'AuthFailed' in jData.get("code", {}).get('msgId', ''):
                            return {'bo': {'docId': jData.get("docId"), 'result': 'AuthFailed'}}
                    if jData.get("docId"):
                        docId = jData.get("docId")
                    res += jData["result"]
                    if callback:
                        print(jData["result"])
                        callback(jData["result"])
            return {'bo': {'docId': docId, 'result': res}}
        except Exception as e:
            if callback:
                callback(str(e))
            print(str(e))
            return {'bo': {'docId': docId, 'result': res}}

    def upload_file(self, filePath, kbaseId, directoryId):
        url = 'https://icosg.dt.zte.com.cn/ai/kbase/file/upload'
        headers = {"X-Tenant-Id": "ZTE",
           "X-Emp-No": self.user,
           "X-Auth-Value": self.token,
           "appcode": "5a04672c46944750bd4c5ce5db39bd6f",
           }
        #构建文件上传所需的 HTTP 头信息和元数据信息
        data = {
            'metadata': '{"kbaseId":"' + kbaseId + '","directoryId":"' +  directoryId + '","coverFlag":true,"parseOperator":{},"splitOperator":{"pdf,docx,doc":{"operatorId":-1,"operatorEnName":"COMPREHENSIVE","operatorName":"","args":[{"argName":"maxWordCount","argType":"int","defaultValue":396,"description":"最大字符限制"},{"argName":"overlap","argType":"int","defaultValue":20,"description":"覆盖限制"}]},"md":{"operatorId":-3,"operatorEnName":"MARKDOWN_SPLIT","operatorName":"markdown切分","args":[{"argName":"maxWordCount","argType":"int","defaultValue":397,"description":"最大字符限制"},{"argName":"overlap","argType":"int","defaultValue":20,"description":"覆盖限制"},{"argName":"headLevel","argType":"int","defaultValue":3,"description":"标题级别"}]},"txt":{"operatorId":-2,"operatorEnName":"REGEX","operatorName":"正则切分","args":[{"argName":"maxWordCount","argType":"int","defaultValue":398,"description":"最大字符限制"},{"argName":"regexSymbol","argType":"str","defaultValue":"%5Cr%5Cn","description":"正则匹配"}]},"csv":{"operatorId":-4,"operatorEnName":"QA","operatorName":"QA切分","args":[{"argName":"maxWordCount","argType":"int","defaultValue":399,"description":"最大字符限制"},{"argName":"overlap","argType":"int","defaultValue":20,"description":"覆盖限制"}]}}}'
        }
        files = {
            'files': open(filePath, 'rb')
        }
        # 发送 POST 请求
        response = requests.post(url, headers=headers, data=data, files=files,verify=False)
        # 处理响应
        if response.status_code == 200:
            print('Upload successful:', response.json())
        else:
            print('Upload failed:', response.status_code, response.text)

    def ssp_rf_generate(self, caseinfo, model='ZTEAIM-Saturn', temperature='0.1', stream=False):
        url = 'https://rdcloud.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat'
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MyCustomUserAgent/1.0',
            'Authorization': 'Bearer {0}-{1}'.format(SSP_RF_GENERATE_APP.get('app_id'), SSP_RF_GENERATE_APP.get('api_key')),
            'X-Emp-No': self.user,
            'X-Auth-Value': self.token
        }
        data = {
            "chatUuid":"",
            "chatName":"",
            "stream": stream,
            "keep": False,
            "text": caseinfo,
            "model": model,
            "temperature": temperature
        }
        response = requests.post(url, json=data, headers=headers)
        return json.loads(response.text)

    def ssp_query_rf_batch_generate_result(self, task_executor):
        params = {
            "task_executor": task_executor,
        }
        response = requests.get("http://10.239.211.253:6008/ai/auto_generate_rf/query_case_code_list", params=params)
        print(response.json())

    def ssp_rf_batch_generate(self, task_executor, case_ids):
        
        data = {
            "case_ids": ','.join(case_ids),
            "task_executor": task_executor,
        }
        
        response = requests.post("http://10.239.211.253:6008/ai/auto_generate_rf/batch_generate_case_code", params=data)
        print(response.json())

if __name__ == '__main__':
    # asa = AiStudioApi({'account': "********", 'password': ''})
    # print(asa.ssp_rf_batch_generate('********', ['SAC-496296', 'SAC-398909']))
    # print(asa.ssp_query_rf_batch_generate_result('********'))
    case = '载波关断'
    asa= AiStudioApi({'account': "1012405", 'password': '', 'token': 'dfasfdsfasfdasdfsaf'})
    ret = asa.get_similar_test(case)
    print(ret['bo']['result'])
#     ret = AiStudioApi({'account': "1012405", 'password': '', 'token': ''}).get_similar_test(case)
#     print(ret)
#     testCase = ret['bo']['result']
#     testCaseList = testCase.split('\n\n ')
#     print(testCaseList)
#     db = MongoDb3()
#     paraList = []
#     for test in testCaseList:
#         testInfo = test.split('__')
#         if len(testInfo) > 4:
#             case_id = testInfo[-1]
#             case_info = db.query({'id': case_id}, TEST_CASE_INFO_DB_TABLE)
#             if case_info:
#                 if len(case_info[0].get('case_content')) < 4:
#                     continue
#                 paraList.append(case_info[0].get('name'))
#                 text = ''
#                 for c in case_info[0].get('case_content'):
#                     text = text + c + '\n'
#                 paraList.append(text)
#             if len(paraList) == 6:
#                 break
# 
#     promopt = '''
# 背景及指令：请根据示例输入和示例输出将下面测试步骤转化为RF脚本，只使用示例输出的脚本里的关键字，不要改变关键字名称或者参数，也不要把关键字改成英文,在代码块里输出脚本，不用做额外注释。删掉代码中的注释语句。。
# 测试步骤：{0}
#  
# 示例输入：{1}
# 示例输出：{2}
#  
# 示例输入：{3}
# 示例输出：{4}
#  
# 示例输入：{5}
# 示例输出：{6}
#     '''.format(case, paraList[0], paraList[1], paraList[2], paraList[3], paraList[4], paraList[5])
# 
#     print(promopt)
#       
#     ret = AiStudioApi('********', '').few_shot_rf(promopt, 'nebulacoder', '0.1')
# #     print(ret['bo']['result'])
    
    