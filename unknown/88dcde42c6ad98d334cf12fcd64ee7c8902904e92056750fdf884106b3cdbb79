[database]
host = ***********
port = 27017
dbname= SaveEnergy
username= tester
password=MongoDB123_!@#

[config]
projectDic= {
            '深圳Qcell': "深圳Qcell",
            '管理面_SZ': "管理面_SZ",
            '管理面_XA': "管理面_XA",
            '管理面_SH': "管理面_SH",
            '西安大容量': "西安大容量",
            '西安多模': "西安多模",
            'RT_SH': "RT_SH",
            'CPA_XA': "CPA_XA",
            'CPA_SH': 'CPA_SH',
            'CPA_SZ': "CPA_SZ",
            'SPA_SZ': "SPA_SZ",
            'SPA_XA': "SPA_XA",
            'SPA_SH': "SPA_SH",
            'UPA_SZ': "UPA_SZ",
            'UPA_XA': "UPA_XA",
            'UPA_SH': "UPA_SH",
            'PHY_SZ': "PHY_SZ",
            'PHY_XA': "PHY_XA",
            'PHY_SH': "PHY_SH",
            'OAM_SZ': "OAM_SZ",
            'OAM_XA': "OAM_XA",
            'IM-TOOL_SZ': "IM-TOOL_SZ",
            'IM-TOOL_XA': "IM-TOOL_XA",
            'IM-TOOL_SH': "IM-TOOL_SH",
            'IM-TOOL_SH': "IM-TOOL_SH",
            'IM-SON_SZ': "IM-SON_SZ",
            'IM-SON_XA': "IM-SON_XA",
            'IM-SON_SH': "IM-SON_SH",
            'IM-IPA': "IM-IPA",
            'CRV_SZ': "CRV_SZ",
            'CRV_XA': "CRV_XA",
            'TDDRRU': "TDDRRU",
            'SSP': "SSP",
            'NE': "NE",
        }
basicKeywordProjectMap = {'SSP': 'SSP', '深圳Qcell': 'Qcell'}
WORK_SPACE_INFO = {'SSP': 'SAC', 'NE': 'MEC'}
testPathSplitMap = {'深圳Qcell': 'testcase', '西安多模': 'testcase', 'SSP': 'autobmcv4'}
llmItems = ['电信大模型-Saturn(3.0)','nebulacoder-v6.0', 'nebulacoder-cot-v6.0']
llmNameDic = {'星云研发大模型': 'nebulacoder', '电信大模型-Saturn(3.0)': 'ZTEAIM-Saturn'}
TEST_CASE_PATH_INFO = {
            '深圳Qcell': [r'D:\script_v3\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell'],
            'RT_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\03_RT'],
            '管理面_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA\DTV3测试用例（model）\DTV3测试用例（SA）'],
            '管理面_XA': [r'D:\script_v3\5GNR\test\testcases\XA_ITRAN_MODEL'],
            '管理面_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\CM'],
            '西安大容量': [r'D:\script_v3\5GNR\test\testcases\Performance'],
            '西安多模': [r'D:\script_v3\5GNR\test\testcases\UnifiedTestcase_ITRAN',
                             r'D:\script_v3\5GNR\test\testcase\xian_fdd_dev_dept'],
            'CPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA'],
            'CPA_XA': [r'D:\script_v3\5GNR\test\testcases\SA',
                       r'D:\script_v3\5GNR\test\testcases\NSA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team11',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team5_Sub1g',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_CPA_Team2',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team1_Sub1g',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team10',
                       r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team2',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_CPA1_NRDC',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_UPA_Team1',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_Team10',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_UPA',
                       ],
            'CPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\CPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA'],
            'SPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_AutoTest_DT'],
            'SPA_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_02_SPA'],
            'SPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\SPA'],
            'UPA_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_03_UPA'],
            'UPA_XA': [r'D:\script_v3\5GNR\test\testcases\NSA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_XA_UPA_Team1',
                       r'D:\script_v3\5GNR\test\testcases\NR_XA_UPA',
                       r'D:\script_v3\5GNR\test\testcases\NR3_03_UPA',],
            'UPA_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\UPA'],
            'PHY_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_04_PHY'],
            'PHY_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_XA_PHY'],
            'PHY_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\PHY'],
            'OAM_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_05_产品OAM'],
            'OAM_XA': [r'D:\script_v3\5GNR\test\testcases\XA_NSA_NCM',
                       r'D:\script_v3\5GNR\test\testcases\NR3_05_产品OAM'],
            'IM-TOOL_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\DT'],
            'IM-TOOL_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\XA_DOMAIN'],
            'IM-TOOL_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_17_高频\02_DT\IM'],
            'IM-SON_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_06_IM-TOOL\03_频谱扫描|26_MDT\1vsw3vbpe5nr5lte',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\LCS',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\半静态频谱共享',
                          r'D:\script_v3\5GNR\test\testcases\NSA\son\dt',
                          r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\功率共享',
                          ],
            'IM-SON_XA': [r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON',
                          r'D:\script_v3\5GNR\test\testcases\SA\dt',
                          r'D:\script_v3\5GNR\test\testcases\NSA\son\dt'],
            'IM-SON_SH': [r'D:\script_v3\5GNR\test\testcases\NR3_07_IM-SON\Raw_Requirement_8293538_NR高频30'],
            'IM-IPA': [r'D:\script_v3\5GNR\test\testcases\IM-IPA'],
            'CRV_SZ': [r'D:\script_v3\5GNR\test\testcases\NR3_02_SPA\CA'],
            'CRV_XA': [r'D:\script_v3\5GNR\test\testcases\SA\cloudradio'],
            'CPE_XA': [r'D:\script_v3\5GNR\test\testcases\NR_XA_ATG'],
            'SSP': [r'D:\workspace\autobmcv4\SYSTEST'],
            'TDDRRU': [r'D:\script_v3\5GNR\test\testcases\NR_XA_RAN3_TDDRRU'],
            'NE': [r'D:\script_v3\5GNR\test\testcases\NE_SZ_CPA',
                   r'D:\script_v3\5GNR\test\testcases\NR3_01_CPA\DT_SOMKE\NE',
                   r'D:\script_v3\5GNR\test\testcases\NodeEngine'],
        }