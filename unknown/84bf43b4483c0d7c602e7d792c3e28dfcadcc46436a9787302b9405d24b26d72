body {
    background: white;
    color: black;
    font-size: small;
    font-family: sans-serif;
    padding: 0 0.5em;
}
.metadata th {
    text-align: left;
    padding-right: 1em;
}
a.name, span.name {
    font-style: italic;
}
a, a:link, a:visited {
    color: #c30;
}
a img {
    border: 1px solid #c30 !important;
}
a:hover, a:active {
    text-decoration: underline;
    color: black;
}
a:hover {
    text-decoration: underline !important;
}
.shortcuts {
    margin: 1em 0;
    font-size: 0.9em;
}
.shortcuts a {
    display: inline-block;
    text-decoration: none;
    white-space: nowrap;
    color: black;
}
.shortcuts a::first-letter {
    font-weight: bold;
    letter-spacing: 0.1em;
}
.normal-first-letter::first-letter {
    font-weight: normal !important;
    letter-spacing: 0 !important;
}
.keywords {
    border: 1px solid #ccc;
    border-collapse: collapse;
    empty-cells: show;
    margin: 0.3em 0;
    width: 100%;
}
.keywords th, .keywords td {
    border: 1px solid #ccc;
    padding: 0.2em;
    vertical-align: top;
}
.keywords th {
    background: #ddd;
    color: black;
}
.kw, .args, .tags {
    min-width: 100px;
    max-width: 20%;
}
td.kw a {
    color: inherit;
    text-decoration: none;
    font-weight: bold;
}
.args span {
    font-style: italic;
    padding: 0 0.1em;
}
.tags a {
    color: inherit;
    text-decoration: none;
    padding: 0 0.1em;
}
.footer {
    font-size: 0.9em;
}
/* Docs originating from HTML and reST are wrapped to divs. */
.doc div > *:first-child {
    margin-top: 0;
}
.doc div > *:last-child {    /* Does not work with IE8. */
    margin-bottom: 0;
}
#search, #open-search {
    position: fixed;
    bottom: 5px;
    right: 5px;
    z-index: 1000;
}
#search {
    width: 30em;
    display: none;
}
#open-search {
    border: 2px solid #ccc;
    border-radius: 4px;
    width: 40px;
    height: 40px;
    background-color: white;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAY5JREFUSImt1LtrFGEUBfCfURsFHwEr29UNkS3MFklrQK0EIYUk/5IQ0FSmCCKW1mpAommToCKoK+lsLUKeSFbXFLuT3B13Hjt64INvOPeec+fOnUs2mpjHBrbRwQE+YQFTObm5qGMZf0qct7gxjPgM9kqKJ+cAs2XFf4fEX3iOe7iKsxjFHTxFO8R2ikzqqcq/oVFQUANfUm8ynhUce97qVVoGo/gaclcGBTVDQDuvigw09Lfrr+maD+TSkOIJngWNx2lyI5C3KxrcDRof0+R2IC9XNLgSNPbTZDKa7YricFr/v3EqIUZ0xxPO4FxFg0vhnoz7scFmICcqGjTDvRWJEayG57mKBg/C/U2anHDSu5+oDSlex6GTlTE2KOhVMPmACyXFL+qOZZL7Xf/3OMY17KZMrheI13px6e26nmVyX3eDxnYt4lav0qTiaTzp8VkrPNdkNyOpkyM4lEkNL0uK/CjgXw8ySHATD7GGLd0/fgfv8QiTOI93BSb/jCKT/4Isk1ZOTiWTF0H8M8aPANvFyARlADGFAAAAAElFTkSuQmCC);
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIHZpZXdCb3g9IjAgMCA4IDgiPgogIDxwYXRoIGQ9Ik0zLjUgMGMtMS45MyAwLTMuNSAxLjU3LTMuNSAzLjVzMS41NyAzLjUgMy41IDMuNWMuNTkgMCAxLjE3LS4xNCAxLjY2LS40MWExIDEgMCAwIDAgLjEzLjEzbDEgMWExLjAyIDEuMDIgMCAxIDAgMS40NC0xLjQ0bC0xLTFhMSAxIDAgMCAwLS4xNi0uMTNjLjI3LS40OS40NC0xLjA2LjQ0LTEuNjYgMC0xLjkzLTEuNTctMy41LTMuNS0zLjV6bTAgMWMxLjM5IDAgMi41IDEuMTEgMi41IDIuNSAwIC42Ni0uMjQgMS4yNy0uNjYgMS43Mi0uMDEuMDEtLjAyLjAyLS4wMy4wM2ExIDEgMCAwIDAtLjEzLjEzYy0uNDQuNC0xLjA0LjYzLTEuNjkuNjMtMS4zOSAwLTIuNS0xLjExLTIuNS0yLjVzMS4xMS0yLjUgMi41LTIuNXoiCiAgLz4KPC9zdmc+), none;
    background-size: 24px 24px;
}
#open-search:hover {
    background-color: #ccc;
}
fieldset {
    background: white;
    border: 2px solid #ccc;
    border-radius: 4px;
    padding: 6px 8px;
}
fieldset fieldset {
    border: 1px solid #ccc;
    margin: 4px 0;
}
#search-title {
    font-size: 1.1em;
    font-weight: bold;
    letter-spacing: 1px;
}
#search-string {
    box-sizing: border-box;
    width: 100%;
}
#hide-unmatched {
    margin: 0.5em 0 0 1em;
}
#search-buttons {
    float: right;
}
.highlight {
    background: yellow;
}
.no-match {
    color: gray !important;
}
tr.no-match.hide-unmatched {
    display: none;
}
