from dataclasses import dataclass
from typing import List, Dict, Optional
import httpx
from openai import OpenAI
from pathlib import Path
import os
import logging
from dotenv import load_dotenv

@dataclass
class ProxyConfig:
    """
    Represents the configuration for an HTTP/HTTPS proxy.
    """

    host: str
    port: int

    def to_dict(self) -> Dict[str, str]:
        """Convert proxy configuration to dictionary format."""
        proxy_url = f"http://{self.host}:{self.port}"
        return {
            "http://": proxy_url,
            "https://": proxy_url
        }

class ChatClient:
    """A client for interacting with OpenAI-compatible chat APIs."""
    
    def __init__(
        self,
        api_key: str,
        base_url: str,
        proxy_config: Optional[ProxyConfig] = None,
        model: str = "deepseek-chat"
    ):
        """
        Initialize the chat client.
        
        Args:
            api_key: The API key for authentication
            base_url: The base URL for the API
            proxy_config: Optional proxy configuration
            model: The model to use for chat completion
        """
        self.model = model
        self.messages: List[Dict[str, str]] = []
        
        # Configure HTTP client with proxy if provided
        http_client = None
        if proxy_config:
            http_client = httpx.Client(proxies=proxy_config.to_dict())
        
        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            http_client=http_client
        )
    
    def send_message(self, message: str) -> str:
        """
        Send a message and get the response.
        
        Args:
            message: The message to send
            
        Returns:
            The response text from the API
        """
        # Add user message to conversation history
        self.messages.append({"role": "user", "content": message})
        
        try:
            # Get completion from API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=self.messages
            )
            
            # Add assistant's response to conversation history
            assistant_message = response.choices[0].message
            self.messages.append(assistant_message)
            
            return assistant_message.content
            
        except Exception as e:
            logging.error(f"Error during API call: {str(e)}")
            raise

    @property
    def conversation_history(self) -> List[Dict[str, str]]:
        """Get the full conversation history."""
        return self.messages

def main():
    """Main function demonstrating the usage of ChatClient."""
    # Load environment variables
    load_dotenv()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize proxy configuration
    proxy = ProxyConfig(
        host="proxyhk.zte.com.cn",
        port=80
    )
    
    # Initialize chat client
    client = ChatClient(
        api_key=os.getenv("OPENAI_API_KEY", "sk-7087fa57ac644e9b854a28c21645dd68"),
        base_url="https://api.deepseek.com",
        proxy_config=proxy
    )
    
    try:
        # Example conversation
        response1 = client.send_message("What's the highest mountain in the world?")
        logging.info(f"Response 1: {response1}")
        
        response2 = client.send_message("What is the second?")
        logging.info(f"Response 2: {response2}")
        
        # Print full conversation history
        logging.info("Full conversation history:")
        for msg in client.conversation_history:
            logging.info(f"{msg['role']}: {msg['content']}")
            
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    main()