
import os

from robot.errors import DataError, Information
from robot.utils.argumentparser import Argument<PERSON>arser
from robot.utils.encoding import SYSTEM_ENCODING

# from robotide.contrib.testrunner.usages import USAGE
from controller.test_runner.BaseProfile import BaseProfile


class PybotProfile(BaseProfile):
    name = "pybot"
    default_settings = {"arguments": u"",
                        "include_tags": "",
                        "exclude_tags": "",
                        "apply_include_tags": False,
                        "apply_exclude_tags": False}

    def __init__(self, plugin):
        BaseProfile.__init__(self, plugin)

    def get_command_prefix(self):
        '''Returns a command and any special arguments for this profile'''
        return [self.get_command()] + self._get_arguments()

    def _get_arguments(self):
        return self.arguments.split()

    def get_command(self):
        #return "pybot.bat" if os.name == "nt" else "pybot"
        return "pybot.bat" if os.name == "nt" else "pybot_linux.bat"

    def get_custom_args(self):
        args = []
        if self.apply_include_tags and self.include_tags:
            for include in self._get_tags_from_string(self.include_tags):
                args.append("--include=%s" % include)
        if self.apply_exclude_tags and self.exclude_tags:
            for exclude in self._get_tags_from_string(self.exclude_tags):
                args.append("--exclude=%s" % exclude)
        return args

    def _get_tags_from_string(self, tag_string):
        tags = []
        for tag in tag_string.split(","):
            tag = tag.strip().replace(' ', '')
            if len(tag) > 0:
                tags.append(tag)
        return tags

#     def OnArgumentsChanged(self, evt):
#         args = self._arguments.GetValue()
#         self._validate_arguments(args or u'')
#         self.set_setting("arguments", args)
#
#     def _validate_arguments(self, args):
#         invalid_message = self._get_invalid_message(args)
#         self._arguments.SetBackgroundColour('red' if invalid_message else 'white')
#         self._arguments.SetForegroundColour('white' if invalid_message else 'black')
#         self._arguments.SetToolTipString(invalid_message or 'Arguments for the test run. Arguments are space separated list.')
#
#     def _get_invalid_message(self, args):
#         try:
#             args = args.encode(SYSTEM_ENCODING)
#             _, invalid = ArgumentParser(USAGE).parse_args(args.split())
#             if bool(invalid):
#                 return 'Unknown option(s): ' + ' '.join(invalid)
#             return None
#         except DataError as e:
#             return e.message
#         except Information:
#             return 'Does not execute - help or version option given'
