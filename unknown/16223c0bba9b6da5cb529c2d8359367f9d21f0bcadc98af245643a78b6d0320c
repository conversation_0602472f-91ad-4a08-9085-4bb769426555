# coding=utf-8
from pymongo import MongoClient

from pymongo import MongoClient
from urllib.parse import quote_plus

class MongoDb3:
    def __init__(self,  dbcfg = {'ip': '***********',
                           'port': '27017',
                           'dbname': 'SaveEnergy',
                           'username': 'tester',
                           'password': 'MongoDB123_!@#'}):
        self._dbcfg = dbcfg
        self._reconnection()

    def _reconnection(self):
        username = quote_plus(self._dbcfg['username'])
        password = quote_plus(self._dbcfg['password'])
        uri = f"mongodb://{username}:{password}@{self._dbcfg['ip']}:{self._dbcfg['port']}/{self._dbcfg['dbname']}"
        self._client = MongoClient(uri, authSource=self._dbcfg['dbname'])
        self._db = self._client[self._dbcfg['dbname']]
        return self._client, self._db
    
    def set_collection(self, collection):
        if collection:
            self._collection = self._db[collection]

    def query_all(self, collection=None):
        self.set_collection(collection)
        def _query_all(self):
            cursor = self._collection.find()
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result
        return _query_all(self)

    def query(self, condition, collection=None):
        self.set_collection(collection)

        def _query(self, condition):
            cursor = self._collection.find(condition)
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result
        return _query(self, condition)

    def update(self, query, condition, collection=None, upsert=True):
        self._reconnection()
        self.set_collection(collection)
        self._collection.update_one(query, {'$set': condition}, upsert=upsert)

    def delete(self, condition=None, collection=None):
        self.set_collection(collection)
        self._collection.delete_many(condition)

    def insert(self, data, collection=None):
        self._reconnection()
        self.set_collection(collection)
        self._collection.insert_one(data)
    
    def close(self):
        self._client.close()


if __name__ == '__main__':
    dbcfg = {
        'ip': '***********',
        'port': '27017',
        'dbname': 'SaveEnergy',
        'username': 'tester',
        'password': 'MongoDB123_!@#'
    }
    
    # 创建 MongoDbHandler 实例
    mdb_handler = MongoDb3(dbcfg)
    result = mdb_handler.query({'envId': '5368'},'PsEnvHistory')
    print(result)
    # 现在你可以使用 mdb_handler._db 来操作数据库