# encoding=utf-8
'''
Created on 2019年12月27日

@author: 10247557
'''
from _functools import partial
from inspect import Traceback
import os
import signal
import subprocess
import threading
import time
import traceback

from PyQt5 import QtGui
from PyQt5.Qt import QProcess
from PyQt5.Qt import QWidget, QWindow, QHBoxLayout, QLabel, Qt, QThread, \
    pyqtSignal, QVBoxLayout
from PyQt5.QtGui import QTextDocumentFragment
from PyQt5.QtWidgets import QTextEdit
import psutil

from controller.system_plugin.SignalDistributor import SignalDistributor
from resources.Loader import Loader
from settings.HistoryProject import HistoryProject
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
import win32gui
import win32process
import win32con


class BashPlugin():

    def __init__(self, tab_obj):
        self._tab_obj = tab_obj
        self._hwnd = None
        self._bash_widget = QWidget()
        self._show_loading_info()
        SignalDistributor().bash_load.connect(partial(BashPlugin.start_by_thread, self))

    def _show_loading_info(self):
        layout = QHBoxLayout()
        label = QLabel('Loading...')
        layout.addWidget(label, 0, Qt.AlignTop)
        self._bash_widget.setLayout(layout)
        self._widget = self._bash_widget

    def get_widget(self):
        return self._widget

    @staticmethod
    def start_by_thread(self):
        if self._hwnd is None:
            self._sub_thread = SubThread()
            self._sub_thread.start()
            self._sub_thread.handle.connect(self.set_widget)
            SignalDistributor().close_event.connect(self._close_git_bash)

    def set_widget(self, handle):
        current_index = self._tab_obj.currentIndex()
        self._hwnd = handle
        widget = self._generate_widget()
        self._tab_obj.removeTab(self._tab_obj.indexOf(self._widget))
        self._tab_obj.addTab(widget, "Bash")
        self._tab_obj.setTabText(SystemSettings().get_value('BASH_SLOT'), LanguageLoader().get('BASH'))
        if current_index == SystemSettings().get_value('BASH_SLOT'):
            self._tab_obj.setCurrentIndex(SystemSettings().get_value('BASH_SLOT'))

    def _generate_widget(self):
        self._bash_widget = QTextEdit()
        if self._hwnd != 0:
            window = QWindow.fromWinId(self._hwnd)
            widget = self._bash_widget.createWindowContainer(window, self._bash_widget)
        else:
            self._set_warning_layout()
            self._bash_widget.setReadOnly(True)
            widget = self._bash_widget
        return widget

    def _set_warning_layout(self):
        error_info = LanguageLoader().get('BASH_ERROR_INFO')
        first_step = LanguageLoader().get('FIRST_STEP')
        first_step_png = self._set_first_step_png()
        second_step = LanguageLoader().get('SECOND_STEP')
        second_step_png = self._set_second_step_png()
        third_step = LanguageLoader().get('THIRD_STEP')
        third_step_png = self._set_third_step_png()
        for c in (error_info, first_step, first_step_png,
                  second_step, second_step_png, third_step,
                  third_step_png):
            self._bash_widget.append(c)

    def _set_first_step_png(self):
        return '<img src=' + '"{}"'.format(Loader().get_path('ENVIROMENT_VARIABLE')) + '>'

    def _set_second_step_png(self):
        return '<img src=' + '"{}"'.format(Loader().get_path('GIT_BASH')) + '>'

    def _set_third_step_png(self):
        return '<img src=' + '"{}"'.format(Loader().get_path('START_GIT_SUCCESS')) + '>'

    def _close_git_bash(self):
        if self._hwnd:
            win32gui.SendMessage(self._hwnd, win32con.WM_CLOSE, 0, 0)


class SubThread(QThread):

    handle = pyqtSignal(int)

    def __init__(self):
        super().__init__()

    def run(self):
        self._start_git_bash()
        self._get_hwnd()

    def _start_git_bash(self):
        self._project_path = HistoryProject.read('PROJECT_PATH')
        try:
            if self._project_path:
                subp = subprocess.Popen(r'git-bash --cd={}'.format(self._project_path))
            else:
                subp = subprocess.Popen([r'git-bash'])
            pid = subp.pid
            self._process = psutil.Process(pid)
        except Exception as e:
            traceback.print_exc()
            self._process = None

    def _get_hwnds_for_pid(self, pid):

        def callback(hwnd, hwnds):
            if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
                _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
                if found_pid == pid:
                    hwnds.append(hwnd)
            return True

        hwnds = []
        win32gui.EnumWindows(callback, hwnds)
        return hwnds

    def _get_child_process(self):
        for p in self._process.children(True):
            return p.pid
        return -1

    def _get_hwnd(self):
        hwnds = []
        if self._process:
            start = time.time()
            while len(hwnds) == 0:
                time.sleep(0.02)
                child_pid = self._get_child_process()
                hwnds = self._get_hwnds_for_pid(child_pid)
                end = time.time()
                if end - start > 5:
                    break
        if len(hwnds) != 0:
            self.handle.emit(hwnds[0])
        else:
            self.handle.emit(0)
