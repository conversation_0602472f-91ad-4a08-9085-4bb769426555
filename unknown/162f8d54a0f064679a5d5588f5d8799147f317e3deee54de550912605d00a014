# coding=utf-8
'''
Created on 2020年1月13日

@author: 10240349
'''
from _functools import partial
import os

from controller.file.Save import Save
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from settings.i18n.Loader import Language<PERSON>oader
from view.common.dialog.LabelAppenderDialog import LabelAppenderDialog
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.ResourceItem import ResourceItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
from view.explorer.tree_item.VariableItem import VariableItem


class FileUpdater(object):

    def __init__(self, parent):
        self._parent = parent

    def check(self, item):
        if isinstance(item, ProjectTreeItem):
            self._handle_directory(item)
        elif isinstance(item, SuiteItem) or isinstance(item, ResourceItem):
            self._handle_suite_or_resource(item)
        elif isinstance(item, VariableItem) or isinstance(item, UserKeywordItem):
            self._handle_variable_or_keyword(item)

    def _handle_directory(self, item):
        if self._is_exist(item._data_file):
            init_data_file = item._data_file.init_file
            if self._is_exist(init_data_file) and self._is_changed(init_data_file):
                self._handle_changed_item(item)

    def _handle_suite_or_resource(self, item):
        if self._is_exist(item._data_file) and self._is_changed(item._data_file):
            self._handle_changed_item(item)

    def _handle_variable_or_keyword(self, item):
        if self._is_exist(self._get_data_file(item)) and self._is_changed(self._get_data_file(item)):
            self._handle_changed_item(item.parent())

    def _refresh_up_directory(self, item):
        parent = item.parent()
        if parent:
            parent_data_file = parent._data_file
            if self._is_exist(parent_data_file):
                self._refresh_file(parent)
            else:
                self._refresh_up_directory(parent)

    def _handle_changed_item(self, item):
        self._parent._dialog = LabelAppenderDialog(LanguageLoader().get('FILE_MONITOR'))
        self._parent._dialog.append_text(LanguageLoader().get('CHANGED_FILE')+os.linesep+item.get_path())
        self._parent._dialog.show()
        self._parent._dialog.ok_pressed.connect(partial(self._refresh_file, item))
        self._parent._dialog.cancel_pressed.connect(self._save_current_file)

    def _is_changed(self, data_file):
        if data_file:
            return data_file.is_modified()
        else:
            return False

    def _is_exist(self, data_file):
        if data_file:
            return not data_file.is_delete()
        return True

    def _refresh_file(self, item):
        item.refresh_children()

    def _save_current_file(self):
        Save().save()

    def _get_data_file(self, item):
        parsed_item = ItemParserFacory().create(type(item).__name__ + 'Parser')
        return parsed_item.get_cur_data_file(item)
