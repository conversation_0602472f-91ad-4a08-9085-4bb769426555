# coding=utf-8
'''
Created on 2019年11月4日

@author: 10247557
'''
import copy
import logging
import os
import time
import traceback

from PyQt5.Qt import QThread, pyqtSignal

from controller.test_runner.TestRunnerPlugin import TestRunnerPlugin
from model.data_file.DataFile import SaveSequence
from settings.LogProject import LogProject
from settings.SystemSettings import SystemSettings
from utility.CmdFormatter import format_normal_test_case_path
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.LogPathRepository import LogPathRepository
from utility.PluginRepository import PluginRepository

MSG_LENGTH = 3000


class WorkThread(QThread):
    new_summary_log = pyqtSignal(str)
    new_trace_log = pyqtSignal(str)
    new_progress_log = pyqtSignal(str, TestRunnerPlugin)
    icon_status = pyqtSignal(object, str)

    def set_cmds(self, cmds):
        self._log_btn_gray = False
        self._cmds = cmds

    def set_btn(self, btn_list):
        self._btn_list = btn_list

    def run(self):
        self._start_plugin()
        LogPathRepository().add('LOG_GENERATED', False)
        while True:
            self._append_log()
            self._set_testcase_status()
            if not self._plugin._test_runner.is_running() or LogPathRepository().find('LOG_GENERATED'):
                logging.info('----' + str(LogProject.read("LOG_GENERATED")))
                self._config_with_testcase_stop()
                break
            time.sleep(0.2)
        self._clear()

    def _start_plugin(self):
        self._plugin = TestRunnerPlugin()
        self._plugin.enable()
        self._plugin._test_runner.set_pause_on_failure(PluginRepository().find('PAUSE_ON_FAILURE'))
        self._plugin.on_run(self._cmds)
        ExecutiveTestCaseRepository().add('is_test_case_running', True)

    def _config_with_testcase_stop(self):
        self._set_btn_status()
        if self._log_btn_gray:
            log_btn = self._btn_list[4]
            log_btn.setEnabled(True)

    def _clear(self):
        self._plugin.clear_messages_log_texts()
        ExecutiveTestCaseRepository().add('is_test_case_running', False)
        if PluginRepository().find('SHOW_REAL_TIME_LOG'):
            del self._plugin.real_time_xml

    def _set_testcase_status(self):
        item_dict = ExecutiveTestCaseRepository().find('EXECUTIVE_ITEM')
        ExecutiveTestCaseRepository().add('OLD_EXECUTIVE_ITEM_', item_dict)
        _dict = self._format_key(item_dict)
        test_state = copy.deepcopy(self._plugin.get_test_state())
        for testcase, status in test_state.items():
            if testcase in _dict.keys():
                self.icon_status.emit(_dict[testcase], status)

    def _format_key(self, item_dict):
        _dict = {}
        for k, v in item_dict.items():
            key = format_normal_test_case_path(k).upper().replace(" ", "_")
            _dict[key] = v
        return _dict

    def set_log_btn_status(self, log_btn_gray):
        self._log_btn_gray = log_btn_gray

    def _set_btn_status(self):
        start_btn = self._btn_list[0]
        start_btn.setEnabled(True)
        stop_btn = self._btn_list[1]
        stop_btn.setEnabled(False)
        pause_btn = self._btn_list[2]
        pause_btn.setEnabled(False)
        continue_btn = self._btn_list[3]
        continue_btn.setEnabled(False)
        log_btn = self._btn_list[4]
        log_btn.setEnabled(True)
        PluginRepository().update('TESTCASE_RUNNING', False)
        SaveSequence.save_all()

    def _append_log(self):
        self._append_trace_log()
        self._append_summary_log()
        self._append_progress_log()

    def _append_trace_log(self):
        count, log = 0, ''
        try:
            max_count = self._set_max_count()
            while not self._plugin.messages_log_texts.empty() and count < float(max_count):
                count += 1
                log += self._plugin.messages_log_texts.get(False) + os.linesep
            if log and len(log) <= MSG_LENGTH:
                self.new_trace_log.emit(log)
        except Exception:
            traceback.print_exc()

    def _set_max_count(self):
        max_count = SystemSettings().read('TRACE_LOG_COUNT')
        if not max_count:
            max_count = 2
        return max_count

    def _append_summary_log(self):
        summary_log = self._get_summary_log()
        if summary_log and len(summary_log) <= MSG_LENGTH:
            self.new_summary_log.emit(summary_log)

    def _get_summary_log(self):
        output, errors = self._plugin._test_runner.get_output_and_errors()
        both_log = ""
        if output:
            both_log += output
        if errors:
            both_log += errors
            if PluginRepository().find('SHOW_REAL_TIME_LOG'):
                self._add_error_msg_element(errors)
        return both_log

    def _add_error_msg_element(self, errors):
        error_list = errors.split('\n')
        new_errors = []
        for err in error_list:
            if err[:6] in ['[ WARN', '[ ERRO']:
                new_errors.append(err)
            else:
                if new_errors:
                    new_errors[-1] += '\n' + err
        self._plugin.real_time_xml.create_error_msg_element(new_errors)

    def write_xml(self):
        self._plugin.real_time_xml.write_xml_file()

    def _append_progress_log(self):
        progress = self._plugin.progress_bar.update_message()
        if progress:
            self.new_progress_log.emit(progress, self._plugin)
