# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''

from PyQt5.Qt import Qt, <PERSON><PERSON><PERSON><PERSON>, QFont, QCursor
from PyQt5.QtWidgets import QPushButton, QHBoxLayout, QLabel, QLineEdit

from controller.system_plugin.edit.view.component.Documentation import Documentation
from controller.system_plugin.edit.view.component.ImportEditArea import ImportEditArea
from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.component.Metadata import MetadataEditArea
from controller.system_plugin.edit.view.component.Tag import Tag
from controller.system_plugin.edit.view.component.VariableEditArea import VariableEditArea
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader


class DirectoryEdit(QWidget):

    def __init__(self, parent):
        super().__init__()
        self._parent_window = parent
        self._is_visible = True

    def load(self):
        self._documentation = self._set_documentation_area()
        self._suite_setup = self._set_suit_setup_area()
        self._suite_teardown = self._set_suit_teardown_area()
        self._test_setup = self._set_test_setup_area()
        self._test_teardown = self._set_test_teardown_area()
        self._force_tags = self._set_force_tags()
        self._settings = self._set_settings_button()
        self._import_area = self._set_import_area()
        self._variable_area = self._set_variable_area()
        self._metadata_area = self._set_metadata_area()
        self._name = self._set_name_area()
        self._path = self._set_path_area()

    def _set_name_area(self):
        self._name_line = QLineEdit()
        self._name_line.setFont(QFont("Roman times", 10, QFont.Bold))
        self._name_line.setStyleSheet("border-width:0;border-style:outset")
        self._name_line.setReadOnly(True)
        layout = QHBoxLayout()
        layout.addWidget(self._name_line)
        return layout

    def _set_path_area(self):
        self._label = QLabel('Source')
        self._path_line = QLineEdit()
        self._path_line.setStyleSheet("border-width:0;border-style:outset")
        self._path_line.setFixedHeight(25)
        self._path_line.setReadOnly(True)
        null_label = QLabel('                   ')
        layout = QHBoxLayout()
        layout.addWidget(self._label, 1)
        layout.addWidget(self._path_line, 6)
        layout.addWidget(null_label)
        return layout

    def _set_settings_button(self):
        layout = QHBoxLayout()
        self._settings_btn = QPushButton(LanguageLoader().get('SETTINGS'), self._parent_window)
        self._settings_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._settings_btn.setFixedSize(120, 25)
        self._settings_btn.clicked.connect(self._set_visible)
        layout.addWidget(self._settings_btn, 0, Qt.AlignLeft)
        return layout

    def _set_documentation_area(self):
        documentation = Documentation(self._parent_window)
        documentation.load()
        return documentation

    def _set_suit_setup_area(self):
        suit_setup = LineEditArea(self._parent_window, 'Suite Setup')
        suit_setup.load()
        return suit_setup

    def _set_suit_teardown_area(self):
        suit_teardown = LineEditArea(self._parent_window, 'Suite Teardown')
        suit_teardown.load()
        return suit_teardown

    def _set_test_setup_area(self):
        test_setup = LineEditArea(self._parent_window, 'Test Setup')
        test_setup.load()
        return test_setup

    def _set_test_teardown_area(self):
        test_teardown = LineEditArea(self._parent_window, 'Test Teardown')
        test_teardown.load()
        return test_teardown

    def _set_force_tags(self):
        tag = Tag(self._parent_window, 'Force Tags')
        tag.load()
        return tag

    def _set_import_area(self):
        import_area = ImportEditArea(self._parent_window)
        import_area.load()
        return import_area

    def _set_variable_area(self):
        variable_area = VariableEditArea(self._parent_window)
        variable_area.load()
        return variable_area

    def _set_metadata_area(self):
        metadata_area = MetadataEditArea(self._parent_window)
        metadata_area.load()
        return metadata_area

    def _set_visible(self):
        if self._is_visible:
            self._is_visible = False
            self._set_visible_area()
        else:
            self._is_visible = True
            self._set_visible_area()

    def _set_visible_area(self):
        self._documentation.set_visible(self._is_visible)
        self._suite_setup.set_visible(self._is_visible)
        self._suite_teardown.set_visible(self._is_visible)
        self._test_setup.set_visible(self._is_visible)
        self._test_teardown.set_visible(self._is_visible)
        self._force_tags.set_visible(self._is_visible)

    def fill_data(self, parsed_item):
        path = CurrentItem().get().get('path').replace('/', '\\')
        self._name_line.setText(path.split('\\')[-1].split('.')[0])
        self._name_line.setCursorPosition(0)
        self._path_line.setText(path)
        self._path_line.setCursorPosition(0)
        self._documentation.fill_data(parsed_item.documentation)
        self._suite_setup.fill_data(parsed_item.suite_setup)
        self._suite_teardown.fill_data(parsed_item.suite_teardown)
        self._test_setup.fill_data(parsed_item.test_setup)
        self._test_teardown.fill_data(parsed_item.test_teardown)
        self._force_tags.fill_data(parsed_item.force_tags)
        self._import_area.fill_data(parsed_item.imports)
        self._variable_area.fill_data(parsed_item.variable_table)
        self._metadata_area.fill_data(parsed_item.metadata_table)

    def _fill_table(self, parsed_item):
        self._table.reset_table()
        row, column = -1, -1
        body_list = parsed_item.table
        for line in body_list:
            row = row + 1
