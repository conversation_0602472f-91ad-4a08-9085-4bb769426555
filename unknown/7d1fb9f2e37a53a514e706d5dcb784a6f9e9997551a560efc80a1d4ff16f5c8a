'''
Created on 2019年11月27日

@author: 10243352
'''
DOCUMENTATION = 'documentation'
SUITE_SETUP = 'suite_setup'
SUITE_TEARDOWN = 'suite_teardown'
TEST_SETUP = 'test_setup'
TEST_TEARDOWN = 'test_teardown'
FORCE_TAGS = 'force_tags'
IMPORTS = 'imports'
METADATA = 'metadata'


class ItemParser(object):
    
    def __init__(self):
        self.table = []

    def get_cur_data_file(self, item):
        raise NotImplementedError()

    def get_cur_obj(self, item):
        if item:
            return item._data_file

    def _set_default(self):
        self.documentation = None
        self.suite_setup = None
        self.suite_teardown = None
        self.test_setup = None
        self.test_teardown = None
        self.force_tags = None
        self.import_table = None
        self.imports = None
        self.variable_table = None
        self.metadata_table = None

    def _get_documentation(self):
        self.documentation = self._settings.documentation if hasattr(self._settings, DOCUMENTATION) else None

    def _get_suite_setup(self):
        self.suite_setup = self._settings.suite_setup if hasattr(self._settings, SUITE_SETUP) else None

    def _get_suite_teardown(self):
        self.suite_teardown = self._settings.suite_teardown if hasattr(self._settings, SUITE_TEARDOWN) else None

    def _get_test_setup(self):
        self.test_setup = self._settings.test_setup if hasattr(self._settings, TEST_SETUP) else None

    def _get_test_teardomn(self):
        self.test_teardown = self._settings.test_teardown if hasattr(self._settings, TEST_TEARDOWN) else None

    def _get_force_tags(self):
        self.force_tags = self._settings.force_tags if hasattr(self._settings, FORCE_TAGS) else None

    def _get_imports(self):
        self.imports = self._settings.imports if hasattr(self._settings, IMPORTS) else None

    def _get_metadata(self):
        self.metadata_table = self._settings.metadata if hasattr(self._settings, METADATA) else None