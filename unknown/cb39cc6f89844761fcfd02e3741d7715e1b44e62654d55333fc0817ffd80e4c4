from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import os, requests
import configparser
import ast
from pathlib import Path


GLOBAL_CONFIG_URL = "http://10.2.71.213:31381/RFCode/Config/{}.txt"

class LoadTestcaseKbThread(QThread):
    Testcase_kb_loaded = pyqtSignal(list)


    def __init__(self, parent=None, project=None):
        super().__init__(parent)
        self.project = project
        self.GLOBAL_CONFIG_URL = GLOBAL_CONFIG_URL.format(self.project)
        name = self.GLOBAL_CONFIG_URL.split('/')[-1]
        self.file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), name)
        self.testcaseKbList = []
    
    def load_testcase_kb_data(self):
        self.load_testcase_kb_from_server()
        self.read_testcase_kb_data()

    def load_testcase_kb_from_server(self):
        try:
            resp = requests.get(self.GLOBAL_CONFIG_URL, timeout=10)
            print("Downloading config file to:", self.file_path)
            if resp.status_code == 200:
                with open(self.file_path, "wb") as file:
                    file.write(resp.content)
                print("Config file downloaded successfully")
                return True
            else:
                print(f"Failed to download config file. Status code: {resp.status_code}")
                return False
        except Exception as e:
            print(f"Error downloading config file: {str(e)}")
            return False

    def read_testcase_kb_data(self):
        content = ''
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            try:
                with open(self.file_path, 'r', encoding='gbk') as file:
                    content = file.read()
            except Exception as e:
                print(f"Error reading config file: {str(e)}")
                return
        self.testcaseKbList = content.split('\n')

    def run(self):
        try:
            self.load_testcase_kb_data()
        except Exception as e:
            print(f"Error loading testcase_kb_data config: {str(e)}")
        self.Testcase_kb_loaded.emit(self.testcaseKbList)


if __name__ == "__main__":
    thread = LoadTestcaseKbThread(None, '深圳Qcell')
    thread.start()
    
    try:
        while thread.isRunning():
            QThread.msleep(100)
    except KeyboardInterrupt:
        print("Shutting down...")
        thread.quit()
        thread.wait()
