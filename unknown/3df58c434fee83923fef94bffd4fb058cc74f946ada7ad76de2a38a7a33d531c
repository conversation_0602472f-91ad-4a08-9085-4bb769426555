# coding=utf-8
from queue import Empty, Queue
import threading


try:
    from robot.utils import encoding
except ImportError:
    from robot.utils.encodingsniffer import (get_console_encoding,
                                             get_system_encoding)
    CONSOLE_ENCODING = get_console_encoding()
    SYSTEM_ENCODING = get_system_encoding()
    encoding = {'CONSOLE': CONSOLE_ENCODING,
                'SYSTEM': SYSTEM_ENCODING}

IS_WINDOWS = True


class StreamReaderThread(object):

    def __init__(self, stream):
        self._queue = Queue()
        self._thread = None
        self._stream = stream

    def run(self):
        self._thread = threading.Thread(target=self._enqueue_output,
                                        args=(self._stream,))
        self._thread.daemon = True
        self._thread.start()

    def _enqueue_output(self, out):
        for line in iter(out.readline, b''):
            self._queue.put(line)

    def pop(self):
        result = ""
        try:
            myqueuerng = xrange(self._queue.qsize())
        except NameError:  # py3
            myqueuerng = range(self._queue.qsize())
        for _ in myqueuerng:
            try:
                result += encoding.console_decode(self._queue.get_nowait(),
                                                  encoding.OUTPUT_ENCODING if IS_WINDOWS
                                                  else 'UTF-8')
            except Empty:
                pass
        return result
