'''
Created on 2019年12月19日

@author: 10243352
'''
from PyQt5.Qt import QThread, pyqtSignal, QObject

from model.searcher.TestCase import TestCase
from settings.SystemSettings import SystemSettings


class SearchRepoInitThread(QThread, QObject):

    init_search_repo_finished = pyqtSignal()
    init_search_repo_running = pyqtSignal()

    def __init__(self, path):
        super().__init__()
        self._path = path

    def run(self):
        self.init_search_repo_running.emit()
        if self._is_config_testcase_lib():
            for path in SystemSettings().read("SOURCE_TESTCASE_LIB").values():
                TestCase(path)
        self.init_search_repo_finished.emit()

    def _is_config_testcase_lib(self):
        return True if SystemSettings().read("SOURCE_TESTCASE_LIB") else False
