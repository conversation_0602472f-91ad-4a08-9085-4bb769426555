# encoding=utf-8
'''
Created on 2019年11月25日

@author: 10247557
'''
SETTINGS = 'settings'
VARIABLES = 'variables'
DEFAULT_TAGS = 'default_tags'

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParser import Item<PERSON>ars<PERSON>
from controller.system_plugin.edit.parser.SuiteItemParser import SuiteItemParser
from model.CurrentItem import CurrentItem
from utility.Singleton import Singleton


@Singleton
class SuiteVariableItemParser(ItemParser):

    def query(self, item):
        self._item = item.parent()
        self._set_default()
        self._content = self._item._data_file
        self._get_variables()
        if self._content and hasattr(self._content, SETTINGS):
            self._settings = self._content.settings.query()
            self._get_documentation()
            self._get_suite_setup()
            self._get_suite_teardown()
            self._get_test_setup()
            self._get_test_teardomn()
            self._get_force_tags()
            self._get_default_tags()
            self._get_imports()
            self._get_metadata()

    def _set_default(self):
        super()._set_default()
        self.default_tags = None

    def get_cur_data_file(self, item):
        return item.parent()._data_file

    def _get_variables(self):
        self.variable_table = self._content.variables.query() if hasattr(self._content, VARIABLES) else None

    def _get_default_tags(self):
        self.default_tags = self._settings.default_tags if hasattr(self._settings, DEFAULT_TAGS) else None

    def modify(self, area_type, content, index=None):
        if self._content:
            self._content.modify_settings(area_type, content)
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'type': 'other', 'operator': 'modify'})
