# coding=utf-8
'''
Created on 2019年11月5日

@author: 10247557
'''
import os

from PyQt5.QtWidgets import QAction, QTableWidgetItem, QApplication

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
from utility.UIRepository import UIRepository


class TableMenu(object):

    def __init__(self, table_obj):
        self._table_obj = table_obj
        self._table = table_obj._table
        self.clipboard = QApplication.clipboard()
        self._undo_text = {}
        self._redo_text = {}

    def load_menu(self):
        self._menu = self._table_obj._menu
        self._menu.addAction(self._insert_rows_action)
        self._menu.addAction(self._delete_rows_action)
        self._menu.addAction(self._insert_cells_action)
        self._menu.addAction(self._delete_cells_action)
        self._menu.addAction(self._delete_action)
        self._menu.addAction(self._select_all_action)
        self._menu.addAction(self._copy_action)
        self._menu.addAction(self._paste_action)
        self._menu.addAction(self._cut_action)
        self._menu.addAction(self._comment_rows_action)
        self._menu.addAction(self._uncomment_rows_action)
        self._menu.addAction(self._make_variable_action)
        self._menu.addAction(self._list_variable_action)
        self._menu.addAction(self._move_up_action)
        self._menu.addAction(self._move_down_action)

    def load_shortcut(self):
        self._set_insert_rows()
        self._set_delete_rows()
        self._set_insert_cells()
        self._set_delete_cells()
        self._set_delete()
        self._set_select_all()
        self._set_copy()
        self._set_paste()
        self._set_cut()
        self._set_undo()
        self._set_redo()
        self._set_comment_rows()
        self._set_uncomment_rows()
        self._set_make_variable()
        self._set_make_list_variable()
        self._set_move_up()
        self._set_move_down()

    def _set_undo(self):
        self._undo_action = QAction('undo')
        self._undo_action.setShortcut('Ctrl+Z')
        self._undo_action.triggered.connect(self._undo)
        self._table.addAction(self._undo_action)

    def _set_redo(self):
        self._redo_action = QAction('redo')
        self._redo_action.setShortcut('Ctrl+Y')
        self._redo_action.triggered.connect(self._redo)
        self._table.addAction(self._redo_action)

    def _set_insert_rows(self):
        self._insert_rows_action = QAction('Insert Rows')
        self._insert_rows_action.setShortcut('Ctrl+I')
        self._insert_rows_action.triggered.connect(self._insert_rows)
        self._table.addAction(self._insert_rows_action)

    def _insert_rows(self):
        row_list, _ = self._get_select_row_col()
        self._table.blockSignals(True)
        for _ in row_list:
            self._table.insertRow(row_list[0])
            if self._is_for_loop(row_list[0]):
                Colorizer().colorize(self._table_obj, row_list[0], ['\\'])
        self._table.blockSignals(False)
        self.save_history_data()
        self._table_obj._modify_data()
#         SignalDistributor().modify_table_event()

    def _is_for_loop(self, row):
        last_row = row - 1
        if last_row >= 0:
            if self._table.item(last_row, 0):
                text = self._table.item(last_row, 0).text()
                if text == '\\' or text.strip('[|]|:').strip().lower() == "for":
                    return True
        return False

    def _set_delete_rows(self):
        self._delete_rows_action = QAction('Delete Rows')
        self._delete_rows_action.setShortcut('Ctrl+D')
        self._delete_rows_action.triggered.connect(self._delete_rows)
        self._table.addAction(self._delete_rows_action)

    def _delete_rows(self):
        row_list, _ = self._get_select_row_col()
        self._table.blockSignals(True)
        for _ in row_list:
            self._table.removeRow(row_list[0])
        rows = self._table.rowCount()
        if rows < 1:
            self._table.insertRow(0)
        if rows - 1 >= row_list[0]:
            self._table.setCurrentCell(row_list[0], 0)
        else:
            self._table.setCurrentCell(rows - 1, 0)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _set_insert_cells(self):
        self._insert_cells_action = QAction('Insert Cells')
        self._insert_cells_action.setShortcut('Ctrl+Shift+I')
        self._insert_cells_action.triggered.connect(self._insert_cells)
        self._table.addAction(self._insert_cells_action)

    def _insert_cells(self):
        row_num, col_num = self._get_select_row_col()
        self._table.blockSignals(True)
        for row in row_num:
            cols = self._table.columnCount()
            texts = self._get_select_row_texts(row, cols)
            for _ in col_num:
                texts.insert(col_num[0], None)
            new_texts = self._remove_end_null_value(texts)
            Colorizer().colorize(self, row, new_texts)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _remove_end_null_value(self, data):
        new_texts = data[::-1]
        for text in data[::-1]:
            if text:
                break
            new_texts.remove(text)
        return new_texts[::-1]

    def _set_delete_cells(self):
        self._delete_cells_action = QAction('Delete Cells')
        self._delete_cells_action.setShortcut('Ctrl+Shift+D')
        self._delete_cells_action.triggered.connect(self._delete_cells)
        self._table.addAction(self._delete_cells_action)

    def _delete_cells(self):
        row_num, col_num = self._get_select_row_col()
        self._table.blockSignals(True)
        for row in row_num:
            cols = self._table.columnCount()
            texts = self._get_select_row_texts(row, cols)
            for col in col_num[::-1]:
                texts.pop(col)
                texts.append(None)
            Colorizer().colorize(self, row, texts)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _get_select_row_col(self):
        indexs = self._table.selectedIndexes()
        row_num, col_num = [], []
        for index in indexs:
            if index.row() not in row_num:
                row_num.append(index.row())
            if index.column() not in col_num:
                col_num.append(index.column())
        return row_num, col_num

    def _get_select_row_texts(self, row, cols):
        texts = []
        for col in range(cols):
            if self._table.item(row, col):
                texts.append(self._table.item(row, col).text().strip())
            else:
                texts.append(None)
        return texts

    def _set_delete(self):
        self._delete_action = QAction('Delete')
        self._delete_action.setShortcut('Del')
        self._delete_action.triggered.connect(self._delete)
        self._table.addAction(self._delete_action)

    def _delete(self):
        if self._table.hasFocus():
            indexs = self._table.selectedIndexes()
            for index in indexs:
                self._table.setItem(index.row(), index.column(), None)
            self.save_history_data()
            self._table_obj._modify_data()

    def _set_select_all(self):
        self._select_all_action = QAction('Select All')
        self._select_all_action.setShortcut('Ctrl+A')
        self._select_all_action.triggered.connect(self._select_all)
        self._table.addAction(self._select_all_action)

    def _select_all(self):
        self._table.selectAll()

    def _set_copy(self):
        self._copy_action = QAction('Copy')
        self._copy_action.setShortcut('Ctrl+C')
        self._copy_action.triggered.connect(self._copy)
        self._table.addAction(self._copy_action)

    def _copy(self):
        indexs = self._table.selectedIndexes()
        texts = []
        for i in range(len(indexs)):
            index = self._table.item(indexs[i].row(), indexs[i].column())
            text = index.text() if index else ''
            if i > 0 and indexs[i].row() != indexs[i - 1].row():
                texts.append([text])
            else:
                if texts:
                    texts[-1].append(text)
                else:
                    texts.append([text])
        text = self._format_data(texts)
        self.clipboard.setText(text)

    def _set_paste(self):
        self._paste_action = QAction('Paste')
        self._paste_action.setShortcut('Ctrl+V')
        self._paste_action.triggered.connect(self._paste)
        self._table.addAction(self._paste_action)

    def _paste(self):
        select_index = self._table.selectedIndexes()[0]
        text = self.clipboard.text()
        text_list = [line.split('\t') for line in text.splitlines()]
        row = select_index.row()
        col = select_index.column()
        self._insert_extra_row_col(text_list, row, col)
        self._table.blockSignals(True)
        for row_text in text_list:
            for col_text in row_text:
                self._table.setItem(row, col, QTableWidgetItem(col_text))
                col += 1
            col = select_index.column()
            row += 1
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self._table_obj.colorize_rows(range(row - len(text_list), row))
        self.save_history_data()

    def _format_data(self, data):
        if isinstance(data, list):
            return os.linesep.join('\t'.join(row) for row in data)
        if isinstance(data, str):
            return data
        return None

    def _set_cut(self):
        self._cut_action = QAction('Cut')
        self._cut_action.setShortcut('Ctrl+X')
        self._cut_action.triggered.connect(self._cut)
        self._table.addAction(self._cut_action)

    def _cut(self):
        self._copy()
        self._delete()

    def save_history_data(self):
        item = str(UIRepository().find('project_tree_items')[-1])
        rows = self._table.rowCount()
        cols = self._table.columnCount()
        texts = []
        for i in range(rows):
            row_text = []
            for j in range(cols):
                index = self._table.item(i, j)
                text = index.text() if index else ''
                row_text.append(text)
            texts.append(row_text)
        if self._format_data(texts) != self._undo_text.get(item, ' ')[-1]:
            self._undo_text.setdefault(item, []).append(self._format_data(texts))

    def _undo(self):
        item = str(UIRepository().find('project_tree_items')[-1])
        if len(self._undo_text.get(item, [])) > 1:
            self._redo_text.setdefault(item, []).append(self._undo_text[item].pop())
            text = self._undo_text.get(item)[-1]
            self._set_item(text)

    def _redo(self):
        item = str(UIRepository().find('project_tree_items')[-1])
        if self._redo_text.get(item):
            text = self._redo_text.get(item).pop()
            self._undo_text.get(item).append(text)
            self._set_item(text)

    def _insert_extra_row_col(self, text_list, row, col):
        rows = self._table.rowCount()
        cols = self._table.columnCount()
        need_row, need_col = 0, 0
        if text_list:
            need_row = len(text_list)
            need_col = len(text_list[0]) if text_list[0] else 0
        for i in range(rows - 1, need_row + row):
            self._table.insertRow(i)
        for j in range(cols - 1, need_col + col):
            self._table.insertColumn(j)

    def _set_item(self, text):
        self._table.clear()
        text_list = [line.split('\t') for line in text.splitlines()]
        row = 0
        self._table.blockSignals(True)
        for line in text_list:
            Colorizer().colorize(self, row, self._remove_end_null_value(line))
            row = row + 1
        self._table.blockSignals(False)
        self._table_obj._modify_data()

    def _set_comment_rows(self):
        self._comment_rows_action = QAction('Comment Rows')
        self._comment_rows_action.setShortcut('Ctrl+3')
        self._comment_rows_action.triggered.connect(self._comment_rows)
        self._table.addAction(self._comment_rows_action)

    def _comment_rows(self):
        row_num, _ = self._get_select_row_col()
        self._table.blockSignals(True)
        for row in row_num:
            cols = self._table.columnCount()
            texts = self._get_select_row_texts(row, cols)
            if texts[0] == '\\':
                texts.insert(1, 'Comment')
            else:
                texts.insert(0, 'Comment')
            new_texts = self._remove_end_null_value(texts)
            Colorizer().colorize(self, row, new_texts)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _set_uncomment_rows(self):
        self._uncomment_rows_action = QAction('Uncomment Rows')
        self._uncomment_rows_action.setShortcut('Ctrl+4')
        self._uncomment_rows_action.triggered.connect(self._uncomment_rows)
        self._table.addAction(self._uncomment_rows_action)

    def _uncomment_rows(self):
        row_num, _ = self._get_select_row_col()
        self._table.blockSignals(True)
        for row in row_num:
            cols = self._table.columnCount()
            texts = self._get_select_row_texts(row, cols)
            if texts[0] == 'Comment' or texts[1] == 'Comment':
                texts.pop(0) if texts[0] == 'Comment' else texts.pop(1)
                Colorizer().colorize(self, row, texts)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _set_make_variable(self):
        self._make_variable_action = QAction('Make Variable')
        self._make_variable_action.setShortcut('Ctrl+1')
        self._make_variable_action.triggered.connect(self._make_variable)
        self._table.addAction(self._make_variable_action)

    def _make_variable(self):
        self._generate_variable('$')
        self.save_history_data()

    def _set_make_list_variable(self):
        self._list_variable_action = QAction('Make List Variable')
        self._list_variable_action.setShortcut('Ctrl+2')
        self._list_variable_action.triggered.connect(self._make_list_variable)
        self._table.addAction(self._list_variable_action)

    def _make_list_variable(self):
        self._generate_variable('@')
        self.save_history_data()

    def _generate_variable(self, types):
        indexs = self._table.selectedIndexes()
        row = indexs[0].row()
        col = indexs[0].column()
        try:
            self._set_edited_status_variable(row, col, types)
        except:
            self._set_variable(row, col, types)
#         self._table_obj.colorize_rows([row])

    def _set_edited_status_variable(self, row, col, types):
        line = self._table.itemDelegate().editor
        text = line.text()
        start = line.selectionStart()
        end = line.selectionEnd()
        back_num = 1
        if line.hasSelectedText():
            text = text[:start] + '%s{' % types + text[start:end] + '}' + text[end:]
            back_num = len(text) - end - 2
        else:
            text = text[:line.cursorPosition()] + '%s{}' % types + text[line.cursorPosition():]
        self._table.setItem(row, col, QTableWidgetItem(text))
        self._table_obj.colorize_rows([row])
        line.setText(text)
        line.cursorBackward(False, back_num)
        line.cursorBackward(True, end - start)

    def _set_variable(self, row, col, types):
        text = ''
        if self._table.item(row, col):
            text = self._table.item(row, col).text()
        self._table.setItem(row, col, QTableWidgetItem('%s{' % types + text + '}'))
        self._table_obj.colorize_rows([row])
        self._table.editItem(self._table.item(row, col))
        line = self._table.itemDelegate().editor
        line.setText('%s{' % types + text + '}')
        line.cursorBackward(False, 1)
        line.cursorBackward(True, len(text))

    def _set_move_up(self):
        self._move_up_action = QAction('Move Up')
        self._move_up_action.setShortcut('Alt+Up')
        self._move_up_action.triggered.connect(self._move_up)
        self._table.addAction(self._move_up_action)

    def _move_up(self):
        row = self._table.selectedIndexes()[0].row()
        col = self._table.selectedIndexes()[0].column()
        if row == 0:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row, cols)
        self._table.blockSignals(True)
        row = row - 1
        for line in [prev_row_data, row_data]:
            Colorizer().colorize(self, row, line)
            row = row + 1
        self._table.setCurrentCell(row - 2, col)
        self._table.blockSignals(False)
        self._table_obj._modify_data()
        self.save_history_data()

    def _get_two_row_data(self, row, cols):
        row_data = []
        prev_row_data = []
        for c in range(cols):
            if self._table.item(row, c):
                row_data.append(self._table.item(row, c).text())
            else:
                row_data.append('')
            if self._table.item(row - 1, c):
                prev_row_data.append(self._table.item(row - 1, c).text())
            else:
                prev_row_data.append('')
        return prev_row_data, row_data

    def _set_move_down(self):
        self._move_down_action = QAction('Move Down')
        self._move_down_action.setShortcut('Alt+Down')
        self._move_down_action.triggered.connect(self._move_down)
        self._table.addAction(self._move_down_action)

    def _move_down(self):
        row = self._table.selectedIndexes()[0].row()
        col = self._table.selectedIndexes()[0].column()
        if row == self._table.rowCount() - 1:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row + 1, cols)
        self._table.blockSignals(True)
        for line in [prev_row_data, row_data]:
            Colorizer().colorize(self, row, line)
            row = row + 1
        self._table.blockSignals(False)
        self._table.setCurrentCell(row - 1, col)
        self._table_obj._modify_data()
        self.save_history_data()
