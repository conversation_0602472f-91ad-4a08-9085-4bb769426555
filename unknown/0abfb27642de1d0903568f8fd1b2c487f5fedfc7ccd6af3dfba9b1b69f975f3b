'''
Created on 2019年11月11日

@author: 10243352
'''
import inspect
import re

from robot.libraries import STDLIBS
from robot.utils.importer import Importer

from controller.parser.rules.CommParsedRule import PY_2_RF_VALUE
from model.data_file.KeyWords import KeyWord
from model.data_file.Repository import BuildInKeyWordRepository


class RfStdLibs(object):

    def __init__(self, repo=BuildInKeyWordRepository(), store_with_prefix=False):
        self._repo = repo
        self._store_with_prefix = store_with_prefix

    def parse(self, name):
        self._init_build_in_libs(name)

    def _init_build_in_libs(self, name):
        self._init_build_in_keywords(name)

    def _init_build_in_keywords(self, name):
        import_name = 'robot.libraries.' + name
        importer = Importer('test library')
        libcode, _ = importer.import_class_or_module(import_name, return_source=True)
        keywords = filter(lambda keyword:
                          inspect.isfunction(getattr(libcode, keyword)) and
                          not keyword.startswith("_"), dir(libcode))
        for keyword in keywords:
            self._init_build_in_keyword(name, libcode, keyword)

    def _init_build_in_keyword(self, source, libcode, name):
        doc = getattr(libcode, name).__doc__
        arguments = self._get_arguments(libcode, name)
        name = " ".join([tmp for tmp in name.split("_")])
        keyword = KeyWord(name)
        keyword.path = source
        keyword.modify("documentation", str(doc))
        keyword._modify_arguments([arguments, ""], is_clear_repo=False)
        self._repo.add(name, keyword)
        if self._store_with_prefix:
            self._repo.add(".".join([source.lower(), name]), keyword)

    def _get_arguments(self, libcode, name):
        arg_spec = inspect.getargspec(getattr(libcode, name))
        args = arg_spec.args
        defaults = arg_spec.defaults
        if defaults:
            for index in range(len(defaults)):
                args_index = 0 - (len(defaults) - index)
                args[args_index] += "=" + str(self._transfer_python_to_rf(defaults[index]))
        args = args[1:] if len(args) > 0 and str(args[0]) == "self" else args
        rf_args = " | ".join(args)
        rf_args = rf_args + " | ${varargs}=${varargs}" if arg_spec.varargs else rf_args
        rf_args = rf_args + " | ${keywords}=${keywords}" if arg_spec.keywords else rf_args
        rf_args = rf_args.lstrip(" | ")
        return rf_args

    def _transfer_python_to_rf(self, py_value):
        if isinstance(py_value, str):
            spaces = re.findall("^\s+$", py_value)
            if len(spaces) != 0:
                return PY_2_RF_VALUE.get(" ") * len(spaces)
        if isinstance(py_value, int):
            return "${" + str(py_value) + "}"
        if py_value in PY_2_RF_VALUE.keys():
            return PY_2_RF_VALUE[py_value]
        return py_value


if __name__ == "__main__":
    RfStdLibs(store_with_prefix=True).parse("BuiltIn")
    print(BuildInKeyWordRepository().keys())
    print(len(BuildInKeyWordRepository().query("Log").keys()), BuildInKeyWordRepository().query("BuiltIn.call method").keys())
