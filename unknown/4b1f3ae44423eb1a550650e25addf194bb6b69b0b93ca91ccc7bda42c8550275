  # -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from controller.system_plugin.rf_assistant.RF_Helper.ui.DiffTextEdit import DiffTextEdit
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiS<PERSON><PERSON><PERSON><PERSON>
from controller.system_plugin.rf_assistant.RF_Helper.Thread.AiThreadSSP import AiThreadSSP
    
TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'


class TabSSP(QWidget):    
    def __init__(self, parent=None, para={}):
        super().__init__()
        self.parent = parent
        self.para = para
        self.initTabUi()

    def initTabUi(self):
        layout = QVBoxLayout()
        self.queryWidget5 = QWidget()
        self.testcaseNameLabel5 = QLabel('用例名称：')
        self.testcaseStepEdit5 = DiffTextEdit()
        self.rfEdit5 = DiffTextEdit()
        self.rfEdit5.setWordWrapMode(QTextOption.NoWrap)
        self.queryLabel5 = QLabel('用例标识：')
        self.queryEdit5 = QLineEdit('SAC-398909')
        self.queryEdit5.setPlaceholderText("标识或用例标识")
        self.queryButton5 = QPushButton("查找用例信息")
        self.startButton5 = QPushButton("开始生成")
        self.startButton5.clicked.connect(self.on_start_button_clicked)
        self.queryButton5.clicked.connect(self.on_query_button_clicked)
        grid = QGridLayout()
        grid.setSpacing(5)
        grid.addWidget(self.queryLabel5,0,0,1,1)
        grid.addWidget(self.queryEdit5,0,1,1,1)
        grid.addWidget(self.queryButton5,0,2,1,1)
        
        grid.addWidget(self.testcaseStepEdit5,5,0,1,3)
        grid.addWidget(self.startButton5,7,0,1,3)
        
        self.queryWidget5.setLayout(grid)
        self.splitter5 = QSplitter(Qt.Horizontal, self)
        self.splitter5.addWidget(self.queryWidget5)
        self.splitter5.addWidget(self.rfEdit5)
        self.splitter5.setSizes([self.para.get('width') * 2 // 5, self.para.get('width') * 3 // 5])
        layout.addWidget(self.splitter5)
        self.setLayout(layout)

    def _on_query_button_clicked(self):
        self.rfEdit5.setText('')
        text = self.queryEdit5.text()
        print(text)
        if not text:
            self.rfEdit5.setText('未查到用例信息')
            return
        caseIdList = text.split(',')
        text = ''
        for caseId in caseIdList:
            print(caseId)
            case_info = self.parent.caseInfoDic.get(caseId)
            if not case_info:
                case_info = self.parent.caseInfoDic0.get(caseId)
            print(case_info)
            if not case_info:
                text = '用例脚本未找到'
                self.rfEdit5.setPlainText(text)
                return
            text = text + 'RDC路径：' + case_info.get('rdcPath', '') + '\n'
            if case_info.get('file_path'):
                path = case_info.get('file_path').split(self.parent.testPathSplitMap.get(self.parent.project, ' '))[1]
                text = text + '脚本路径：' + path +'\n\n'
            
            text = text + '用例脚本：\n'    
            text = text + '*Settings*\n'
            if case_info.get('suite_setup'):
                text = text + 'Suite Setup    ' + case_info.get('suite_setup', []) + '\n'
            if case_info.get('suite_teardown'):
                text = text + 'Suite Teardown    ' + case_info.get('suite_teardown', []) + '\n'
            for v in case_info.get('variables', []):
                text = text + v + '\n'
            for r in case_info.get('resource', []):
                text = text + r + '\n'
            text = text + r + '\n\n'
            text = text + '*Test Cases*' + '\n'
            text = text + case_info.get('name', []) + '\n'
            for c in case_info.get('case_content', []):
                text = text + c + '\n'
            text = text + '\n'
        texts = text.split('\n')
        self.rfEdit5.set_colored_text_test_case(texts)

    def on_query_button_clicked(self):
        db = MongoDb3()
        text = self.queryEdit5.text()
        case_info = db.query({'id': text}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            case_info = db.query({'id0': text}, TEST_CASE_INFO_DB_TABLE)
        self.startButton5.setEnabled(True)
        self.testcaseStepEdit5.setText('查找中。。。')
        if not text:
            self.testcaseStepEdit5.setText('未查到用例信息')
            return
        testName, testStep, testPath = self.parent.get_rdc_test_info(text)
        self.testcasePath = testPath
        if testStep:
            testSteps = testStep.split('\n')
            self.testcaseStepEdit5.setText('')
            self.testcaseStepEdit5.set_colored_text_rdc_step(testSteps)
        else:
            systemId = self.parent.get_rdc_system_id(text)
            if not systemId:
                systemId = self.parent.get_rdc_system_id('TFS_WX_' + text)
            if systemId:
                testName, testStep, testPath = self.parent.get_rdc_test_info(systemId)
                if testStep:
                    testSteps = testStep.split('\n')
                    self.testcaseStepEdit5.setText('')
                    self.testcaseStepEdit5.set_colored_text_rdc_step(testSteps)
                else:
                    self.testcaseStepEdit5.setText('未查到用例信息') 
            else:
                self.testcaseStepEdit5.setText('未查到用例信息')
        self.parent.statusBar.showMessage(testPath, 10000)
        self._on_query_button_clicked()

    def on_start_button_clicked(self):
        self.start_genarate_rf()

    def start_genarate_rf(self):
        self.parent.statusBar.showMessage('正在生成脚本，这可能花费几分钟时间，请耐心等待...', 120000)
        self.rfEdit5.clear()
        self.startButton5.setEnabled(False)
        self.threadAi = AiThreadSSP(self.parent)
        self.threadAi.rf_finished_ssp.connect(self.on_genarate_rf_finish)
        self.threadAi.daemon = True
        self.threadAi.start()

    def on_genarate_rf_finish(self, text):
        print('生成脚本完毕。')
        print(text)
        self.startButton5.setEnabled(True)
        if 'AuthFailed' in text:
            text = text + '\n' + 'DN Studio认证失败，请重新登录账号。'
        self.parent.statusBar.showMessage('生成脚本完毕。', 10000)
        self.rfEdit5.clear()
        self.rfEdit5.set_colored_text_test_case(text.split('\n'))