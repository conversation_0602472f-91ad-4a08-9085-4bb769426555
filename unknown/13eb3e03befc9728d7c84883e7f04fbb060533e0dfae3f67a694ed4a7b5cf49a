# encoding=utf-8
'''
Created on 2020年3月3日

@author: 10247557
'''
import socket
import uuid
import platform


class UserInfo():

    def get_pc_name(self):
        return socket.getfqdn(socket.gethostname())

    def get_pc_ip(self):
        pcName = self.get_pc_name()
        return socket.gethostbyname(pcName)

    def get_mac_address(self):
        mac = uuid.UUID(int=uuid.getnode()).hex[-12:]
        return ":".join([mac[e:e+2] for e in range(0, 11, 2)])

    def get_operator_system_type(self):
        return platform.system()


if __name__ == "__main__":
#     userInfo = UserInfo()
#     print (userInfo.get_pc_name())
#     print (userInfo.get_pc_ip())
#     print (userInfo.get_mac_address())
#     print (userInfo.get_operator_system_type())

    import requests, json
    url = 'http://**************:3346/userinfo'
    data = {'key1':'value1','key2':'value2'}
    r =requests.post(url, data)
    print(r)
    print(r.text)
    print(r.content)