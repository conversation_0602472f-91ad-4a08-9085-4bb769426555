master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内圆形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69772
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内圆形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69753
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内圆形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69750
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内矩形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69745
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内矩形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69764
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内矩形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69761
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内多边形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69770
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内多边形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69771
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性矩形区域UR内多边形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69760
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内圆形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69748
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性R和UR的区域（含矩形、多边形、圆形），且Prru算法属性包括D0、D1、D2，配置保存且同步成功__曹清华10086910__null__MEC-72835
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内多边形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69766
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内矩形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69758
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内圆形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69752
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内多边形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69757
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内矩形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69765
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内圆形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69744
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性矩形区域UR内多边形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69747
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性R和UR的区域（含矩形、多边形、圆形），且Prru算法属性包括D0、D1、D2，配置保存且同步成功__曹清华10086910__null__MEC-72829
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内多边形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69751
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内圆形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-72826
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内矩形区域R1-Prru算法属性D2的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69769
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内圆形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69743
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内多边形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69746
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内矩形区域R1-Prru算法属性D1的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69754
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内圆形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69756
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内多边形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69768
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性矩形区域UR内矩形区域R1-Prru算法属性D0的MO配置，配置保存且同步成功__曹清华10086910__null__MEC-69763
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性R和UR的区域（含矩形、多边形、圆形），且Prru算法属性包括D0、D1、D2，配置保存且同步成功__曹清华10086910__null__MEC-72837
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】NE复位恢复后地图属性和Prru算法属性加载正常__曹清华10086910__null__MEC-72828
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】algo容器复位恢复后地图属性和Prru算法属性加载正常__曹清华10086910__null__MEC-72827
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性区域UR与属性区域R部分重叠的MO配置__曹清华10086910__null__MEC-76263
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】删除地图属性区域UR1与属性区域R1部分重叠的MO配置__曹清华10086910__null__MEC-76266
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】6个TRP，有孤岛，网管配置6个trp均为0维属性(用圆绘制trp属性)，验证解算结果按照0维解算走单站算法，取trp中rsrp大的那个__陈保霖10222411__null__MEC-71383
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】6个TRP，有孤岛，其中4个trp一组，另外2个trp一组，打桩强制开启一字型，解算位置稳定(30秒均是同一个位置)，打桩关闭一字型，网管配置4个trp均为一字型属性(用线段绘制trp属性)，验证配置下发后按照一字型解算，解算位置和打桩开启的一致__陈保霖10222411__null__MEC-71382
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】4个TRP，线长一致，使用trp位于正方形四个角(1个trp在原点、2个在坐标轴上)、反推trp坐标配置(假定得到四个trp坐标为(x,0,0),(0,x,0),(x,x,0),(0,0,0))，网管配有地图不可达区域和trp算法属性，解算位置预期应稳定在(x/2,y/2,1.5)附近3m，变更基站侧TRP的坐标为原坐标2倍(即(2x,0,0),(0,2x,0),(2x,2x,0),(0,0,0))，触发trp响应，指纹库根据新的trp坐标重新生成，解算UE位置为(x,x,1.5)附近3m__陈保霖10222411__null__MEC-71385
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】4个TRP，无孤岛，解算位置稳定(30秒均是同一个位置)，网管配置此位置为圆心、半径为2m的圆为不可达区域UR，网管配置4个trp均为二维属性(用多边形绘制trp属性)，验证配置下发后解算位置不在此区域内__陈保霖10222411__null__MEC-71380
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】4个TRP，网管配有地图不可达区域和trp算法属性，解算位置稳定(x,y,1.5)，变更定位地图的长、宽为原来的2倍，配置下发后，指纹库根据新的地图生成，解算位置不变。打桩可看到对应地图的指纹库包含的地图坐标范围扩大，为原来的2倍。__陈保霖10222411__null__MEC-71384
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【DT】4个TRP，无孤岛，解算位置稳定(30秒均是同一个位置)，网管配置此位置为圆心、半径为4m的圆为可达区域R1、网管配置此位置为圆心、半径为8m的圆为不可达区域UR，trp算法属性不配置，验证配置下发后解算位置在此区域内__陈保霖10222411__null__MEC-71381
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;支持V2X服务机制__（DT）域名配置下，异常复位场景测试__杨龙10306822__null__MEC-71372
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;vRSU支持与OBU对接__（DT）OBU与vRSU上下行消息发送，OBU与vRSU使用UDP/MQTT通信方式__董霞00190042__null__MEC-66694
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;高精度__(DT)小区内切换测试，时延测试__杨龙10306822__null__MEC-67046
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;eFS服务支持路侧感知信息__（DT）eVS检测流程验证__董霞00190042__null__MEC-66691
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;efs网管参数配置__（DT）异常复位场景测试__杨龙10306822__null__MEC-68239
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;efs网管参数配置__（DT）配置网管摄像头、全局参数及Vlan/IP参数生效验证，时延测试__杨龙10306822__null__MEC-67045
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;支持交通流量特征识别__(DT)异常复位场景测试__杨龙10306822__null__MEC-80220
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;vRSU支持与OBU对接__（DT）异常复位、容器重启验证__董霞00190042__null__MEC-66697
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;多摄像头融合__（DT)异常复位场景测试__杨龙10306822__null__MEC-84595
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;大消息__(DT)满配下，异常复位场景测试__杨龙10306822__null__MEC-77609
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;远处目标识别优化__（DT远端目标识别优化复位场景测试__杨龙10306822__null__MEC-96731
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;远处目标识别优化__(DT)远端目标识别优化后时延对比验证__杨龙10306822__null__MEC-96747
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;12路摄像头__（DT）12路摄像头异常复位场景测试__杨龙10306822__null__MEC-76706
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;VGCd2板支持L2卡__（DT）L2卡-12路摄像头异常复位场景测试__杨龙10306822__null__MEC-84594
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;VGCd2板支持L2卡__（DT）L2卡—OBU与vRSU上下行消息发送，OBU与vRSU使用UDP/MQTT通信方式，时延测试__杨龙10306822__null__MEC-88095
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【车联网】多路口绿波引导__异常复位场景测试（DT）__李俊龙10254415__null__MEC-120645
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;RSI交通事件按区域精确分发__(DT)异常复位场景测试__李俊龙10254415__null__MEC-114730
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;RSI交通事件按区域精确分发__（DT）修改xml配置，变更多条moid-eventCode映射关系，导入配置__李俊龙10254415__null__MEC-114738
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;支持通感一体+摄像头的雷视融合方案__场景1：车联网应用场景下，输出视频感知，时延验证__李俊龙10254415__null__MEC-132127
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;vRSU支持与OBU对接__（DT）站内同频切换测试，时延测试__董霞00190042__null__MEC-66695
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;vRSU支持与OBU对接__（DT）站间异频切换测试，时延测试__董霞00190042__null__MEC-66696
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;端到端时延log保存__（DT）异常复位、容器重启日志不会清除验证__杨龙10306822__null__MEC-69608
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;端到端时延log保存__（DT）日志导出验证__杨龙10306822__null__MEC-69602
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;端到端时延log保存__（DT）log日志反复启动、停止时延测试__杨龙10306822__null__MEC-69598
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【MEC-128538】【车联网】支持跨vRSU节点的V2X消息精确分发】__NE间覆盖重叠率为零验证__李俊龙10254415__null__MEC-141365
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【MEC-128538】【车联网】支持跨vRSU节点的V2X消息精确分发】__NE间覆盖重叠率为1验证__李俊龙10254415__null__MEC-141416
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【MEC-69158】【【车联网】支持感知处理的消息分发业务连续性】;业务用例__邻区覆盖范围保存及变更__鲁楷锋10243321__null__MEC-143256
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【MEC-69158】【【车联网】支持感知处理的消息分发业务连续性】;业务用例__单邻区链路建立及修改__鲁楷锋10243321__null__MEC-143254
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;【MEC-69158】【【车联网】支持感知处理的消息分发业务连续性】;业务用例__多邻区的建立及修改__鲁楷锋10243321__null__MEC-143255
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;测量流程__【DT】Qcell定位机型，定位技术为UTDOA，测量请求中下发测量类型验证__刘永10033173__null__MEC-107107
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;测量流程__【DT】QCELL机型，测量响应消息和测量报告中MeasurementPrivateInfoContainer获取到groupid验证__刘永10033173__null__MEC-98556
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;测量流程__【DT】64T机型，测量响应消息和测量报告中获取到groupid和cluster验证__刘永10033173__null__MEC-108119
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;TRP增量化处理__【DT】QCELL站，TRP数目有变化，NE重新生成指纹库__刘永10033173__null__MEC-96555
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;TRP增量化处理__【DT】QCELL站，TRP信息有变化，NE重新生成指纹库__刘永10033173__null__MEC-96560
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【DT】QcellConfig容器上电恢复测试__曹清华10086910__null__MEC-126585
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【DT】QcellInternalApi容器上电恢复测试__曹清华10086910__null__MEC-126589
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【DT】QcellLBSProcess容器上电恢复测试__曹清华10086910__null__MEC-126581
master;NodeEngine测试用例集;定位特性;2023NE定位重构;NE产品去TCFS依赖__【DT】Algo容器log打印功能测试__曹清华10086910__null__MEC-83661
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【DT】地图转换矩阵元素取值范围测试__曹清华10086910__null__MEC-102511
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【DT】修改QcellMapInfo的coordinateTransformMode与/或coordTransfControlPoint不触发指纹库更新__曹清华10086910__null__MEC-103193
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【DT】DataParser5G容器重启恢复测试—单NR站无订阅UE__曹清华10086910__null__MEC-129509
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【DT】DataParser5G容器重启恢复测试—两NR站有一个站存在订阅UE__曹清华10086910__null__MEC-129513
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】修改QcellSystemCfg节点positionAlgoLevel字段取值重新触发定位【1->0】__曹清华10086910__null__MEC-130108
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】修改QcellSystemCfg节点positionAlgoLevel字段取值重新触发定位【0->1】__曹清华10086910__null__MEC-130107
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】新增网元开通UTDOA定位服务QcellSystemCfg节点positionAlgoLevel字段的处理__曹清华10086910__null__MEC-130181
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】TRP/ARP ID级别定位算法支持站内小区间移动时保持Periodic定位__曹清华10086910__null__MEC-130496
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】修改NE网元定位模式positionAlgoLevel触发TRP查询流程（坐标相等TRP数量大于5个）__曹清华10086910__null__MEC-131640
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】TRP/ARP ID级别定位算法支持对终端进行OnDemand定位__曹清华10086910__null__MEC-126654
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】TRP/ARP ID级别定位算法支持对终端进行Periodic定位__曹清华10086910__null__MEC-137345
master;NodeEngine测试用例集;定位特性;70-UTDOA 0维定位;定位流程__【DT】修改NE网元定位模式positionAlgoLevel触发TRP查询流程（坐标相等TRP数量小于等于5个）__曹清华10086910__null__MEC-137952
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;坐标转换__【DT】新增QcellMapInfo配置的coordinateTransformMode为非0时coordTransfControlPoint字段参数生效规则测试__曹清华10086910__null__MEC-146523
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】UE定位位置在原R区域内的图形修改地图属性为UR1后定位结果不出现在UR1内__曹清华10086910__null__MEC-146094
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位参数边界值梳理__【DT】QcellMapInfo表的maplen、mapwide 和 mapsize参数取值范围对指纹库生成的影响测试__曹清华10086910__null__MEC-143689
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;TRPAction__【DT】NE支持通过Action触发TRP查询功能—返回成功__曹清华10086910__null__MEC-80847
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;TRPAction__【DT】NE支持通过Action触发TRP查询功能—返回失败__曹清华10086910__null__MEC-88674
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位流程;测量流程__【DT】64T定位机型，定位技术为UTDOA+AOA，测量请求中固定下发三种测量类型验证__刘永10033173__null__MEC-107814
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性区域UR与属性区域R部分重叠的MO配置，指纹库保留重叠区域的指纹__曹清华10086910__null__MEC-76170
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性区域UR1与属性区域R1部分重叠的MO配置，指纹库保留重叠区域的指纹__曹清华10086910__null__MEC-76265
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】修改地图属性区域UR与属性区域R部分重叠的MO配置，指纹库保留重叠区域的指纹__曹清华10086910__null__MEC-76262
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;NE定位支持地图场景识别功能__【DT】新增地图属性区域UR1与属性区域R1部分重叠的MO配置，指纹库保留重叠区域的指纹__曹清华10086910__null__MEC-76264
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位算法;TRP流程__【DT】【MEC-90681】NE对接1个BBU，此BBU同时有64T AOA AAU, UTDOA Qcell,电联共享，对UTDOA Qcell发起测量,能正常解算出结果，且未带起64T AOA AAU__陈保霖10222411__null__MEC-88688
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位算法;TRP流程__【DT】【MEC-90681】NE上对接1个BBU，此BBU同时有64T AOA AAU, UTDOA Qcell,电联共享，对64T AOA AAU发起测量,能正常解算出结果，且未带起Qcell__陈保霖10222411__null__MEC-88687
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;TRP信息获取__UTDOA定位逻辑链路初次建立后立即获取TRP信息__熊择6396001406__null__MEC-64471
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;TRP信息获取__单站单小区短周期TRP信息__熊择6396001406__null__MEC-64468
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;TRP信息获取__NE重启后短周期TRP信息获取__熊择6396001406__null__MEC-64467
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;孤岛测量__孤岛基站同步测量__林玉清00105055__null__MEC-64549
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;孤岛测量__UTDOA基站测量同步孤岛__熊择6396001406__null__MEC-64550
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;小区协同__UTDOA定位命令开启小区协同-存在已发起测量__林玉清00105055__null__MEC-64489
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;小区协同__UTDOA定位命令关闭小区协同-已有测量重新触发__林玉清00105055__null__MEC-64492
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;小区协同__小区协同测试__熊择6396001406__null__MEC-64472
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;小区协同算法__单平面地图-单站多小区协同（单PLMN）-协同小区筛选算法验证-数量、距离、频点、测量上报-同频__林玉清00105055__null__MEC-64349
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;小区协同算法__单平面地图-单站多小区协同（单PLMN）-协同小区筛选算法验证-数量、距离、频点、测量上报-异频__陈保霖10222411__null__MEC-77513
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;小区协同算法__单平面地图-单站多小区协同（单PLMN）-协同小区筛选算法验证-数量、距离、频点、测量上报-同频不共地图__陈保霖10222411__null__MEC-77514
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA 先订阅后查询单个UE__林玉清00105055__null__MEC-64418
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA 先查询后订阅单个UE__林玉清00105055__null__MEC-64417
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA 多个url同时查询单个UE__林玉清00105055__null__MEC-64416
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip订阅（ue被订阅去订阅过）__林玉清00105055__null__MEC-64415
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip查询（ue被订阅去订阅过）__林玉清00105055__null__MEC-64414
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip订阅（ue不存在）__林玉清00105055__null__MEC-64409
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip查询（ue不存在）__林玉清00105055__null__MEC-64408
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip订阅触发定位流程__林玉清00105055__null__MEC-64400
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA按ip单次查询触发定位流程__林玉清00105055__null__MEC-64399
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA 多个url同时订阅单个UE__林玉清00105055__null__MEC-64402
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA取消ip订阅__林玉清00105055__null__MEC-64403
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__UTDOA支持对IPv6地址的UE发起订阅和查询__林玉清00105055__null__MEC-67058
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__多个url订阅多个UE后取消一个订阅__林玉清00105055__null__MEC-64424
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__多个url订阅多个UE后更新一个订阅__林玉清00105055__null__MEC-64425
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__多个url订阅多个UE（UE有重叠）__林玉清00105055__null__MEC-64423
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__多个查询方查询不同UE__林玉清00105055__null__MEC-64422
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__两UE同时接入后先查询后订阅__林玉清00105055__null__MEC-64419
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__UTDOA 一个url同时订阅多个UE（更新）__林玉清00105055__null__MEC-64421
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__UTDOA 一个url同时订阅多个UE（部分UE未接入）__林玉清00105055__null__MEC-64420
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__两UE同时接入后先订阅后查询__林玉清00105055__null__MEC-64406
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__UTDOA 一个url同时订阅多个UE__林玉清00105055__null__MEC-64401
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__两UE同时接入根据ip查询__林玉清00105055__null__MEC-64404
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;多UE UTDOA定位__两UE同时接入根据ip订阅__林玉清00105055__null__MEC-64405
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__【RT】QcellConfig、QcellInternalApi及QcellLBSProcess容器上电恢复测试__曹清华10086910__null__MEC-126605
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;多IP定位__【RT】单UE多IP查询，能够正常触发UTDOA查询__刘永10033173__null__MEC-84106
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;多IP定位__【RT】多UE多IP同时订阅，能够正常触发UTDOA订阅__刘永10033173__null__MEC-84105
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;多IP定位__【RT】三IP查询，其中一个Ip属于一个UE，另外两个不存在__刘永10033173__null__MEC-79848
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;多IP定位__【RT】四IP订阅，两个IP属于同一UE，一个IP属于另外一个UE，一个IP不存在__刘永10033173__null__MEC-79655
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__定位服务对外查询开发接口仅支持单UE__熊择6396001406__null__MEC-64411
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__定位服务对外查询开放接口提供UE实时查询__熊择6396001406__null__MEC-64410
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;Rnis交互__RNIS查询UE IP上报PDU Session ID信息__林玉清00105055__null__MEC-64466
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;RSRP上报__UTDOA ondemand定位上报RSRP值__林玉清00105055__null__MEC-64475
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;RSRP上报__UTDOA 周期订阅定位上报RSRP值__林玉清00105055__null__MEC-64476
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;SRS响应__UTDOA-SRS资源回收__林玉清00105055__null__MEC-64465
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;时延统计__定位时延统计统计信息输出__林玉清00105055__null__MEC-64502
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;时延统计__定位过程中测量过程的时延统计__林玉清00105055__null__MEC-64500
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;时延统计__定位过程中SRS过程的时延统计__林玉清00105055__null__MEC-64499
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;时延统计__定位过程中RNIS之间交互的时延统计__林玉清00105055__null__MEC-64498
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;时延统计__定位Ondemand场景接收到定位请求到输出位置信息的时延统计__林玉清00105055__null__MEC-64501
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;测量流程__UTDOA定位网管配置测量周期__林玉清00105055__null__MEC-64484
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;测量流程__UTDOA定位网管配置测量周期-已有测量重新触发__林玉清00105055__null__MEC-64486
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;测量流程__UTDOA定位网管配置测量周期-存在已发起测量__林玉清00105055__null__MEC-64485
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;卡尔曼滤波__支持通过命令动态修改卡尔曼滤波系数__林玉清00105055__null__MEC-64570
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;卡尔曼滤波__不同测量周期下动态修改卡尔曼滤波系数__林玉清00105055__null__MEC-64571
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__(真实环境)UTDOA定位流程-单个RRU计算定位结果-孤岛和非孤岛场景合一__林玉清00105055__null__MEC-64567
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__(真实环境)UTDOA定位流程-两个RRU计算定位结果-孤岛和非孤岛场景__林玉清00105055__null__MEC-64568
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__(真实环境)UTDOA定位流程-三个或三个以上RRU计算定位结果-孤岛和非孤岛场景__林玉清00105055__null__MEC-64569
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位协议和方法约束__MECSAP协议约束__林玉清00105055__null__MEC-64722
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位协议和方法约束__NRPPA协议约束__林玉清00105055__null__MEC-64721
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 定位地图批量配置__林玉清00105055__null__MEC-64733
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 定位引擎参数批量配置__林玉清00105055__null__MEC-64734
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 基站基本信息批量配置__林玉清00105055__null__MEC-64735
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 基站上的小区信息批量配置__林玉清00105055__null__MEC-64736
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell RRU信息批量配置__林玉清00105055__null__MEC-64732
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell ECID权重算法批量配置__林玉清00105055__null__MEC-64731
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell sctp偶联批量配置__林玉清00105055__null__MEC-64739
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell ECID权重算法批量删除__林玉清00105055__null__MEC-64748
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell RRU信息批量删除__林玉清00105055__null__MEC-64749
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 基站上的小区信息批量删除__林玉清00105055__null__MEC-64753
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 基站基本信息批量删除__林玉清00105055__null__MEC-64752
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 定位引擎参数批量删除__林玉清00105055__null__MEC-64751
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 定位地图批量删除__林玉清00105055__null__MEC-64750
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell sctp偶联批量删除__林玉清00105055__null__MEC-64755
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 默认引擎参数批量配置__林玉清00105055__null__MEC-64738
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表容量测试__qcell 默认引擎参数批量删除__林玉清00105055__null__MEC-64754
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell定位地图配置__朱磊10265044__null__MEC-64727
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell定位引擎参数配置__朱磊10265044__null__MEC-64728
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell默认引擎参数配置__朱磊10265044__null__MEC-64726
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell基站基本信息配置__朱磊10265044__null__MEC-64725
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell基站上的小区信息配置__朱磊10265044__null__MEC-64729
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell RRU信息配置__朱磊10265044__null__MEC-64730
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell ECID权重算法配置__林玉清00105055__null__MEC-64724
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__sctp偶联配置__朱磊10265044__null__MEC-64737
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell ECID权重算法删除__林玉清00105055__null__MEC-64740
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell RRU信息删除__林玉清00105055__null__MEC-64741
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell 基站上的小区信息删除__林玉清00105055__null__MEC-64745
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell 基站基本信息删除__林玉清00105055__null__MEC-64744
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell 默认引擎参数删除__林玉清00105055__null__MEC-64746
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell 定位引擎参数删除__林玉清00105055__null__MEC-64742
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell 定位地图删除__林玉清00105055__null__MEC-64743
master;NodeEngine测试用例集;定位特性;01-M基本功能;M03-定位配置测试;定位表配置__qcell sctp偶联删除__林玉清00105055__null__MEC-64747
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】指纹库融合算法，网管配置指纹模式为"采用自动生成和人工采集融合的指纹库",人工、自动指纹边界打点验证__陈保霖10222411__null__MEC-87586
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】指纹库融合算法，网管配置指纹模式在"采用自动生成和人工采集融合的指纹库"、"采用自动生成的指纹库"、"采用人工采集的指纹库"之间切换，验证切换成功，解算正确__陈保霖10222411__null__MEC-87587
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】【自动化差分实现】多map下的指纹融合测试：2个map，人工自动指纹融合模式，融合后的指纹库正确，解算结果正确__陈保霖10222411__null__MEC-95355
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】【自动化差分实现】NE reboot测试：多map下的指纹融合测试：2个map，人工自动指纹融合模式，融合后的指纹库正确，解算结果正确__陈保霖10222411__null__MEC-95425
master;NodeEngine测试用例集;定位特性;20-宏站UTDOA+AOA融合定位;定位算法;定位流程的模拟测试;TRP增量化处理__【RT】TRP信息有变化，指纹或POS有更新__刘永10033173__null__MEC-88675
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】14个TRP，13个分布在半径小于20*3=60m的圆内，校验圆心的网格点trp个数为12__陈保霖10222411__null__MEC-123868
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】构造测量报告，测量报告里的TRP个数等于待匹配网格点的TRP个数，解算结果正确__陈保霖10222411__null__MEC-123870
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】构造测量报告，测量报告里的TRP个数小于待匹配网格点的TRP个数，解算结果正确__陈保霖10222411__null__MEC-123871
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】构造测量报告，测量报告里的TRP个数大于待匹配网格点的TRP个数，解算结果正确__陈保霖10222411__null__MEC-123869
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】14个TRP 均匀分布在地图上，敲桩开启减小维度，校验整张地图上的所有指纹点有按照距离减小空间维度生成(校验点)；敲桩关闭减小维度开关，指纹点恢复默认维度(校验点)__陈保霖10222411__null__MEC-123861
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;NE定位算法内存优化__【RT】4个TRP 均匀分布在地图上，校验整张地图上的所有指纹点没有按照距离减小空间维度生成__陈保霖10222411__null__MEC-123872
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，不可达UR内有可达R1，构造走三站以上的孤岛解算算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状遍历多边形、矩形、圆形，两区域形状两两交叉。__陈保霖10222411__null__MEC-71397
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，可达R内有不可达UR1，构造走三站以上的正常解算算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状遍历多边形、矩形、圆形，两区域形状两两交叉。__陈保霖10222411__null__MEC-71398
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，可达R内有不可达UR1，构造走三站以上的孤岛解算算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状遍历多边形、矩形、圆形，两区域形状两两交叉。__陈保霖10222411__null__MEC-71386
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，不可达UR内有可达R1，构造走三站以上的正常解算算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状遍历多边形、矩形、圆形，两区域形状两两交叉。__陈保霖10222411__null__MEC-71399
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，可达R内有不可达UR1，构造走双站算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71400
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，不可达UR内有可达R1，构造走双站算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71401
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，可达R内有不可达UR1，构造走一字型算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71408
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，可达R内有不可达UR1，构造走单站算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71404
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，不可达UR内有可达R1，构造走单站算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71405
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，可达R内有不可达UR1，构造走双站算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71402
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，不可达UR内有可达R1，构造走单站算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71407
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，可达R内有不可达UR1，构造走单站算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71406
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，不可达UR内有可达R1，构造走双站算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71403
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，不可达UR内有可达R1，构造走一字型算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71431
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，可达R内有不可达UR1，构造走一字型算法，UE构造理论位置于R外、R内UR1外、UR1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71442
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，有孤岛，6个TRP全部标注1维，4个一条线段，3个另一条线段，其中有1个trp同时属于2条线段且这个trp按rsrp排序位于rsrp2的位置，地图区域属性需要配置不可达区域(但不验证)，验证最后使用有4条线段的trp进入一字型解算，解算结果正确__陈保霖10222411__null__MEC-71446
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，6个TRP全部标注1维，其中一个trp rsrp为0被过滤，case1，按rsrp降序排序得到{[trp1,rsrp1],[trp2,rsrp2]...}，trp1、trp2都是1维算法trp，case2：过滤非一维trp，剩余一维trp剩5个，trp1属于线段1，trp2属于线段2，trp3属于线段3，线段1上有1个trp，线段2上有1个trp，线段3上有1个trp，剩余trp不属于上述3条线段，进二维算法__陈保霖10222411__null__MEC-71447
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】典型场景验证：算法文档case1区域分流不存在交叠标注图，增加不可达区域，算法切换边界打点验证解算符合预期, 0维PRRU要在地图边界上，验证边界prru可选上__陈保霖10222411__null__MEC-71452
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，无孤岛，不可达UR内有可达R1，构造走一字型算法，UE构造理论位置于UR外、UR内R1外、R1内，解算符合理论预期。地图区域属性形状包含多边形、圆形，不要求交叉。__陈保霖10222411__null__MEC-71443
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】2张地图，每张地图3个TRP，有孤岛，每个地图1个可达区域嵌套19个不可达区域(形状任意)，区域附近打点验证解算位置有跳过__陈保霖10222411__null__MEC-71450
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】2张地图，6个TRP，1张地图2个，另一张地图4个TRP，有孤岛，先配置4个TRP属于地图1，2个属于地图2验证可达生效；再测4个TRP属于地图2,2个属于地图1验证可达生效；每个地图1个不可达区域嵌套19个可达区域(形状任意)，区域附近打点验证解算位置有保留，不可达区域有跳过__陈保霖10222411__null__MEC-71451
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】典型场景验证：算法文档case2区域属性间存在重合交叠的区域，增加不可达区域，可能会对相同prru进行多次标记，算法切换边界打点验证符合预期__陈保霖10222411__null__MEC-71453
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】存在地图区域属性和trp算法属性配置的地图，变更地图区域属性，验证指纹库有重新生成，解算符合预期__陈保霖10222411__null__MEC-71469
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】典型场景验证：算法文档case3小区域多种算法自动切换，增加不可达区域，算法切换边界打点验证解算符合预期__陈保霖10222411__null__MEC-71466
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个trp，trp同时属于5个1维算法属性线段端点，构造ue位于这五条线上的点，验证解算过程和解算位置__陈保霖10222411__null__MEC-71468
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】典型场景验证：算法文档case4可达不可达区域重叠，重叠、非重叠区域选点验证解算符合预期__陈保霖10222411__null__MEC-71467
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】存在地图区域属性和trp算法属性配置的地图，变更trp算法属性，验证解算算法变更__陈保霖10222411__null__MEC-71470
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】存在地图区域属性和trp算法属性配置的地图，变更trp坐标，验证指纹库有重新生成，解算符合预期__陈保霖10222411__null__MEC-71471
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，3个属于1个配有地图区域属性和算法属性的地图，3个属于另一个未配置地图属性的地图，4个属于一个小区，2个属于另一个小区，邻区协同，地图选择进入有地图属性的地图，按照算法属性选择算法__陈保霖10222411__null__MEC-71473
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP，3个属于1个配有地图区域属性和算法属性的地图，3个属于另一个未配置地图属性的地图，4个属于一个小区，2个属于另一个小区，邻区协同，地图选择进入无地图属性的地图，按无算法属性自动判定算法__陈保霖10222411__null__MEC-71474
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】6个TRP、构造含有UR1、R，一维和二维算法属性存在的场景，验证UR1在二维算法属性解算下解算位置偏移出UR1区域，在一维算法属性附近解算位置按照一字型解算。__陈保霖10222411__null__MEC-73177
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，6个TRP，case4,排序靠前的trp1和trp2，rsrp1-rsrp2<15db,trp1具有0维属性，trp2不标记属性，trp3~6具有1维属性，进入1维算法__陈保霖10222411__null__MEC-89538
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，6个TRP，case3,排序靠前的trp1和trp2，rsrp1-rsrp2<15db,trp1具有0维属性，trp1、trp2具有2维属性，其他trp具有1维属性，保留所有trp进入正常2维算法__陈保霖10222411__null__MEC-89527
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，6个TRP，case1,排序靠前的trp1和trp2，同时具有0维和1维算法属性，进入0维算法__陈保霖10222411__null__MEC-89525
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，6个TRP，case2,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有0维属性，trp1、trp2具有1维属性，进入1维算法__陈保霖10222411__null__MEC-89526
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，3个TRP，case2,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有2维属性，trp1、trp2具有1维属性，进入1维算法__陈保霖10222411__null__MEC-85760
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，4个TRP，case1,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有0维属性，trp1、trp2具有1维属性，进入0维算法__陈保霖10222411__null__MEC-81709
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，4个TRP，case3,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有1维属性，trp1、trp2具有2维属性，trp3、trp4不标注属性，进入2维算法__陈保霖10222411__null__MEC-85759
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，3个TRP，case3,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有1维属性，trp1、trp2具有2维属性，trp3不标注属性，进入2维算法__陈保霖10222411__null__MEC-85764
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，4个TRP，case2,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有2维属性，trp1、trp2具有1维属性，进入1维算法__陈保霖10222411__null__MEC-81719
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】每个PRRU支持多个算法属性，3个TRP，case1,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1具有0维属性，trp1、trp2具有1维属性，进入0维算法__陈保霖10222411__null__MEC-81677
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】不标注算法属性的处理，4个TRP，case3,排序靠前的trp1和trp2，rsrp1-rsrp2<15db，trp1不标注属性，trp2具有1维属性，trp3、trp4不标注属性，进入2维算法__陈保霖10222411__null__MEC-81520
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】不标注算法属性的处理，6个TRP，case3,排序靠前的trp1和trp2，rsrp1-rsrp2<15db,trp1具有0维属性，其他trp无属性标注，保留所有trp进入正常2维算法__陈保霖10222411__null__MEC-81690
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图场景识别__【RT】2张地图，每张地图3个TRP，无孤岛，每个地图20个不可达区域(形状任意)，区域附近打点验证解算位置有跳过__陈保霖10222411__null__MEC-71449
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图选择算法类用例__【RT】6个TRP，TRP1~5在地图1，TRP6在地图2，地图1的Em1,地图2Em2,Em1-Em2>150db(敲桩降低测),选择地图1__陈保霖10222411__null__MEC-81659
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图选择算法类用例__【RT】6个TRP，TRP1~3在地图1，TRP4~6在地图2，地图1的Em1,地图2Em2,trp按rsrp排序得到rsrps1(trp6),rsrps2(trp1),rsrps3(trp2),rsrps4(trp5),rsrps5(trp4),rsrps6(trp3),Em1-Em2<=150db,选择地图2__陈保霖10222411__null__MEC-76763
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图选择算法类用例__【RT】6个TRP，TRP1~3在地图1，TRP4~6在地图2，地图1的Em1,地图2Em2,trp按rsrp排序得到rsrps1(trp1),rsrps2(trp2)...,Em1-Em2<=150db，rsrp6-rsrp1<=15,选择地图1__陈保霖10222411__null__MEC-85758
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;地图选择算法类用例__【RT】6个TRP，TRP1~5在地图1，TRP6在地图2，地图1的Em1,地图2Em2,trp按rsrp排序得到rsrps1(trp6),rsrps2(trp1)...,Em1-Em2<=150db,rsrp6-rsrp1>15,选择地图2__陈保霖10222411__null__MEC-81414
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q04-性能测试__【RT】NE支持50个基站SCTP__林玉清00105055__null__MEC-64343
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;测量数据过滤__UTDOA按25db RSRP差值筛选测量结果__林玉清00105055__null__MEC-64493
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;测量数据过滤__UTDOA按nlos筛选测量结果__林玉清00105055__null__MEC-69327
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】rsrp过滤条件，4个TRP在2个分组，分组1的trp全部大于等于-135db，分组2的trp全部小于-135db，经过滤只剩分组1__陈保霖10222411__null__MEC-67478
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】rsrp过滤条件，4个TRP在2个分组，trp全部小于-135db，经过滤只剩所有TRP里按大rsrp、小ta排序排序第一的那个trp__陈保霖10222411__null__MEC-67479
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__(模拟基站)UTDOA定位流程-单个RRU计算定位结果-孤岛和非孤岛场景合一__陈保霖10222411__null__MEC-83669
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__(模拟基站)UTDOA定位流程-两个RRU计算定位结果-孤岛和非孤岛场景__陈保霖10222411__null__MEC-83660
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法，基站上报结果里只有一个组，4个TRP经过rsrpfilter25db后剩余3个，满足最大坐标差门限条件，进一字型算法，case2单组ue在两个trp之间__陈保霖10222411__null__MEC-66224
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法，基站上报结果里有多个组，经过滤后的所有组合并，满足最大角度差门限条件，进一字型算法,只留下rsrp最大的那一组的数据进行定位，case3 ue没有在两个trp之间、在一字型延长线上的靠近原点的一侧__陈保霖10222411__null__MEC-66225
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】孤岛少站退化，基站上报结果里有2个孤岛group，每个group只有1个TRP的RSRP是非0。algo NLOS过滤后没有分组了__林玉清00105055__null__MEC-66025
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法，2个地图，一个地图上TRP一字型部署：基站上报结果里有1个组，满足最大坐标差门限条件，进一字型算法，case3 ue在靠近原点坐标为负的位置；一个地图TRP非一字型部署，走正常解算__陈保霖10222411__null__MEC-66226
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法，算法公式特殊取值__陈保霖10222411__null__MEC-67021
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法开关自动，孤岛多组，case1多组ue在两个TRP之间，ue的rsrp全部<-132db，权重计算看到<-132db组被过滤，解算位置位于排序第一的TRP组之间__陈保霖10222411__null__MEC-67482
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;退化计算__【RT】一字型算法开关自动，case1多组ue在两个TRP之间，有的ue的rsrp>-132db, 有的<-132db，权重计算看到<-132db组被过滤，解算位置位于未被过滤的TRP之间__陈保霖10222411__null__MEC-67481
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景2-url IP 非法__林玉清00105055__null__MEC-67062
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景3-url IP 非法__林玉清00105055__null__MEC-67063
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景4-定位UE IP 不全__林玉清00105055__null__MEC-67064
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景5-定位UE IP非法__林玉清00105055__null__MEC-67065
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景6-定位UE IP非法__林玉清00105055__null__MEC-67066
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景7-存在多个定位UE IP__林玉清00105055__null__MEC-67067
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-订阅接口非法数据测试场景2-url IP 非法__林玉清00105055__null__MEC-67070
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-订阅接口非法数据测试场景3-url IP 非法__林玉清00105055__null__MEC-67071
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-订阅接口非法数据测试场景4-定位UE IP 不全__林玉清00105055__null__MEC-67072
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-查询接口非法数据测试场景5-定位UE IP非法__林玉清00105055__null__MEC-67073
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-订阅接口非法数据测试场景6-定位UE IP非法__林玉清00105055__null__MEC-67074
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;接口相关__第三方接口-订阅接口非法数据测试-UE IP部分缺失__林玉清00105055__null__MEC-64330
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;action命令测试__通过Action查询未建立逻辑链路的基站数__林玉清00105055__null__MEC-64700
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;action命令测试__通过Action列出NE所有指纹定位文件__林玉清00105055__null__MEC-64702
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;action命令测试__通过Action获取位置服务信息__林玉清00105055__null__MEC-64703
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;action命令测试__通过Action获取LBS服务基于EDS的主备状态__林玉清00105055__null__MEC-64704
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;告警测试__动态偶联断链__吕波10128949__null__MEC-64696
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;告警测试__动态偶联建链__吕波10128949__null__MEC-64698
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q01-运维测试;告警测试__sctp上电__吕波10128949__null__MEC-64697
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-定位服务dataparser重启__林玉清00105055__null__MEC-64648
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-定位服务algo重启__林玉清00105055__null__MEC-64652
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-定位服务restful重启__林玉清00105055__null__MEC-64653
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-RNIS服务rnismaster重启__林玉清00105055__null__MEC-64647
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-RNIS服务e2termination重启__林玉清00105055__null__MEC-64654
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-NE重启__林玉清00105055__null__MEC-64655
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-RNIS链路异常恢复__林玉清00105055__null__MEC-64650
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-SCTP链路异常恢复__林玉清00105055__null__MEC-64649
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__E2链路中断更改ue连接状态后恢复链路__林玉清00105055__null__MEC-64651
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位接口;单UE UTDOA定位__按ip订阅后UE飞行再接入__林玉清00105055__null__MEC-64407
master;NodeEngine测试用例集;定位特性;03-Q质量属性;Q03-复位中断异常恢复__UTDOA-基站重启__林玉清00105055__null__MEC-64656
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;TRP信息获取__单站多小区短周期TRP信息获取__林玉清00105055__null__MEC-64469
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】无moid的自动人工指纹融合测试，融合结果正确，解算正常__陈保霖10222411__null__MEC-95382
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;基于AI指纹的解算优化__【RT】指纹库融合算法，电联共享的自动指纹库，人工指纹文件是单PLMN的，融合后指纹库正确__陈保霖10222411__null__MEC-95352
master;testfirst__网关 SLA MONITOR 监控双模组__唐朝10321377__null__MEC-37475
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【中海达】地图坐标系间三维坐标转换场景1--多地图，仅1张地图配置转换矩阵__林玉清00105055__null__MEC-66745
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【中海达】地图坐标系间三维坐标转换场景2--多地图配置转换矩阵-地图间切换__林玉清00105055__null__MEC-66749
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【中海达】地图坐标系间三维坐标转换场景3-多地图配置转换矩阵系数，algo容器下电后上电，校验上电后自动读取配置文件。__林玉清00105055__null__MEC-66751
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位算法;坐标系转换__【中海达】地图坐标系间三维坐标转换场景4-多地图配置转换矩阵系数，reboot重启NE，校验上电后自动读取配置文件。__陈保霖10222411__null__MEC-73689
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;孤岛测量__单平面地图-单小区-孤岛分组场景定位__林玉清00105055__null__MEC-64348
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;孤岛测量__单平面地图-单小区-非孤岛场景定位__林玉清00105055__null__MEC-64347
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;小区协同__UTDOA定位命令开启小区协同-已有测量重新触发__林玉清00105055__null__MEC-64490
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;小区协同__UTDOA定位命令关闭小区协同-存在已发起测量__林玉清00105055__null__MEC-64491
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;网络共享__定位支持网络共享__林玉清00105055__null__MEC-64503
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;网络共享__UTDOA定位支持多个逻辑站（多plmn）__林玉清00105055__null__MEC-64558
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;Rnis交互__Rnis服务提供给LBS服务的UE所在NRCGI必需和基站一致__林玉清00105055__null__MEC-64473
master;NodeEngine测试用例集;定位特性;01-M基本功能;M01-UTDOA定位;定位流程;切换__UTDOA-小区切换-定位服务重启__林玉清00105055__null__MEC-64557
master;NodeEngine测试用例集;平台特性__日志正常采集TcfLog__康剑锋10055360__null__MEC-67314
master;NodeEngine测试用例集;平台特性__日志正常采集TcfHwLog__康剑锋10055360__null__MEC-67345
master;NodeEngine测试用例集;平台特性__Provider异常恢复后日志可以正常采集TcfLog__康剑锋10055360__null__MEC-67309
master;NodeEngine测试用例集;平台特性__Provider异常恢复后日志可以正常采集TcfHwLog__康剑锋10055360__null__MEC-67342
master;NodeEngine测试用例集;平台特性__订阅信息正常流程__康剑锋10055360__null__MEC-67296
master;NodeEngine测试用例集;平台特性__订阅信息异常恢复流程__康剑锋10055360__null__MEC-67290
master;NodeEngine测试用例集;平台特性__通过网管可以查询到PAAS TOPO__康剑锋10055360__null__MEC-67317
master;NodeEngine测试用例集;平台特性__TCFA异常恢复后可以正常通过网管查询到PAAS TOPO__康剑锋10055360__null__MEC-67320
master;NodeEngine测试用例集;平台特性__provider纳管后可以正常查看设备信息__康剑锋10055360__null__MEC-67312
master;NodeEngine测试用例集;平台特性__provider异常恢复后可以正常查看设备信息__康剑锋10055360__null__MEC-67306
master;NodeEngine测试用例集;平台特性__性能可以正常上报到网管__康剑锋10055360__null__MEC-67299
master;NodeEngine测试用例集;平台特性__provider异常恢复后性能可以正常上报__康剑锋10055360__null__MEC-67303
master;NodeEngine测试用例集;平台特性__实例化之后节点上有nodeengine服务账号，可以提供鉴权需要的token__康剑锋10055360__null__MEC-67334
master;NodeEngine测试用例集;平台特性__provider可以正常纳管__康剑锋10055360__null__MEC-67315
master;NodeEngine测试用例集;5GCCP特性;M2 基站对接功能;支持IPv4v6双栈__(RT)5GCCP业务功能需默认支持IPv4&v6双栈__邓立平10125499__null__MEC-64831
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持IPv6地址分配__(RT)ng口采用ipv4&v6地址，5GCCP支持UE的IPv4&v6双栈分配验证__邓立平10125499__null__MEC-88643
master;NodeEngine测试用例集;5GCCP特性;Q综合特性__(RT)5GCCP表修改，UE接入释放验证__邓立平10125499__null__MEC-86067
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持AMBR&SPID修改__(RT)接入释放流程下，UE级别Index to RAT/Frequency Selection Priority信息携带验证__邓立平10125499__null__MEC-85438
master;NodeEngine测试用例集;5GCCP特性;M2 基站对接功能;支持MTU配置__(RT)MTU配置下发测试__邓立平10125499__null__MEC-64830
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持IP地址静态分配__(RT)多ue接入，不同imsi分配各自的静态固定ip__邓立平10125499__null__MEC-64828
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持IP地址静态分配__(RT)多ue接入，部分ue分配静态固定ip，部分ue分配动态ip__邓立平10125499__null__MEC-64829
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持AMBR&SPID修改__(RT)接入切换释放流程下，UE级别AMBR信息携带验证__邓立平10125499__null__MEC-83990
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持终端掉线重新接入后释放原NG口资源__(RT)终端掉线后，UE重建立到其他站，释放原NG口资源验证__邓立平10125499__null__MEC-96298
master;NodeEngine测试用例集;5GCCP特性;M4 终端对接功能;支持IP地址静态分配__(RT)终端接入，终端侧查看分配的ip地址是否是配置的静态固定ip__邓立平10125499__null__MEC-64827
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__NE支持App级上行流特征的ST保障的QoS Control__朱静00241660__null__MEC-65133
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__NE支持App级下行流特征的ST保障的QoS Control__朱静00241660__null__MEC-65134
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__NE支持UE级上行流特征的ST保障的QoS Control__朱静00241660__null__MEC-65135
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__NE支持UE级下行流特征的ST保障的QoS Control__朱静00241660__null__MEC-65136
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-64259【广州明珞时延毛剌问题】NE在连接业务的ST业务不达标时，直接上报ST异常告警（对接iDos）__告警上报-UEIP在配置的IP列表__王焕萍00120295__null__MEC-69494
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-64259【广州明珞时延毛剌问题】NE在连接业务的ST业务不达标时，直接上报ST异常告警（对接iDos）__告警上报-UEIP不在配置的IP列表__王焕萍00120295__null__MEC-69495
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-92053 【NE维测】完善UE上下文订阅，无需E2断链，也可以触发ue上下文订阅__UE接入，触发ue上下文订阅NE__朱静00241660__null__MEC-83401
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型仅一个5qi匹配，下发相应5qi__朱静00241660__null__8270670
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__没有任何资源类型匹配，不下发，保持原来的5qi__朱静00241660__null__8270673
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，且PDB/PER相同的仅一个5QI__朱静00241660__null__8270674
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB不完全匹配/PER完全匹配，选择比APP-PDB小的5QI-PDB失败，则选择比APP-PDB大的5QI-PDB中最小的5QI-PDB所属的5QI__朱静00241660__null__8270691
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，5QI表的带宽配置为NA,APP表的maxmessagesize不配置,仍映射出多个5QI则输出任意匹配的5QI__朱静00241660__null__8270676
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表的usedatarates用户体验速率不配置,5QI表mdbv为NA,仍映射出多个5QI则输出任意匹配的5QI__朱静00241660__null__8270677
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，且PDB/PER相同的多个5QI，APP表的usedatarates用户体验速率不配置,maxmessagesize不配置,仍映射出多个5QI则输出任意匹配的5QI__朱静00241660__null__8270678
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得到带宽值相同的一个5QI__朱静00241660__null__8270679
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得到带宽值相同的多个5QI,且APP表和5QI表中的mdbv均有配置，得到mdbv值相同的5QI输出__朱静00241660__null__8270680
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得到带宽值相同的多个5QI,且APP表和5QI表中的mdbv均有配置，得不到mdbv值相同的5QI映射,则按照取大原则，取5QI表中mdbv最接近APP中mdbv的最大值映射5QI__朱静00241660__null__8270681
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得不到带宽值相同的一个5QI,则选择比APP带宽大的5QI中最小的5QI带宽所属5QI值且仅一个__朱静00241660__null__8270682
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得不到带宽值相同的一个5QI,则选择比APP带宽大的5QI中最小的5QI带宽所属5QI值且多个__朱静00241660__null__8270683
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得不到带宽值相同的一个5QI,则选择比APP带宽大的5QI中最小的5QI带宽所属5QI值失败，则选择比APP带宽小的5QI中最大的5QI带宽所属5QI值且仅一个__朱静00241660__null__8270684
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER完全匹配，PDB/PER相同的多个5QI，APP表和5QI表中的带宽均有配置，得不到带宽值相同的一个5QI,则选择比APP带宽大的5QI中最小的5QI带宽所属5QI值失败，则选择比APP带宽小的5QI中最大的5QI带宽所属5QI值且多个__朱静00241660__null__8270685
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB完全匹配/PER不完全匹配，选择比APP-PER小的5QI-PER失败，则选择比APP-PER大的5QI-PER中最小的5QI-PER所属的5QI__朱静00241660__null__8270686
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB不完全匹配/PER完全匹配，选择比APP-PDB小的5QI-PDB中最大的5QI-PDB所属的5QI__朱静00241660__null__8270687
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__资源类型相同情况，PDB不完全匹配/PER完全匹配，选择比APP-PDB小的5QI-PDB失败，则选择比APP-PDB大的5QI-PDB中最小的5QI-PDB所属的5QI__朱静00241660__null__8270688
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__appip配置多个，资源类型相同情况，PDB完全匹配/PER完全匹配，且PDB/PER相同的仅一个5QI__朱静00241660__null__8270694
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__APPQoSControl表配置多条app moid 配置（相同的app 不同的协议号，app优先级不同），且appip配置多个，资源类型相同情况，PDB完全匹配/PER完全匹配，且PDB/PER相同的仅一个5QI__朱静00241660__null__8270696
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__APPVIP.VIPStart=打开，不看APPQoSControl表里的开关，多CPE侧PC ping 本地分流服务器及UDP访问，E2口下发VIP指示（有shell命令可看）__朱静00241660__null__8270698
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__APP保障开，仅一条app moid且appip就1个，ipiSwitch=关，TrafficModel无配置，NonGBR签约，可靠性优先，FiveQIChara表中resourcetype为NonGBR的多个5QI配置，PER不完全匹配/PDB完全匹配，选择比APP-PER小的5QI-PER失败，则选择比APP-PER大的5QI-PER中最小的5QI-PDB所属的5QI，多CPE侧PC ping 本地分流服务器，E2口下发5qi__朱静00241660__null__8270693
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030102 5qi映射算法__APPQoSControl.controlswitch由开到关，E2口指示5qi disable__朱静00241660__null__8270675
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;配置需求__NE实例化后，增加5qi202的固化配置-v9200__许尧10225649__null__MEC-62186
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-97436  增加5QI203，5QI204相关配置__设置RTT时延10ms,时延达标率为4个9，做小包业务，支持5QI映射到203__朱静00241660__null__MEC-97831
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-97436  增加5QI203，5QI204相关配置__设置RTT时延10ms,时延达标率为4个9，做大包业务，支持5QI映射到204__朱静00241660__null__MEC-97795
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22030103 E2接口测试__恢复E2后，NE侧E2口信息与基站对齐__朱静00241660__null__MEC-30549
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-92053 【NE维测】完善UE上下文订阅，无需E2断链，也可以触发ue上下文订阅__UE重新接入，NE向NR重新订阅UE上下文信息__朱静00241660__null__MEC-86129
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-92053 【NE维测】完善UE上下文订阅，无需E2断链，也可以触发ue上下文订阅__UE接入单pdusession单5qi，NE向NR订阅UE上下文信息__朱静00241660__null__MEC-86125
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__UME支持SLA计数器上报（C63060）__朱静00241660__null__MEC-74188
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__UME支持SLA计数器上报（C63061）__朱静00241660__null__MEC-74190
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__IDOS支持APP级SLA丢包率计数器数据上报（C63057）__朱静00241660__null__MEC-74186
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__IDOS支持APP级测量包数计数器数据上报（C63059）__朱静00241660__null__MEC-73954
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__IDOS支持上报上下行PDCP字节数（C63066）__朱静00241660__null__MEC-67734
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__IDOS支持APP级下行时延时延计数器数据上报（C63056）__朱静00241660__null__MEC-73518
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71686【NE功能梳理-SLA monitor服务的计数器】__IDOS支持APP级时延达标率计数器数据上报（C63058）__朱静00241660__null__MEC-73505
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-84412【NE维测】moid处理优化支持63060和63061两个计数器簇上报到UME__多条APPqos配置除特殊字符外其余均相同的moid，支持C63060和C63061簇计数器上报网管__朱静00241660__null__MEC-85690
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-74758  SLA APP级实时测量任务支持UME呈现__63058计数器簇计数器实时上报网管和idos__王焕萍00120295__null__MEC-74417
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;22032003 STOF+APP保障开+SLA开+IPI开__IPI识别__朱静00241660__null__MEC-37160
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V9200环境，EdgeQos支持同时配置及修改UE级上下行带宽保障__朱静00241660__null__MEC-65858
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V9200环境，EdgeQos支持同时配置及修改APP级上下行带宽保障__朱静00241660__null__MEC-65856
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V9200环境，EdgeQos支持同时配置及修改5QI和APP级上下行带宽保障__朱静00241660__null__MEC-65857
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V9200环境，EdgeQos支持同时配置及修改5QI和UE级上下行带宽保障__朱静00241660__null__MEC-65859
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__支持IPI输出的上下行流特征的ST保障的QoS Control，IPFlow无法获取网管配置UE级的traffic下的下行survivalratio参数，输出默认值__朱静00241660__null__MEC-65143
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__支持IPI输出的上下行流特征的ST保障的QoS Control，IPFlow无法获取网管配置APP级的traffic下的下行survivalratio参数，输出默认值__朱静00241660__null__MEC-65142
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__支持IPI输出的上下行流特征的ST保障的QoS Control，据流特征的IPFlow获取网管配置APP级的traffic下的上下行survivalratio参数__朱静00241660__null__MEC-65140
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__支持IPI输出的上下行流特征的ST保障的QoS Control，据流特征的IPFlow获取网管配置UE级的traffic下的上下行survivalratio参数__朱静00241660__null__MEC-65141
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61055: NE支持PLC业务模型的ST确定性保障__NE支持QoS Control功能流特征带上行和下行的特征信息及survivalratiodl/survivalratioul__朱静00241660__null__MEC-65132
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71683【能力开放流程&接口优化--基于生存时间ST保障功能新架构适配】__新基站版本+新NE版本，ST保障功能测试__朱静00241660__null__MEC-83394
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71682  【能力开放流程&接口优化--下行触发上行预调度功能新架构适配】__下触上静态配置测试场景，新基站版本+新NE版本，时延及丢包率测试__朱静00241660__null__MEC-77579
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-64259【广州明珞时延毛剌问题】NE在连接业务的ST业务不达标时，直接上报ST异常告警（对接iDos）__ST业务---IDOS上上报ST告警和ST告警恢复__王焕萍00120295__null__MEC-69501
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-62088: RNIS感知QoS flow与DRB映射功能__UE接入后修改5qi，NE向NR重新订阅UE上下文信息__许尧10225649__null__MEC-71270
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-62088: RNIS感知QoS flow与DRB映射功能__UE释放，NE删除UE上下文信息__许尧10225649__null__MEC-71269
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-74746【辽宁鲅鱼圈】RNIS支持给IDOS上报ue所在的小区信息和UE的ID__RT-UME网管上向rnis发送getappueinfo查询ue信息--考虑多UE__王焕萍00120295__null__MEC-80728
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;22032001 STOF+APP保障开+SLA关__本地分流业务，APP保障开，SLA探测关，5QI及TM下发及大屏业务呈现测试__朱静00241660__null__MEC-37144
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;22032002 STOF+APP保障开+SLA开+IPI关__业务报文+SLA探测报文UDP格式__朱静00241660__null__MEC-37154
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;22032002 STOF+APP保障开+SLA开+IPI关__业务报文+SLA探测报文ICMP格式__朱静00241660__null__MEC-37156
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关打开，协议类型为ICMP，读取app表里的e2elatency和latencykpi值下发正常__许尧10225649__null__MEC-61496
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关打开，协议类型为UDP，读取ue表里的e2elatency和latencykpi值下发正常__许尧10225649__null__MEC-61497
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关打开，协议类型为TCP，优先读取ue表里的e2elatency和latencykpi值下发正常__许尧10225649__null__MEC-61498
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关打开，controlswitch打开时5qi修改正常__许尧10225649__null__MEC-61501
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关关闭，协议类型为ICMP，不下发e2elatency和latencykpi__许尧10225649__null__MEC-61500
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关打开，协议类型为ICMP，不配置e2elatency和latencykpi，则不下发__许尧10225649__null__MEC-61499
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-7794： 基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障测试用例__SLA大闭环控制开关关闭，controlswitch打开时5qi修改正常__许尧10225649__null__MEC-61502
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ICMP探测报文，ipi关，单终端，上行分流业务保持期间，UE上行 可ping通 Sla Monitor服务地址__朱静00241660__null__MEC-27346
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ICMP探测报文，ipi关，单终端上行分流保持期间SLAForAppConfig.sladetecEnable=2到1（ICMP变更为UDP)__朱静00241660__null__MEC-28848
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ICMP探测报文，ipi关，单终端，SLA 测量报文的大小按照静态设置包长进行发送__朱静00241660__null__MEC-26271
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ICMP探测报文，ipi关，单终端，SLA 测量报文的发包间隔按照设置间隔进行发送__朱静00241660__null__MEC-27331
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__SLA包大小的修改范围64~1380字节__朱静00241660__null__8486069
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ipiSwitch由关到开，在线终端SLA测量报文大小立即切换为按IPI识别出的报文大小进行发送__朱静00241660__null__8446196
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__ipiSwitch由开到关，在线终端SLA测量报文大小立即切换为按照静态配置的进行发送__朱静00241660__null__8446195
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__TrafficModel 设置下触上，（探测包长、周期来自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益（静态下触上）__朱静00241660__null__MEC-58940
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__TrafficModel 设置非下触上，（探测包长和周期均取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文无调度增益__朱静00241660__null__MEC-58938
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出动态下触上，（探测包长取自IPI输出，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益__朱静00241660__null__MEC-58944
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出动态下触上，（探测包长取自IPI输出，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益，E2口下发IPI输出的下触上，不再新增一套下触上__朱静00241660__null__MEC-58950
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出上行周期，（探测包长取自IPI输出，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益，E2口新增一套下触上模型下发，下触上delay写死500us，TM index 是254（信令跟踪可看）__朱静00241660__null__MEC-58948
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出上行周期，（探测包长取自IPI输出（app粒度下）的包长，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文无调度增益__朱静00241660__null__MEC-58942
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出上行周期+动态下触上多套业务模型，（探测包长取自IPI输出，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益__朱静00241660__null__MEC-58946
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__IPI输出上行周期业务+动态下触上，（探测包长取自IPI输出，周期取自SLAForAppConfig设置），观测SLA探测报文RTT。预期探测报文有调度增益，E2口下发IPI输出的下触上，不再新增一套下触上__朱静00241660__null__MEC-58952
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;220302 SLA Monitor 测试__[NE OAM]V3.21.30.01 EdgeQos配置数据和升级验证__朱静00241660__null__8497463
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61062:【精准网络-NE行业大脑】基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障（策略）__V9200环境，协议类型为UDP，APPQoSCtrl保障，终端做UDP灌包业务，时延和丢包率不达标，但调整目标超过基站能力则不下发期望目标值和调整增量__朱静00241660__null__MEC-66161
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61062:【精准网络-NE行业大脑】基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障（策略）__V9200环境，协议类型为UDP，UEQoSCtrl保障，终端做UDP灌包业务，时延和丢包率不达标，但调整目标超过基站能力则不下发期望目标值和调整增量__朱静00241660__null__MEC-66163
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下udp探测报文监控和静态模型qos保障__朱静00241660__null__MEC-33329
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下icmp探测报文监控和静态模型qos保障__朱静00241660__null__MEC-33331
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下udp探测报文监控和ipi动态学习qos保障__朱静00241660__null__MEC-33333
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下icmp探测报文监控和ipi动态学习qos保障__朱静00241660__null__MEC-33335
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下ipi由开到关，探测报文包大小变为静态配置__朱静00241660__null__MEC-33337
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下ipi由关到开，探测报文包大小变为ipi识别的包大小__朱静00241660__null__MEC-33339
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下SLA探测报文按照配置的间隔发送__朱静00241660__null__MEC-33341
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务下SLA探测报文迭代发包数按照设置进行发送__朱静00241660__null__MEC-33343
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__重启STOF/RNIS容器后的核心网SLA保障功能-udp探测报文__朱静00241660__null__MEC-33345
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__重启STOF/RNIS容器后的核心网SLA保障功能-icmp探测报文__朱静00241660__null__MEC-33347
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032005 核心网SLA保障功能__核心网业务期间，sladetecEnable由1改为0，大屏无数据上报__朱静00241660__null__MEC-33349
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;220307 3.22.30改动用例__上行业务，T0业务保持过程中，app保障开关从开到关，5QI disable，查看E2口TM正确下发__朱静00241660__null__MEC-51517
master;NodeEngine测试用例集;EdgeQos;220302 无线增强功能测试集;220307 3.22.30改动用例__上行业务，T0业务保持过程中，app保障开关从关到开，5QI disable，查看E2口TM正确下发__朱静00241660__null__MEC-51518
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__T0精准预调度场景，新基站版本+新NE版本，ping包测试__朱静00241660__null__MEC-77581
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__T0精准预调度场景，新基站版本+新NE版本，UDP灌包测试__朱静00241660__null__MEC-77585
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__T0精准预调度场景，新基站版本+新NE版本，TCP灌包测试__朱静00241660__null__MEC-77589
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级controlswitch=关，UE级调度=开__朱静00241660__null__MEC-60252
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级controlswitch=开，UE级调度=开__朱静00241660__null__MEC-60254
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级controlswitch=开，UE级调度=关__朱静00241660__null__MEC-60256
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级controlswitch=关，UE级调度=关__朱静00241660__null__MEC-60257
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级bwGuaranteeSwitch=关，UE级调度=开__朱静00241660__null__MEC-60258
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级bwGuaranteeSwitch=开，UE级调度=开__朱静00241660__null__MEC-60259
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级bwGuaranteeSwitch=开，UE级调度=关__朱静00241660__null__MEC-60260
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级bwGuaranteeSwitch=关，UE级调度=关__朱静00241660__null__MEC-60261
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级trafficmodelswitch=关，UE级调度=开__朱静00241660__null__MEC-60262
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级trafficmodelswitch=开，UE级调度=开__朱静00241660__null__MEC-60263
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级trafficmodelswitch=开，UE级调度=关__朱静00241660__null__MEC-60272
master;NodeEngine测试用例集;EdgeQos;220301 EdgeQos测试;22032006 UE级QoS保障__APP级trafficmodelswitch=关，UE级调度=关__朱静00241660__null__MEC-60273
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-62214: E2新框架下版本功能特性兼容性需求__新NE对接新NR（65.10）交互功能回归-NI/inner消息__许尧10225649__null__MEC-67000
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-62214: E2新框架下版本功能特性兼容性需求__新NE对接新NR（65.10）交互功能回归-UE级inactive timer__许尧10225649__null__MEC-66998
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-62214: E2新框架下版本功能特性兼容性需求__新NE对接新NR，增加function流程__许尧10225649__null__MEC-66988
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71528能力开放流程接口优化--SR功能新架构适配__MEC修改5qi同时下发SR__许尧10225649__null__MEC-86661
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71528能力开放流程接口优化--SR功能新架构适配__NE单独下发SR参数，下发的SR周期小于当前配置__许尧10225649__null__MEC-86659
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71528能力开放流程接口优化--SR功能新架构适配__MEC下发精准预调度同时下发SR__许尧10225649__null__MEC-86662
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担源mac哈希;NE FRERsmac不匹配，负荷分担目的mac哈希__张盼10295980__null__MEC-65380
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担指定链路优先;NE FRER smac不匹配，负荷分担目的mac哈希__张盼10295980__null__MEC-65381
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担目的mac哈希;NE FRER smac不匹配，负荷分担目的mac哈希__张盼10295980__null__MEC-65382
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担轮询;NE FRER smac不匹配，负荷分担目的mac哈希__张盼10295980__null__MEC-65383
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担指定链路优先;NE FRER smac不匹配，负荷分担轮询__张盼10295980__null__MEC-65384
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行单发__单SE单VNI(双无线)-上行单发下行单发-SE smac不匹配，负荷分担指定链路优先;NE FRER smac不匹配，负荷分担源mac哈希__张盼10295980__null__MEC-65385
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行双发__单SE单VNI(双无线)-上行单发下行双发-SE smac不匹配，负荷分担源mac哈希；NE FRER All__张盼10295980__null__MEC-65386
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行双发__单SE单VNI(双无线)-上行单发下行双发-SE smac不匹配，负荷分担目的mac哈希；NE FRER All__张盼10295980__null__MEC-65387
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行双发__单SE单VNI(双无线)-上行单发下行双发-SE smac不匹配，负荷分担轮询；NE FRER All__张盼10295980__null__MEC-65388
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行单发下行双发__单SE单VNI(双无线)-上行单发下行双发-SE smac不匹配，负荷分担指定链路优先；NE FRER All__张盼10295980__null__MEC-65389
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行双发下行单发__单SE单VNI(双无线)-上行双发下行单发-SE ALL；NE FRER smac不匹配，负荷分担源mac哈希__张盼10295980__null__MEC-65390
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行双发下行单发__单SE单VNI(双无线)-上行双发下行单发-SE ALL；NE FRER smac不匹配，负荷分担目的mac哈希__张盼10295980__null__MEC-65391
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行双发下行单发__单SE单VNI(双无线)-上行双发下行单发-SE ALL；NE FRER smac不匹配，负荷分担轮询__张盼10295980__null__MEC-65392
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行双发下行单发__单SE单VNI(双无线)-上行双发下行单发-SE ALL；NE 测不做FRER__张盼10295980__null__MEC-73705
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;上行双发下行双发__单SE单VNI(双无线)-上行双发下行双发-SE FRER规则ALL,NE FRER规则ALL__张盼10295980__null__MEC-65393
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【RT】V1100FRER场景，VxlanGW网管配置增删改__张盼10295980__null__MEC-92815
master;NodeEngine测试用例集;L2组网特性;Frer;Frer性能测试__iperf udp灌包测试__张盼10295980__null__MEC-65551
master;NodeEngine测试用例集;L2组网特性;Frer;Frer性能测试__iperf tcp灌包测试__张盼10295980__null__MEC-65552
master;NodeEngine测试用例集;L2组网特性;Frer;Frer性能测试__FTP文件上传测试__张盼10295980__null__MEC-65549
master;NodeEngine测试用例集;L2组网特性;Frer;Frer性能测试__FTP文件下载测试__张盼10295980__null__MEC-65550
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;SE侧不带Vlan__上下行双发-SE侧不带Vlan-单SE单VNI（双无线）__张盼10295980__null__MEC-65378
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;SE侧不带Vlan__上行双发下行单发-SE侧不带Vlan-单SE单VNI（双无线）__张盼10295980__null__MEC-65379
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE单VNI(双无线)（16）;SE侧不带Vlan__上下行双发-NE侧不带Vlan-单SE单VNI（双无线）__张盼10295980__null__MEC-73697
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI__双无线VNI1上下行双发，VNI8上下行单发__张盼10295980__null__MEC-65424
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI__双无线VNI1上行单发下行双发，VNI8上行双发下行单发__张盼10295980__null__MEC-65425
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI__双无线VNI1上下行双发，VNI8上下行双发__张盼10295980__null__MEC-65426
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI__双无线VNI1上行双发下行单发，VNI8上下行双发__张盼10295980__null__MEC-65427
master;NodeEngine测试用例集;L2组网特性;Frer;FRER支持多VNI场景__【RT】单SE多VNI，FRER删建情况下frer业务正常__张盼10295980__null__MEC-83666
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;多SE多VNI__FRER多SE多终端互访(PC1PC2双发，PC1PC2单发）__张盼10295980__null__MEC-65435
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;多SE多VNI__FRER多SE多终端互访(PC1双发，PC2单发）__张盼10295980__null__MEC-65436
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[RT]多SE多VNI双无线场景，FRER Action命令查询__张盼10295980__null__MEC-65232
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[RT]单SE单VNI无线加有线场景，FRER Action命令查询__张盼10295980__null__MEC-65228
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[RT]多SE单VNI双无线场景，FRER Action命令查询__张盼10295980__null__MEC-65233
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【RT】【V1100】SE9102 FRER多VNI场景，queryL2FibTable和queryFrerTunnelCfg查询__张盼10295980__null__MEC-141599
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI（无线加有线）__单SE多VNI(无线加有线)-VNI1上下行双发，VNI8上下行单发__张盼10295980__null__MEC-65442
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI（无线加有线）__单SE多VNI(无线加有线)-VNI1上行单发下行双发，VNI8上行双发下行单发__张盼10295980__null__MEC-65443
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI（无线加有线）__单SE多VNI(无线加有线)-VNI1上下行双发，VNI8上下行双发__张盼10295980__null__MEC-65444
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;单SE多VNI（无线加有线）__单SE多VNI(无线加有线)-VNI1上行双发下行单发，VNI8上下行双发__张盼10295980__null__MEC-65445
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;frer和vxlan开关测试__FRER和VXLAN同时开启时关闭FRER__张盼10295980__null__MEC-65467
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【RT】多SE多VNI场景，Frer计数器上报__张盼10295980__null__MEC-62110
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【RT】Frer和Vxlan共存下，Frer计数器正确上报__张盼10295980__null__MEC-62043
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【RT】有线加无线场景，Frer计数器上报__张盼10295980__null__MEC-62111
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【RT】6100软件FRER用户与6100软件FRER用户互访__张盼10295980__null__MEC-97772
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+wan1场景__Vxlan动态隧道-双无线-FRER上下行单发__张盼10295980__null__MEC-65494
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+wan1场景__Vxlan动态隧道-双无线-FRER上行单发下行双发__张盼10295980__null__MEC-65491
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+wan1场景__Vxlan动态隧道-双无线-frer上行双发下行单发__张盼10295980__null__MEC-65493
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+wan1场景__Vxlan动态隧道-双无线-FRER上下行双发__张盼10295980__null__MEC-65486
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+Eth1场景__Vxlan动态隧道-无线加有线-frer上行双发下行单发__张盼10295980__null__MEC-65489
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+Eth1场景__Vxlan动态隧道-无线加有线-FRER上下行单发__张盼10295980__null__MEC-65487
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+Eth1场景__Vxlan动态隧道-无线加有线-FRER上行单发下行双发__张盼10295980__null__MEC-65488
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan动态隧道L2对接本地;wan0+Eth1场景__Vxlan动态隧道-无线加有线-FRER上下行双发__张盼10295980__null__MEC-65490
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan0+wan1场景__Vxlan静态隧道-双无线-FRER上下行单发__张盼10295980__null__MEC-65514
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan0+wan1场景__Vxlan静态隧道-双无线-FRER上行单发下行双发__张盼10295980__null__MEC-65512
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan0+wan1场景__Vxlan静态隧道-双无线-frer上行双发下行单发__张盼10295980__null__MEC-65513
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan0+wan1场景__Vxlan静态隧道-双无线-FRER上下行双发__张盼10295980__null__MEC-65519
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan+eth1场景__Vxlan静态隧道-无线加有线-frer上行双发下行单发__张盼10295980__null__MEC-65515
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan+eth1场景__Vxlan静态隧道-无线加有线-FRER上下行单发__张盼10295980__null__MEC-65518
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan+eth1场景__Vxlan静态隧道-无线加有线-FRER上行单发下行双发__张盼10295980__null__MEC-65517
master;NodeEngine测试用例集;L2组网特性;Frer;Frer与Vxlan共存场景测试(16);vxlan静态隧道L2对接本地;wan+eth1场景__Vxlan静态隧道-无线加有线-FRER上下行双发__张盼10295980__null__MEC-65516
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;其它异常__负荷分担-目的mac哈希-链路中断__张盼10295980__null__MEC-65466
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;其它异常__负荷分担-轮询-链路中断__张盼10295980__null__MEC-65464
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;其它异常__负荷分担-源mac哈希-链路中断__张盼10295980__null__MEC-65465
master;NodeEngine测试用例集;L2组网特性;Frer;VXLANGW大消息处理__Vxlangw网桥域配置增删改__张盼10295980__null__MEC-81903
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;异常重启__FRER业务重启NE stof__张盼10295980__null__MEC-65453
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;异常重启__FRER业务中重启vxlangw服务__张盼10295980__null__MEC-65454
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;异常重启__FRER业务中SE下电恢复__张盼10295980__null__MEC-65458
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;异常重启__FRER业务中NE NE下电恢复__张盼10295980__null__MEC-65459
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__FRER单链路配置为双链路__张盼10295980__null__MEC-66611
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__FRER双无线配置为单链路__张盼10295980__null__MEC-66610
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__基站-NE GRE断链恢复__张盼10295980__null__MEC-65452
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__FRER无线切换有线场景__张盼10295980__null__MEC-66609
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__FRER双链路异常恢复__张盼10295980__null__MEC-65451
master;NodeEngine测试用例集;L2组网特性;Frer;Frer异常测试（13）;链路异常恢复__FRER单链路断开异常恢复__张盼10295980__null__MEC-65450
master;NodeEngine测试用例集;L2组网特性;Frer;接入认证__FRER本地认证__张盼10295980__null__MEC-65333
master;NodeEngine测试用例集;L2组网特性;Frer;隧道开关和链路数量测试__frer隧道开关打开，vxlan隧道开关关闭__张盼10295980__null__MEC-65359
master;NodeEngine测试用例集;L2组网特性;Frer;隧道开关和链路数量测试__vxlan开关打开frer隧道开关打开__张盼10295980__null__MEC-65358
master;NodeEngine测试用例集;L2组网特性;Frer;隧道开关和链路数量测试__FRER单隧道__张盼10295980__null__MEC-65362
master;NodeEngine测试用例集;L2组网特性;Frer;隧道开关和链路数量测试__FRER双隧道链路__张盼10295980__null__MEC-65363
master;NodeEngine测试用例集;L2组网特性;Frer;Frer性能测试__Frer增益性能测试__张盼10295980__null__MEC-68193
master;NodeEngine测试用例集;IPI;历史用例__上行业务，单pdusession单qosflow，单app单ipflow，查看IPI输出结果及RNIS 在E2口的下发__郭子豪10247561__null__MEC-50442
master;NodeEngine测试用例集;IPI;历史用例__上行业务，单pdusession单qosflow，单app，多ip flow，查看IPI输出结果及RNIS 在E2口的下发__郭子豪10247561__null__MEC-50444
master;NodeEngine测试用例集;IPI;历史用例__上行业务，单pdusession单qosflow，多app，多ip flow，查看IPI输出结果及RNIS 在E2口的下发__郭子豪10247561__null__MEC-50446
master;NodeEngine测试用例集;IPI;历史用例__碰撞检测结果呈现__郭子豪10247561__null__MEC-37906
master;NodeEngine测试用例集;IPI;历史用例__APPQosCtrl.TunnelInnerAppInfo为空，IPI不做内层识别__郭子豪10247561__null__MEC-60567
master;NodeEngine测试用例集;IPI;历史用例__APPQosCtrl.TunnelInnerAppInfo有配置，IPI识别内层报文__郭子豪10247561__null__MEC-60571
master;NodeEngine测试用例集;IPI;MEC-77994 支持100ms以上周期性业务模型识别的优化-防护__IPI识别大周期各类业务报文（>100ms），业务特征输出正常-RT防护__郭子豪10247561__null__MEC-80625
master;NodeEngine测试用例集;IPI;MEC-68837 按APP的DSCP关联业务特征__IPI功能增加DSCP流特性字段识别-RT__郭子豪10247561__null__MEC-68145
master;NodeEngine测试用例集;IPI;MEC-74448 IPI流管理映射机制优化__IPI功能增加DSCP流特性字段识别-RT__郭子豪10247561__null__MEC-68493
master;NodeEngine测试用例集;IPI;MEC-74448 IPI流管理映射机制优化__I帧碰撞检测和优化功能正常-RT__郭子豪10247561__null__MEC-68186
master;NodeEngine测试用例集;IPI;MEC-128683 IPI支持MAC IN MAC的报文特征识别-VxLan-防护__Vxlan封装macINmac，大周期报文，IPI识别-RT防护__郭子豪10247561__null__MEC-139152
master;NodeEngine测试用例集;IPI;MEC-128683 IPI支持MAC IN MAC的报文特征识别-VxLan-防护__Vxlan封装macINmac，大包报文，IPI识别-RT防护__郭子豪10247561__null__MEC-139151
master;NodeEngine测试用例集;IPI;MEC-128683 IPI支持MAC IN MAC的报文特征识别-VxLan-防护__二层Mac报文业务流IPI识别功能-RT防护__郭子豪10247561__null__MEC-139143
master;NodeEngine测试用例集;NE性能测试;M1 STOF;M1-3 NAPT转发性能__STOF融合版本NAPT网络地址端口转换模式，只有本地侧业务，50个NB共5.55W个UE22.2W条TCP（512字节）+22.2W条UDP（512字节）流，仪表模拟上行5G下行5G灌包性能测试__艾昭琳00312162__null__MEC-61789
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020201 单本地网(多APP)多终端测试;2202020102 多CPE相同VPN下不同域账号登录__CPE关NAT，LNS用户动态IP，多CPE相同VPN下不同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51326
master;NodeEngine测试用例集;L3组网特性;LNS;220201基础用例;22020105 MTU配置测试__CPE侧MTU值修改配置业务测试__林成10249249__null__MEC-51668
master;NodeEngine测试用例集;L3组网特性;LNS;220201基础用例;22020105 MTU配置测试__NE侧MTU值修改配置业务测试__林成10249249__null__MEC-51664
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020201 单本地网(多APP)多终端测试;2202020102 多CPE相同VPN下不同域账号登录__CPE关NAT，LNS用户动态IP，多CPE相同VPN下不同域账号登录，终端互访__林成10249249__null__MEC-51325
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020201 单本地网(多APP)多终端测试;2202020101 多CPE相同VPN下相同域账号登录__CPE关NAT，LNS用户动态IP，多CPE相同VPN下相同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51321
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020201 单本地网(多APP)多终端测试;2202020101 多CPE相同VPN下相同域账号登录__CPE关NAT，LNS用户动态IP，多CPE相同VPN下相同域账号登录，终端互访__林成10249249__null__MEC-51322
master;NodeEngine测试用例集;L3组网特性;LNS;220201基础用例;22020106 边界值测试__LNS地址池表空间满配__林成10249249__null__MEC-51319
master;NodeEngine测试用例集;L3组网特性;LNS;220201基础用例;22020106 边界值测试__LNSippool参数配置测试__林成10249249__null__MEC-35458
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020202 多本地网多终端测试;2202020201 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户动态IP，多CPE不同VPN下不同域账号登录，各CPE访问对应VPN本地网__林成10249249__null__MEC-51329
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020202 多本地网多终端测试;2202020201 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户动态IP，多CPE不同VPN下不同域账号登录，终端互访（不通)__林成10249249__null__MEC-51330
master;NodeEngine测试用例集;L3组网特性;LNS;220202 CPE关NAT，LNS用户动态IP;22020202 多本地网多终端测试;2202020201 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户动态IP，两个域绑定不同的VPN，当地址池存在冲突时，两个CPE分别使用两个域拨号接入（业务正常）__林成10249249__null__MEC-56106
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020302 单本地网(多APP)多终端测试;2202020202 多CPE相同VPN下不同域账号登录__CPE关NAT，LNS用户固定IP，多CPE相同VPN下不同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51277
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020302 单本地网(多APP)多终端测试;2202020202 多CPE相同VPN下不同域账号登录__CPE关NAT，LNS用户固定IP，多CPE相同VPN下不同域账号登录，终端互访__林成10249249__null__MEC-51278
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020302 单本地网(多APP)多终端测试;2202020201 多CPE相同VPN下相同域账号登录__CPE关NAT，LNS用户固定IP，多CPE相同VPN下相同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51273
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020302 单本地网(多APP)多终端测试;2202020201 多CPE相同VPN下相同域账号登录__CPE关NAT，LNS用户固定IP，多CPE相同VPN下相同域账号登录，终端互访__林成10249249__null__MEC-51275
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020203 多本地网多终端测试;2202020301 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户固定IP，多CPE不同VPN下不同域账号登录，配置两个本地网，分别对应VPN1/VPN2，CPE以VPN1下的域进行拨号，PC分别ping两个本地网__林成10249249__null__MEC-51285
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020203 多本地网多终端测试;2202020301 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户固定IP，多CPE不同VPN下不同域账号登录，终端互访（不通)__林成10249249__null__MEC-51281
master;NodeEngine测试用例集;L3组网特性;LNS;220203 CPE关NAT，LNS用户固定IP;22020203 多本地网多终端测试;2202020301 多CPE不同VPN下不同域账号登录__CPE关NAT，LNS用户固定IP，两个域绑定不同的VPN，当地址池存在冲突时，两个CPE分别使用两个域拨号接入（业务正常）__林成10249249__null__MEC-56110
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020501 单本地网(多APP)多终端测试;2202050102 多CPE相同VPN下不同域账号登录__CPE开NAT，LNS用户动态IP，多CPE相同VPN下不同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51429
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020501 单本地网(多APP)多终端测试;2202050102 多CPE相同VPN下不同域账号登录__CPE开NAT，LNS用户动态IP，多CPE相同VPN下不同域账号登录，终端互访（不通）__林成10249249__null__MEC-51428
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020501 单本地网(多APP)多终端测试;2202050101 多CPE相同VPN下相同域账号登录__CPE开NAT，LNS用户动态IP，多CPE相同VPN下相同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51425
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020501 单本地网(多APP)多终端测试;2202050101 多CPE相同VPN下相同域账号登录__CPE开NAT，LNS用户动态IP，多CPE相同VPN下相同域账号登录，终端互访__林成10249249__null__MEC-51424
master;NodeEngine测试用例集;NE性能测试;性能相关新需求;市场需求 MEC-68802: [NE指标梳理需求]LNS基本指标交付测试__L2TP支持VPN数量配置__沈函10333705__null__MEC-68904
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020502 多本地网多终端测试;2202050201 多CPE不同VPN下不同域账号登录__CPE开NAT，LNS用户动态IP，多CPE不同VPN下不同域账号登录，配置两个本地网，分别对应VPN1/VPN2，CPE以VPN1下的域进行拨号，PC分别ping两个本地网__林成10249249__null__MEC-51433
master;NodeEngine测试用例集;L3组网特性;LNS;220204 CPE开NAT，LNS用户动态IP;22020502 多本地网多终端测试;2202050201 多CPE不同VPN下不同域账号登录__CPE开NAT，LNS用户动态IP，多CPE不同VPN下不同域账号登录，终端互访（不通)__林成10249249__null__MEC-51432
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020401 单本地网(多APP)多终端测试;2202040102 多CPE相同VPN下不同域账号登录__CPE开NAT，LNS用户固定IP，多CPE相同VPN下不同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51354
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020401 单本地网(多APP)多终端测试;2202040102 多CPE相同VPN下不同域账号登录__CPE开NAT，LNS用户固定IP，多CPE相同VPN下不同域账号登录，终端互访（不通）__林成10249249__null__MEC-51353
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020401 单本地网(多APP)多终端测试;2202040101 多CPE相同VPN下相同域账号登录__CPE开NAT，LNS用户固定IP，多CPE相同VPN下相同域账号登录，访问本地网APP1和APP2__林成10249249__null__MEC-51349
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020401 单本地网(多APP)多终端测试;2202040101 多CPE相同VPN下相同域账号登录__CPE开NAT，LNS用户固定IP，多CPE相同VPN下相同域账号登录，终端互访__林成10249249__null__MEC-51350
master;NodeEngine测试用例集;L3组网特性;LNS;220201基础用例;22020103 异常测试__重启STOF业务测试__林成10249249__null__MEC-51309
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020402 多本地网多终端测试;2202040201 多CPE不同VPN下不同域账号登录__CPE开NAT，LNS用户固定IP，多CPE不同VPN下不同域账号登录，配置两个本地网，分别对应VPN1/VPN2，CPE以VPN1下的域进行拨号，PC分别ping两个本地网__林成10249249__null__MEC-51359
master;NodeEngine测试用例集;L3组网特性;LNS;220205 CPE开NAT，LNS用户固定IP;22020402 多本地网多终端测试;2202040201 多CPE不同VPN下不同域账号登录__CPE开NAT，LNS用户固定IP，多CPE不同VPN下不同域账号登录，终端互访（不通)__林成10249249__null__MEC-51358
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201121 配置测试__配置容量 -系统参数__沈函10333705__null__MEC-56153
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220118 异常测试__NFOAM容器重启__沈函10333705__null__MEC-52283
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010201 PLMN分流;2201020101多UE多PLMN报文，PLMN匹配成功，业务测试__基于IPV4的PLMN分流，动态NAPT_NAT后地址与子接口同网段，验证ping包业务__沈函10333705__null__MEC-40491
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201192  NE转发模式__动态NAT__沈函10333705__null__MEC-40726
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201192  NE转发模式__直接转发__沈函10333705__null__MEC-40725
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201192  NE转发模式__静态NAPT__沈函10333705__null__MEC-40728
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201192  NE转发模式__多UE接入，静态NAT-UE动态反写ip测试__沈函10333705__null__MEC-40727
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220118 异常测试__STOF容器重启__沈函10333705__null__MEC-40568
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010201 PLMN分流;2201020102 多UE多PLMN报文，UE1 PLMN匹配成功，UE2 PLMN匹配失败，走其它分流，业务测试__基于IPV6的PLMN分流，PLMN分流成功和失败混合场景，动态NAT，验证ping包业务__沈函10333705__null__MEC-52743
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010201 PLMN分流;2201020102 多UE多PLMN报文，UE1 PLMN匹配成功，UE2 PLMN匹配失败，走其它分流，业务测试__基于IPV6的PLMN分流，PLMN分流成功和失败混合场景，直接转发，验证TCP业务__沈函10333705__null__MEC-40497
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020202 多UE多切片报文，UE1切片匹配成功，UE2切片匹配失败，走ULRule分流，业务测试__动态NAPT_NAT后地址与子接口不同网段__沈函10333705__null__MEC-53820
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020202 多UE多切片报文，UE1切片匹配成功，UE2切片匹配失败，走ULRule分流，业务测试__动态NAPT_NAT后地址与子接口同网段__沈函10333705__null__MEC-40522
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020202 多UE多切片报文，UE1切片匹配成功，UE2切片匹配失败，走ULRule分流，业务测试__静态NAT，先上行后下行业务测试__沈函10333705__null__MEC-40523
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020201 多UE多切片报文，切片匹配成功，业务测试__直接转发，业务测试__沈函10333705__null__MEC-40502
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020201 多UE多切片报文，切片匹配成功，业务测试__静态NAPT，先下行后上行业务测试__沈函10333705__null__MEC-40501
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010202 切片分流;2201020201 多UE多切片报文，切片匹配成功，业务测试__动态NAT__沈函10333705__null__MEC-53822
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201193 基本分流业务类型__ICMP ping包__沈函10333705__null__MEC-40740
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201193 基本分流业务类型__FTP业务__沈函10333705__null__MEC-40738
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201193 基本分流业务类型__TCP灌包测试__沈函10333705__null__MEC-40739
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201194 大包分片重组功能__大包分片重组功能—核心网侧__沈函10333705__null__MEC-40747
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201196 UE状态转换测试__UE首次接入__沈函10333705__null__MEC-40586
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201122 action测试__Action: 获取UE在线状态__林成10249249__null__MEC-40651
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020302 按源IP地址+目的地址分流__动态NAT__沈函10333705__null__MEC-54374
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201122 action测试__根据IP获取NAT表项__林成10249249__null__MEC-40655
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020302 按源IP地址+目的地址分流__直接转发，业务测试__沈函10333705__null__MEC-54375
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201122 action测试__Action: 获取在线UE的标识信息__林成10249249__null__MEC-40650
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020302 按源IP地址+目的地址分流__静态NAPT__沈函10333705__null__MEC-54378
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020302 按源IP地址+目的地址分流__静态NAT-UE使用固定地址（NAT后地址与子接口同网段）__沈函10333705__null__MEC-54377
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220118 异常测试__MQTT容器重启__沈函10333705__null__MEC-52281
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220118 异常测试__MEC 整机重启__沈函10333705__null__MEC-40569
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220118 异常测试__基站重启__沈函10333705__null__MEC-40570
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__动态NAPT-NAT后地址与子接口不同网段，上行规则中支持的规则类型选择目的IP__沈函10333705__null__MEC-54122
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__动态NAPT-NAT后地址与子接口同网段__沈函10333705__null__MEC-38347
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__动态NAT__沈函10333705__null__MEC-38350
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020303 基于RFSP or SPID进行用户控制__承载上报RFSP/SPID信息__沈函10333705__null__MEC-50492
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020303 基于RFSP or SPID进行用户控制__场景测试-ULSPI配置RFSP/SPID__沈函10333705__null__MEC-50493
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020303 基于RFSP or SPID进行用户控制__场景测试-ULSPI不配置RFSP/SPID__沈函10333705__null__MEC-54396
master;NodeEngine测试用例集;分流特性;MEC-73249 NE双域网下支持基于位置的灵活业务控制--伟星场景__RT-高频NSA组网，终端接入5G高频小区，双域网灵活配置__艾昭琳00312162__null__MEC-76745
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__直接转发，业务测试__沈函10333705__null__MEC-38346
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__静态NAPT__沈函10333705__null__MEC-38349
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__静态NAT-UE使用固定地址（NAT后地址与子接口同网段）__沈函10333705__null__MEC-38348
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010204 域名分流__DNS报文筛选开关开：UlRule进行DNSServer ip地址匹配失败，DomainName域名匹配成功__沈函10333705__null__MEC-54398
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010204 域名分流__DNS报文筛选开关开：UlRule进行DNSServer ip地址匹配失败，DomainName没匹配，DNSRule匹配（支持模糊匹配）__沈函10333705__null__MEC-54400
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010205 混合场景__上行报文PLMN不匹配，上行报文切片不匹配，上行报文UlRule 匹配__沈函10333705__null__MEC-40556
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010205 混合场景__所有规则不匹配，转发核心网__沈函10333705__null__MEC-40557
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201192  NE转发模式__LBFullNAT__沈函10333705__null__MEC-40730
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010204 域名分流__DNS  异常测试__沈函10333705__null__MEC-54406
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010204 域名分流__DNS报文筛选开关开：UlRule进行DNSServer ip地址匹配成功__沈函10333705__null__MEC-40542
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010204 域名分流__DNS报文筛选开关开：域名匹配失败，stof直接按照缺省路由则转核心网__沈函10333705__null__MEC-54404
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220113 业务测试;2201195 LDNS功能测试__一个域名对应多个地址__沈函10333705__null__MEC-40750
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220111环境&组网配置__环境&组网配置---基站场景隧道版本测试__沈函10333705__null__MEC-40717
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22012 组合用例集;22010203 五元组分流;2201020301 按目的地址分流__多CPE接入，不同转发方式进园区不同app__沈函10333705__null__MEC-40540
master;NodeEngine测试用例集;分流特性;MEC-68352【故障转需求】NE STOF支持网管抓包上传__STOF支持网管抓包上传__沈函10333705__null__MEC-67523
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201123 告警测试__分流服务和基站通信链路断__沈函10333705__null__MEC-40662
master;NodeEngine测试用例集;分流特性;MEC-59033 NE为IDOS提供向导式告警处理__[通用]无Ran Function时候告警命令和告警原因码（RT）__王焕萍00120295__null__MEC-67424
master;NodeEngine测试用例集;分流特性;【MEC-87701】【NE维测】分流支持实时测量计数器__【9200】分流支持63046所属实时/历史测量计数器__沈函10333705__null__MEC-86868
master;NodeEngine测试用例集;分流特性;【MEC-87701】【NE维测】分流支持实时测量计数器__【9200】分流支持63076所属实时/历史测量计数器__沈函10333705__null__MEC-86870
master;NodeEngine测试用例集;分流特性;【MEC-87701】【NE维测】分流支持实时测量计数器__【9200】分流支持63062所属实时/历史测量计数器__沈函10333705__null__MEC-86869
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620003:发往本地网报文数(packets)__沈函10333705__null__MEC-40697
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620004:收到本地网转发的报文数(packets)__沈函10333705__null__MEC-40695
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620005:发往本地网报文字节数(bytes)__沈函10333705__null__MEC-40693
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620006:收到本地网转发的报文字节数(bytes)__沈函10333705__null__MEC-40692
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620001:收到上行GTPU报文数(packets)__沈函10333705__null__MEC-40696
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620002:收到上行GTPU报文字节数(bytes)__沈函10333705__null__MEC-40694
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630001:基站上报承载消息的次数(times)__沈函10333705__null__MEC-40709
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630002:基站上报承载释放消息的次数(times)__沈函10333705__null__MEC-51335
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630003:基站上报切换开始指示消息的次数(times)__沈函10333705__null__MEC-51336
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630004:主动请求承载的次数(times)__沈函10333705__null__MEC-51337
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630005:发送ping的次数(times)__沈函10333705__null__MEC-51338
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630006:收到ping的确认次数(times)__沈函10333705__null__MEC-51339
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630007:承载数量的最大值(times)__沈函10333705__null__MEC-51340
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB信令__媒体服务NodeB信令：C630630008:承载数量的平均值(times)__沈函10333705__null__MEC-51341
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620007:收到外层IP报文分片数(fragments)__沈函10333705__null__MEC-40691
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620008:外层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-40698
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620009:外层分片重组失败IP报文数(packets)__沈函10333705__null__MEC-40708
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620010:收到内层IP报文分片数(fragments)__沈函10333705__null__MEC-40710
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620011:内层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-40711
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务NodeB数据__媒体服务NodeB数据：C630620012:内层分片重组失败IP报文数(packets)__沈函10333705__null__MEC-40707
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440005:发往本地网报文数(packets)__沈函10333705__null__MEC-51366
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440006:收到本地网转发的报文数(packets)__沈函10333705__null__MEC-51363
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440007:发往本地网报文字节数(bytes)__沈函10333705__null__MEC-51367
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440008:收到本地网转发的报文字节数(bytes)__沈函10333705__null__MEC-51370
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440001:收到上行GTPU报文数(packets)__沈函10333705__null__MEC-51368
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440002:收到上行GTPU报文字节数(bytes)__沈函10333705__null__MEC-51369
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440003:外层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-51365
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务PLMN data性能计数器__媒体服务PLMN数据：C630440004:内层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-51364
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450005:发往本地网报文数(packets)__沈函10333705__null__MEC-51384
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450006:收到本地网转发的报文数(packets)__沈函10333705__null__MEC-51380
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450007:发往本地网报文字节数(bytes)__沈函10333705__null__MEC-51381
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450001:收到上行GTPU报文数(packets)__沈函10333705__null__MEC-51385
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450002:收到上行GTPU报文字节数(bytes)__沈函10333705__null__MEC-51379
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450003:外层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-51386
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体面SNSSAI data性能计数器__媒体面SNSSAI数据：C630450004:内层分片重组成功IP报文数(packets)__沈函10333705__null__MEC-51382
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务 Local Network data性能计数器__媒体服务本地网数据：C630460001:发往本地网报文数(packets)__沈函10333705__null__MEC-51395
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务 Local Network data性能计数器__媒体服务本地网数据：C630460002:发往本地网报文字节数(bytes)__沈函10333705__null__MEC-51397
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务 Local Network data性能计数器__媒体服务本地网数据：C630460003:收到本地网转发的报文数(packets)__沈函10333705__null__MEC-51398
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);媒体服务 Local Network data性能计数器__媒体服务本地网数据：C630460004:收到本地网转发的报文字节数(bytes)__沈函10333705__null__MEC-51396
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);STOF全局业务测量__STOF全局业务测量计数器：C630540001:总分流流量(bytes)__沈函10333705__null__MEC-51416
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);STOF全局业务测量__STOF全局业务测量计数器：C630540002:超出告警带宽告警流量(bytes)__沈函10333705__null__MEC-51403
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);STOF全局业务测量__STOF全局业务测量计数器：C630540003:超出订购带宽流量(bytes)__沈函10333705__null__MEC-51404
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);LDNS计数器__LDNS计数器：C630550001:按域名分流转发到本地网字节数(Bytes)__沈函10333705__null__MEC-51407
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201124 计数器KPI测试(45);LDNS计数器__LDNS计数器-C630550002:按域名分流收到本地网字节数(Bytes)__沈函10333705__null__MEC-51408
master;NodeEngine测试用例集;NE性能测试;性能相关新需求;市场需求 MEC-68801: [NE指标梳理需求]STOF基本指标交付测试__STOF上行报文规则数量配置__沈函10333705__null__MEC-68903
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201127 版本升级__VGCd系列单板MEC软件升级DPDK版本__沈函10333705__null__MEC-50500
master;NodeEngine测试用例集;通感;MEC-66267【高频通感】--- stof支持高频通感数据转发;管理面用例__NE实例化配置，新增NonUESessionPktRule固化配置__林成10249249__null__MEC-68660
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201121 配置测试__配置测试-异常值测试__沈函10333705__null__MEC-52325
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220117 带宽管理功能__带宽管理功能-会话级和应用级__沈函10333705__null__MEC-55443
master;NodeEngine测试用例集;分流特性;2201 普通本地分流测试;22011 基础用例集;220112 运维类测试;2201121 配置测试__配置容量__沈函10333705__null__MEC-38343
master;NodeEngine测试用例集;分流特性;MEC-98997: 【TCF极致版功能】TCFAdapter获取VGCd面板光口光功率__VGCd面板口出口连接1G光模块或者10G光模块，获取光功率__王焕萍00120295__null__MEC-114842
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__NAPT下单UE2个PDUsession1个PDN下ping包__王焕萍00120295__null__MEC-62150
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__PassiveUE表修改UE IP__王焕萍00120295__null__MEC-62155
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__UE级别流量上报不包括公网流量__王焕萍00120295__null__MEC-62154
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__本地网业务丢弃时不统计在UE级本地网流量中__王焕萍00120295__null__MEC-62153
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__一阶段-2UE支持被动访问ping包流量上报__王焕萍00120295__null__MEC-62143
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__2UE2个PDU session下1个PDN下ping包__王焕萍00120295__null__MEC-62152
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__单UE2个不同PDUsession下多PDN下ping包__王焕萍00120295__null__MEC-62151
master;NodeEngine测试用例集;分流特性;MEC-54987 NE支持通过UME网管基于固定IP的UE级流量统计__2UE2个QFI下1个PDN下ping包__王焕萍00120295__null__MEC-62149
master;NodeEngine测试用例集;分流特性;MEC-61555 APPM 9200版本切换CoNAP平台__验证APM功能反写设备信息__林成10249249__null__MEC-65941
master;NodeEngine测试用例集;分流特性;BFD功能;MEC-59014    NE+UPF备份，NE分流需要支持对端交换开启的BFD检测功能;MEC-65325 STOF支持BFD协议__STOF支持单条BFD链路正常配置（bfd认证会话配置增删）__林成10249249__null__MEC-66888
master;NodeEngine测试用例集;分流特性;BFD功能;MEC-59014    NE+UPF备份，NE分流需要支持对端交换开启的BFD检测功能;MEC-65325 STOF支持BFD协议__STOF支持单条BFD链路异常配置__林成10249249__null__MEC-66891
master;NodeEngine测试用例集;分流特性;BFD功能;MEC-61227 NE+UPF备份，NE支持单端链路故障下的端口联动处理__stof容器重启对BFD联动的影响__林成10249249__null__MEC-66906
master;NodeEngine测试用例集;分流特性;BFD功能;MEC-61227 NE+UPF备份，NE支持单端链路故障下的端口联动处理__NE重启对BFD联动的影响__林成10249249__null__MEC-66907
master;NodeEngine测试用例集;分流特性;BFD功能;MEC-61227 NE+UPF备份，NE支持单端链路故障下的端口联动处理__业务口(1个)联动本地口（1个，基于IPV4传输），NE切换到UPF以及恢复流程__林成10249249__null__MEC-66899
master;NodeEngine测试用例集;分流特性;MEC-93290: 支持将Trunk哈希模式默认修改为MAC方式__RT-基站背板hashmode默认值修改为l2_srcdestmac__艾昭琳00312162__null__MEC-84225
master;NodeEngine测试用例集;确定性工业基站;【MEC-100211】工业基站企标-NE支持5QI分流__5QI分流规则优先级验证__王雷10070438__null__MEC-96830
master;NodeEngine测试用例集;确定性工业基站;【MEC-100211】工业基站企标-NE支持5QI分流__异常流程测试__王雷10070438__null__MEC-88420
master;NodeEngine测试用例集;分流特性;VFW(切conap之后)__VFW组网，验证数据包过滤__林成10249249__null__MEC-103987
master;NodeEngine测试用例集;分流特性;VFW(切conap之后)__VFW组网，验证防DOS攻击__林成10249249__null__MEC-95442
master;NodeEngine测试用例集;分流特性;VFW(切conap之后)__VFW组网，验证终端之间互访基本业务__林成10249249__null__MEC-103346
master;NodeEngine测试用例集;分流特性;【MEC-66002】【支持GRE over Ipsec分流隧道】__【V9200】【IPSEC]性能测试--分别验证单站以及多站3g上下行灌包测试包括UDP和TCP__王焕萍00120295__null__MEC-77476
master;NodeEngine测试用例集;分流特性;【MEC-66002】【支持GRE over Ipsec分流隧道】__上行回传-IPSEC下不同报文测试512字节和1500字节-分片__王焕萍00120295__null__MEC-77482
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - IPV4 DNS分流规则配置__金施嘉珞10332996__null__MEC-40247
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - API性能测试__金施嘉珞10332996__null__MEC-40242
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - 配置应用级别带宽管理规则__金施嘉珞10332996__null__MEC-40244
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - 配置session级别带宽管理规则__金施嘉珞10332996__null__MEC-40245
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - 同时查询和删除多个DNS分流规则__金施嘉珞10332996__null__MEC-40246
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - API接口异常流程测试__金施嘉珞10332996__null__MEC-40241
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - 同时查询和删除多个带宽管理规则__金施嘉珞10332996__null__MEC-40243
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - IPV6 DNS分流规则配置__金施嘉珞10332996__null__MEC-40248
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - 同时查询和删除多个五元组分流规则配置__金施嘉珞10332996__null__MEC-40249
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - IPV6五元组分流规则配置__金施嘉珞10332996__null__MEC-40250
master;NodeEngine测试用例集;API能力开放;2005004 第三方mp2接口__MP2分流能力开放 - IPV4五元组分流规则配置__金施嘉珞10332996__null__MEC-40251
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，slaMeasEnable置为关，大屏除了app速率其他卡片没有数据上报__金施嘉珞10332996__null__MEC-40214
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__删除APPQosControl表操作__金施嘉珞10332996__null__MEC-40211
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，有5QI配置，业务正常__金施嘉珞10332996__null__MEC-40208
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，servicepattern取值遍历覆盖测试__金施嘉珞10332996__null__MEC-40213
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__修改APPQosControl表操作，修改5QI配置，业务正常__金施嘉珞10332996__null__MEC-40209
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，没有5QI配置，业务正常__金施嘉珞10332996__null__MEC-40212
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__查询APPQosControl表操作__金施嘉珞10332996__null__MEC-40210
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，slaMeasEnable置为ICMP，大屏有数据上报__金施嘉珞10332996__null__MEC-40215
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，slaMeasEnable置为UDP，大屏有数据上报__金施嘉珞10332996__null__MEC-40216
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加APPQosControl表操作，e2elatency/latencykpi/5qi三个参数带2个或1个测试，业务正常__金施嘉珞10332996__null__MEC-40218
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__修改TrafficModel表操作，IPI开关关，E2口下发静态TM__金施嘉珞10332996__null__MEC-40220
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__增加TrafficModel表操作，IPI开关关，E2口下发静态TM__金施嘉珞10332996__null__MEC-40219
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__查询TrafficModel表操作__金施嘉珞10332996__null__MEC-40221
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__删除TrafficModel表操作__金施嘉珞10332996__null__MEC-40222
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__反复增删改查APPQosControl表操作，多APP curl反复执行，终端访问该APP业务正常__金施嘉珞10332996__null__MEC-40223
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__UE级业务接口特征测试__金施嘉珞10332996__null__MEC-52096
master;NodeEngine测试用例集;API能力开放;2205002 第三方sla签约接口__ue级下行触发上行业务特征__金施嘉珞10332996__null__MEC-56316
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-App测量信息查询（30s）__金施嘉珞10332996__null__MEC-40266
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试响应码优化__金施嘉珞10332996__null__MEC-40263
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-Ue测量信息查询__金施嘉珞10332996__null__MEC-40265
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-100个Ue测量信息查询__金施嘉珞10332996__null__MEC-40264
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA 度量能力开放-API功能测试-UE测量信息订阅__金施嘉珞10332996__null__MEC-40268
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-App测量信息查询（900s）__金施嘉珞10332996__null__MEC-40267
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-App测量信息订阅__金施嘉珞10332996__null__MEC-40269
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-API功能测试-去订阅__金施嘉珞10332996__null__MEC-40270
master;NodeEngine测试用例集;API能力开放;2205003 第三方sla测量查询接口__SLA度量能力开放-100个UE测量信息订阅__金施嘉珞10332996__null__MEC-40271
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-70066 【5G LAN】v1100支持ethernet PDU SESSION类型的分流功能__RT-【5G LAN】ue互访业务测试__沈函10333705__null__MEC-109311
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-70066 【5G LAN】v1100支持ethernet PDU SESSION类型的分流功能__RT-【5G LAN】按vlanid分流业务测试__沈函10333705__null__MEC-108064
master;NodeEngine测试用例集;分流特性;【MEC-123775】 【故障转需求】【5G LAN】STOF支持拷机场景CB场景修改或重新接入业务正常__【RT】【5GLAN】验证UE多次重接后本地PDN业务能正常恢复__沈函10333705__null__MEC-147766
master;NodeEngine测试用例集;分流特性;【MEC-123775】 【故障转需求】【5G LAN】STOF支持拷机场景CB场景修改或重新接入业务正常__【RT】【5GLAN】验证VNG配置变更后PDN业务能正常恢复__沈函10333705__null__MEC-147767
master;NodeEngine测试用例集;分流特性;切换用例__【RT】【切换】XN切换后切回__许尧10225649__null__MEC-148077
master;NodeEngine测试用例集;分流特性;切换用例__【RT】【切换】反复XN切换拷机测试__许尧10225649__null__MEC-148078
master;NodeEngine测试用例集;分流特性;本地分流基础DT用例;上行报文规则__目的IP规则-直接转发__肖凯10294120__null__MEC-137571
master;NodeEngine测试用例集;分流特性;本地分流基础DT用例;上行报文规则__目的IP规则-NAPT__肖凯10294120__null__MEC-130141
master;NodeEngine测试用例集;分流特性;本地分流基础DT用例;上行报文规则__目的IP规则-静态NAT转换__肖凯10294120__null__MEC-137582
master;NodeEngine测试用例集;分流特性;本地分流基础DT用例;上行报文规则__目的IP规则-静态网络地址端口转换__肖凯10294120__null__MEC-137547
master;NodeEngine测试用例集;分流特性;本地分流基础DT用例;上行报文规则__目的IP规则-动态NAT地址转换__肖凯10294120__null__MEC-130124
master;NodeEngine测试用例集;分流特性;【MEC-114998】NE支持GRE断链后重新获取基站承载信息__GRE断链超过8s，终端不重新接入__毛玉春10320555__null__MEC-109575
master;NodeEngine测试用例集;分流特性;【MEC-114998】NE支持GRE断链后重新获取基站承载信息__GRE断链超过8s，终端重新接入__毛玉春10320555__null__MEC-109574
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-5G LAN新CB组网方案模型（正常值）__沈函10333705__null__MEC-101613
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-5G LAN新CB组网方案模型1（正常值）__沈函10333705__null__MEC-100175
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-5G LAN新CB组网方案模型2（正常值）__沈函10333705__null__MEC-99619
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-5G LAN新CB和非CB组网方案模型（正常值）__沈函10333705__null__MEC-93485
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-SubIfL2表关键字修改为ifId和vlanId的组合（正常值）__沈函10333705__null__MEC-101008
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__DT-非CB之间vlanid不能有交集校验（异常值）__沈函10333705__null__MEC-102253
master;NodeEngine测试用例集;分流特性;【5G LAN】STOF支持802.1CB报文转发__FT-VNGroup.vlanId取异常值校验3 copy 2024-05-07 17:09:09.645759__沈函10333705__null__MEC-92827
master;NodeEngine测试用例集;分流特性;MEC-88900【5G LAN】支持按vlan进行VN组用户管理__【RT】【9200】【5G LAN】【SE6100】支持按vlan进行VN组用户管理本地业务测试__沈函10333705__null__MEC-85464
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-70066 【5G LAN】v1100支持ethernet PDU SESSION类型的分流功能__RT-【5G LAN】L2转发和非子卡L3转发业务共存验证__沈函10333705__null__MEC-109276
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__TSN环境，基于Arrival Time的精确预调度测试，新基站版本+新NE版本，ping包测试__朱静00241660__null__MEC-80626
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__TSN环境，基于Arrival Time的精确预调度测试，新基站版本+新NE版本，UDP灌包测试__朱静00241660__null__MEC-80627
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-71538 【能力开放流程&接口优化--基于Arrival Time和Air Time的精确预调度功能新架构适配】__TSN环境，基于Arrival Time的精确预调度测试，新基站版本+新NE版本，TCP灌包测试__朱静00241660__null__MEC-80628
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-61010: 【TSN】NE从基站获取时隙调度资源配置信息__TSN查询UE的小区信息，RNIS上报正常__许尧10225649__null__MEC-62296
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-61010: 【TSN】NE从基站获取时隙调度资源配置信息__偶连断后恢复触发审计时NE重新订阅帧结构和时间信息__许尧10225649__null__MEC-62295
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;MEC-61010: 【TSN】NE从基站获取时隙调度资源配置信息__帧结构发生变化，周期（暂定1秒）后NE重新订阅帧结构和时间信息__许尧10225649__null__MEC-62294
master;NodeEngine测试用例集;分流特性;MEC-60841TSN需求—STOF打通到NW-TT的用户面通路__【通用】【TSN】SE6100验证单UE TSN报文__王焕萍00120295__null__MEC-61419
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;异常场景__(RT)TSN环境，TSN子卡各种异常场景下，上下行功能验证__邓立平10125499__null__MEC-69717
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;异常场景__(RT)TSN环境，时钟参数反复修改，上下行功能验证__邓立平10125499__null__MEC-69718
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，10ms周期，不启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-66981
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，不启动CB，终端在极好点(基站PRB利用率50%)，单用户单流上下行性能验证__邓立平10125499__null__MEC-66979
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，不启动CB，终端在极好点(基站PRB利用率20%)，单用户单流上下行性能验证__邓立平10125499__null__MEC-66978
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，不启动CB，终端在极好点(基站PRB利用率10%)，单用户单流上下行性能验证__邓立平10125499__null__MEC-66980
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，不启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-66460
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，不启动CB，单用户单流下行性能验证__邓立平10125499__null__MEC-66461
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，不启动CB，单用户单流下行性能验证__邓立平10125499__null__MEC-66458
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，不启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-66453
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，不启动CB，单用户单流上下行性能验证__邓立平10125499__null__MEC-66101
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户单流场景__(RT)TSN环境，20ms周期，不启动CB，两端口编排，单用户单流上下行性能验证__邓立平10125499__null__MEC-69702
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户多流场景__(RT)TSN环境，20ms周期，包长1300字节，不启动CB，单端口编排，单用户多流（两端口各出一流）上下行性能验证__邓立平10125499__null__MEC-69708
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN子卡的tsnGmSource为TSN，单用户单流上下行性能验证__邓立平10125499__null__MEC-83680
master;NodeEngine测试用例集;TSN特性;Q3 无害场景__(RT)TSN环境下，NW不启动TSN编排功能，业务经过NW-TT功能验证__邓立平10125499__null__MEC-65176
master;NodeEngine测试用例集;TSN特性;Q1 TSN多用户场景__(RT)TSN环境，数据流(NwttStream)满配，单用户上行编排验证__邓立平10125499__null__MEC-81955
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户单流场景__(RT)TSN环境，20ms周期，启动CB，两端口编排，单用户单流上下行性能验证__邓立平10125499__null__MEC-69701
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户单流场景__(RT)TSN环境，16ms周期，启动CB，单用户单流上下行性能验证__邓立平10125499__null__MEC-69383
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户单流场景__(RT)TSN环境，16ms周期，启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-69382
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户单流场景__(RT)TSN环境，16ms周期，启动CB，单用户单流下行性能验证__邓立平10125499__null__MEC-69381
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，启动CB，单用户单流上下行性能验证__邓立平10125499__null__MEC-66734
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，启动CB，单用户单流下行性能验证__邓立平10125499__null__MEC-66737
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，启动CB，单用户单流上下行性能验证__邓立平10125499__null__MEC-66733
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-66735
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，20ms周期，启动CB，单用户单流下行性能验证__邓立平10125499__null__MEC-66736
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)TSN环境，30ms周期，启动CB，单用户单流上行性能验证__邓立平10125499__null__MEC-66738
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;多用户单流场景__(RT)TSN环境，20ms周期，包长1300字节，启动CB，单端口编排，多用户均单流上行性能验证__邓立平10125499__null__MEC-69711
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;单用户多流场景__(RT)TSN环境，20ms周期，包长1300字节，启动CB，单端口编排，单用户多流（两端口各出一流）上下行性能验证__邓立平10125499__null__MEC-69709
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)UE互访，不启动CB，20ms的RTT业务，100byte单流性能验证__邓立平10125499__null__MEC-67632
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)UE互访，不启动CB，20ms的RTT业务，1200byte双流性能验证__邓立平10125499__null__MEC-67512
master;NodeEngine测试用例集;TSN特性;Q1 TSN多用户场景__(RT)TSN环境，不启动CB，多用户分别单流，上下行单流编排验证__邓立平10125499__null__MEC-66098
master;NodeEngine测试用例集;TSN特性;Q2 TSN性能(单端口)__(RT)UE互访，不启动CB，20ms的UDP业务，100byte单流性能验证__邓立平10125499__null__MEC-67545
master;NodeEngine测试用例集;TSN特性;Q4 融合用例;多用户单流场景__(RT)TSN环境，20ms周期，包长1300字节，不启动CB，单端口编排，多用户均单流下行性能验证__邓立平10125499__null__MEC-69712
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttTunnel表约束验证__邓立平10125499__null__MEC-68710
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttTunnel中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67358
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttVNIMapping中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67367
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttFmi中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67362
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttTsnTransCfg表约束验证__邓立平10125499__null__MEC-67386
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttPTPPort表约束验证__邓立平10125499__null__MEC-68708
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttPTPGlobal中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67363
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttPTPPort中的moid范围和其他值生效验证__邓立平10125499__null__MEC-68704
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttSgi表约束验证__邓立平10125499__null__MEC-68709
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttSgi中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67370
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttSfi中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67354
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttCb表约束验证__邓立平10125499__null__MEC-68707
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttStream中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67373
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttCb中的moid范围和其他值生效验证__邓立平10125499__null__MEC-67371
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttQbv表约束验证__邓立平10125499__null__MEC-68706
master;NodeEngine测试用例集;TSN特性;M7 约束场景__NwttControlPolicy中的moid范围和其他值生效验证__邓立平10125499__null__MEC-68705
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;配置需求__NE实例化后，GlbPara表中增加一条记录-v1100__许尧10225649__null__MEC-62193
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V1100环境，EdgeQos支持同时配置及修改UE级上下行带宽保障__朱静00241660__null__MEC-65862
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V1100环境，EdgeQos支持同时配置及修改APP级上下行带宽保障__朱静00241660__null__MEC-65860
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V1100环境，EdgeQos支持同时配置及修改5QI和APP级上下行带宽保障__朱静00241660__null__MEC-65861
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-59043&MEC-50738: NE9200和V1100支持下行和上下行带宽类业务保障__V1100环境，EdgeQos支持同时配置及修改5QI和UE级上下行带宽保障__朱静00241660__null__MEC-65863
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;配置需求__NE实例化后，增加5qi202的固化配置-v1100__许尧10225649__null__MEC-62187
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-57363  【V1100】建议固化并支持简单MO配置需求__V1100的edgeqos业务正常__王焕萍00120295__null__MEC-69687
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61062:【精准网络-NE行业大脑】基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障（策略）__V1100环境，协议类型为UDP，APPQoSCtrl保障，终端做UDP灌包业务，时延和丢包率不达标，但调整目标超过基站能力则不下发期望目标值和调整增量__朱静00241660__null__MEC-66172
master;NodeEngine测试用例集;EdgeQos;新需求用例设计目录;市场需求 MEC-61062:【精准网络-NE行业大脑】基站支持根据NE下发的业务特征及QoS调整指示执行SLA保障（策略）__V1100环境，协议类型为UDP，UEQoSCtrl保障，终端做UDP灌包业务，时延和丢包率不达标，但调整目标超过基站能力则不下发期望目标值和调整增量__朱静00241660__null__MEC-66174
master;V1100测试用例集;MEC-128384  NE@V1100安全需求_本地分流接口支持vFW功能__VFW开启下，验证ACL（IPV4）功能__林成10249249__null__MEC-142650
master;V1100测试用例集;MEC-128384  NE@V1100安全需求_本地分流接口支持vFW功能__VFW开启下，验证ACL（IPV6）功能__林成10249249__null__MEC-142655
master;V1100测试用例集;维测需求__V1100vxlan业务，上下行UDP业务（IPV4业务，包长1500字节）__林成10249249__null__MEC-93264
master;V1100测试用例集;维测需求__V1100vxlan业务，上下行TCP业务（IPV4业务）__林成10249249__null__MEC-93265
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-61213 V1100 STOF和基站链路断告警上报到IDos和UME__[V1100]STOF和基站链路断告警上报__王焕萍00120295__null__MEC-61439
master;NodeEngine测试用例集;分流特性;V1100 分流需求;MEC-57363  【V1100】建议固化并支持简单MO配置需求__V1100的分流业务正常__王焕萍00120295__null__MEC-69686
master;NodeEngine测试用例集;分流特性;MEC-98997: 【TCF极致版功能】TCFAdapter获取VGCd面板光口光功率__[V1100]面板口出口连接10G光模块，获取光功率__王焕萍00120295__null__MEC-114830
master;NodeEngine测试用例集;分流特性;【MEC-87701】【NE维测】分流支持实时测量计数器__【1100】分流支持63046所属实时/历史测量计数器__沈函10333705__null__MEC-86871
master;NodeEngine测试用例集;分流特性;【MEC-87701】【NE维测】分流支持实时测量计数器__【1100】分流支持63062所属实时/历史测量计数器__沈函10333705__null__MEC-86872
master;NodeEngine测试用例集;L2组网特性;Vxlan;VxlanAC Radius强密码约束__VxlanAC Radius强密码校验-V9200__张盼10295980__null__MEC-78591
master;NodeEngine测试用例集;L2组网特性;Frer;VPP提供buffer接口__【RT】V1100环境，vxlangw内存使用计数器和KPI正确上报__张盼10295980__null__MEC-83971
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__单SE建立Vxlan隧道，Action命令查询__张盼10295980__null__MEC-65295
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__不开启Vxlan，Action命令查询__张盼10295980__null__MEC-65294
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__多SE使用相同VNI相同组接入Vxlan隧道，Action命令查询&计数器查询__张盼10295980__null__MEC-65298
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__多SE使用不同VNI相同组接入Vxlan隧道，Action命令查询&计数器查询__张盼10295980__null__MEC-65299
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__多SE使用相同VNI不同组接入Vxlan隧道，Action命令查询&计数器查询__张盼10295980__null__MEC-65296
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__多SE使用不同VNI不同组接入Vxlan隧道，Action命令查询&计数器查询__张盼10295980__null__MEC-65297
master;NodeEngine测试用例集;L2组网特性;Frer;VPP提供buffer接口__【RT】NE复位场景，vxlangw内存使用计数器和KPI正确上报__张盼10295980__null__MEC-83973
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【RT】多SE vxlan场景，Vxlan计数器查询__张盼10295980__null__MEC-96328
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【RT】多SE多VNI 动静态vxlan场景，Vxlan Action查询__张盼10295980__null__MEC-95991
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【RT】多SE多VNI动态vxlan+FRER场景，删除恢复网桥域配置，隧道正常删建__张盼10295980__null__MEC-107623
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【RT】多SE单VNI动态vxlan+FRER场景，开启关闭开关，隧道正常删建__张盼10295980__null__MEC-107742
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【RT】SE9102vxlan、SE6100Vxlan、SE9102FRER共存场景，ueryL2FibTable、queryVxlanTunnelCfg和queryFrerTunnelCfg查询__张盼10295980__null__MEC-141588
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【RT】【V1100】SE9102 Vxlan隧道场景，queryL2FibTable和queryVxlanTunnelCfg查询__张盼10295980__null__MEC-141592
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【DT】单SE Radius认证vxlan场景，Vxlan Action查询__张盼10295980__null__MEC-95895
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【DT】单SE动态vxlan场景，开启关闭vxlan开关，隧道正常删建__张盼10295980__null__MEC-108729
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【DT】单SE动态vxlan场景，删除恢复网桥域配置，隧道正常删建__张盼10295980__null__MEC-108726
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【DT】单SE FRER场景，删除恢复网桥域配置，隧道正常删建__张盼10295980__null__MEC-108735
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【DT】单SE FRER场景，开启关闭frer开关，隧道正常删建__张盼10295980__null__MEC-108738
master;NodeEngine测试用例集;L2组网特性;Vxlan;vxlan动态隧道删除关联__【DT】对接6100 TSN动态vxlan场景，开启关闭vxlan开关，隧道正常删建__张盼10295980__null__MEC-108732
master;NodeEngine测试用例集;L2组网特性;Frer;VPP提供buffer接口__【DT】vxlangw复位，内存使用计数器和KPI正确上报__张盼10295980__null__MEC-83964
master;NodeEngine测试用例集;L2组网特性;Frer;VPP提供buffer接口__【DT】9200环境，vxlangw内存使用计数器和KPI正确上报__张盼10295980__null__MEC-83966
master;NodeEngine测试用例集;L2组网特性;Frer;VPP提供buffer接口__【DT】Vxlangw内存使用计数器和KPI与告警上报结果一致__张盼10295980__null__MEC-83968
master;NodeEngine测试用例集;L2组网特性;Frer;vxlangw内存告警上报__9200环境，vxlangw内存超限告警上报清除__张盼10295980__null__MEC-76480
master;NodeEngine测试用例集;L2组网特性;Frer;vxlangw内存告警上报__AMP复位，vxlangw内存超限告警上报清除__张盼10295980__null__MEC-76478
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】Vxlan（静态隧道）单独使用，不上报FRER计数器__张盼10295980__null__MEC-62091
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】Vxlan（动态隧道）单独使用，不上报FRER计数器__张盼10295980__null__MEC-62109
master;NodeEngine测试用例集;L2组网特性;Frer;FRER支持多VNI场景__[DT]单SE多VNI场景，Frer隧道建立流程__张盼10295980__null__MEC-78521
master;NodeEngine测试用例集;L2组网特性;Frer;FRER支持多VNI场景__[DT]单SE多VNI单链路场景，Frer隧道删除流程__张盼10295980__null__MEC-83623
master;NodeEngine测试用例集;L2组网特性;Frer;FRER支持多VNI场景__[DT]单SE多VNI双链路场景，Frer隧道删除流程__张盼10295980__null__MEC-83619
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[DT]反复重启vxlangw和NFOAM容器，FRER Action命令查询__张盼10295980__null__MEC-65234
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[DT]单SE单VNI单无线链路场景，FRER Action命令查询__张盼10295980__null__MEC-65229
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[DT]单SE单VNI双无线链路场景，FRER Action命令查询__张盼10295980__null__MEC-65227
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[DT]单SE多VNI双无线场景，FRER Action命令查询__张盼10295980__null__MEC-65231
master;NodeEngine测试用例集;L2组网特性;Frer;Frer Action查询__[DT]认证失败场景，Frer Action命令查询__张盼10295980__null__MEC-65230
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】单SE单VNI场景，上下行配置All__张盼10295980__null__MEC-62092
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】单SE单VNI场景，上下行单发__张盼10295980__null__MEC-62095
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】单SE多VNI场景，Frer计数器上报__张盼10295980__null__MEC-62096
master;NodeEngine测试用例集;L2组网特性;Frer;Frer计数器__【DT】NE侧容器重启恢复，SE侧重启，Frer计数器上报__张盼10295980__null__MEC-62099
master;NodeEngine测试用例集;L2组网特性;Vxlan;子接口配置__单VNI带vlan场景，二三层子接口不能同时配置不配vlan__张盼10295980__null__MEC-69224
master;NodeEngine测试用例集;L2组网特性;Vxlan;6100Vxlangw__SE9102 vxlan静态隧道-本地认证，单CPE访问与园区APP（主动上行业务和主动下行业务分别覆盖）__张盼10295980__null__MEC-69320
master;NodeEngine测试用例集;L2组网特性;Vxlan;6100Vxlangw__SE9102 FRER上下行双发业务__张盼10295980__null__MEC-69322
master;NodeEngine测试用例集;L2组网特性;Vxlan;TSN6100__TSN Vxlan动态隧道-本地认证，认证失败场景验证__张盼10295980__null__MEC-68687
master;NodeEngine测试用例集;L2组网特性;Vxlan;TSN6100__TSN Vxlan动态隧道，异常重启场景测试（SE6100/Vxlanac/STOF）__张盼10295980__null__MEC-68691
master;NodeEngine测试用例集;L2组网特性;Frer;vxlangw内存告警上报__【RT】vxlangw复位，清除当前告警信息__张盼10295980__null__MEC-71374
master;NodeEngine测试用例集;L2组网特性;Vxlan;UME抓包__【DT】异常场景，UME下发任务失败__张盼10295980__null__MEC-83747
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【DT】9102静态vxlan场景，vxlan计数器查询__张盼10295980__null__MEC-96325
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【DT】9102动态vxlan场景，vxlan计数器查询__张盼10295980__null__MEC-96318
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【DT】6100动态vxlan场景，vxlan计数器查询__张盼10295980__null__MEC-96322
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlangw解析SE6100时间戳__【DT】SE6100不带vlan的vxlan场景，解析报文时间戳__张盼10295980__null__MEC-86932
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlangw解析SE6100时间戳__【DT】SE6100反复接入，业务IPV6场景，解析报文时间戳__张盼10295980__null__MEC-86935
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【DT】SE9102 Vxlan隧道场景，queryL2FibTable和queryVxlanTunnelCfg查询__张盼10295980__null__MEC-141569
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【DT】SE9102 FRER单多VNI场景，queryL2FibTable和queryFrerTunnelCfg查询__张盼10295980__null__MEC-141583
master;NodeEngine测试用例集;L2组网特性;Vxlan;MEC-87076 【NE维测】对接SE6100，支持设备MAC地址和VXLAN隧道关系上报NE__【DT】SE6100 CB隧道开启关闭场景，queryL2FibTable和queryVxlanTunnelCfg查询__张盼10295980__null__MEC-141577
master;NodeEngine测试用例集;L2组网特性;Frer;VXLANGW大消息处理__VxlanAC用户配置增删改__张盼10295980__null__MEC-85815
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【DT】vxlan场景，VxlanAC网管配置增删改__张盼10295980__null__MEC-92951
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【DT】FRER场景，VxlanGW网管配置增删改__张盼10295980__null__MEC-93120
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【DT】静态Vxlan场景，VxlanGW网管配置增删改__张盼10295980__null__MEC-83562
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【DT】动态Vxlan场景，VxlanGW网管配置增删改__张盼10295980__null__MEC-83572
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【DT】vxlan场景，单VNI多缺省二层子接口配置__张盼10295980__null__MEC-95500
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【DT】vxlan场景，多VNI多缺省二层子接口配置__张盼10295980__null__MEC-103988
master;NodeEngine测试用例集;L2组网特性;Vxlan;动静态vxlan隧道切换__【DT】动态Vxlan隧道切换静态隧道正常__张盼10295980__null__MEC-95837
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER时间戳场景验证__张盼10295980__null__MEC-97322
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER业务vlan场景验证__张盼10295980__null__MEC-97250
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER单双链路切换场景验证__张盼10295980__null__MEC-97489
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER打开关闭CB场景验证__张盼10295980__null__MEC-97325
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER认证用户场景验证__张盼10295980__null__MEC-97495
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER异常场景验证__张盼10295980__null__MEC-97748
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER用户与9102软件Vxlan用户互访__张盼10295980__null__MEC-97744
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【DT】6100软件FRER用户与9102软件FRER用户互访__张盼10295980__null__MEC-97746
master;NodeEngine测试用例集;L2组网特性;Vxlan;Gre隧道类型_MEC-8640__GRE隧道为IPV4地址，VXLAN业务测试__张盼10295980__null__MEC-65286
master;NodeEngine测试用例集;L2组网特性;Vxlan;Gre隧道类型_MEC-8640__GRE隧道为IPV6地址，VXLAN业务测试__张盼10295980__null__MEC-65287
master;NodeEngine测试用例集;L2组网特性;Vxlan;TSN6100__TSN Vxlan动态隧道和Vxlangw vxlan隧道共存场景测试__张盼10295980__null__MEC-68693
master;NodeEngine测试用例集;L2组网特性;Vxlan;6100Vxlangw__SE6100 Vxlan动态隧道-本地认证，单CPE访问与园区APP（主动上行业务和主动下行业务分别覆盖）__张盼10295980__null__MEC-69318
master;NodeEngine测试用例集;L2组网特性;Vxlan;6100Vxlangw__SE6100和SE9102关联同一个VNI建立Vxlan隧道__张盼10295980__null__MEC-69505
master;NodeEngine测试用例集;L2组网特性;Vxlan;6100Vxlangw__SE6100和SE9102关联不同VNI建立Vxlan隧道__张盼10295980__null__MEC-68038
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;9102和6100互访__【RT】vxlan+Frer场景，9102和6100不同VNI__张盼10295980__null__MEC-81897
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;9102和6100互访__【RT】vxlan场景，9102和6100不同VNI，SE出口不带vlan__张盼10295980__null__MEC-91264
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;9102和6100互访__【RT】vxlan场景，9102和6100相同VNI，不带vlan__张盼10295980__null__MEC-90762
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;9102和6100互访__【RT】vxlan+Frer场景，9102和6100相同VNI__张盼10295980__null__MEC-91308
master;NodeEngine测试用例集;L2组网特性;Frer;FRER业务场景测试（26）;9102和6100互访__【RT】vxlan场景，9102和6100相同VNI，带vlan__张盼10295980__null__MEC-90815
master;NodeEngine测试用例集;L2组网特性;Frer;6100软件FRER__【RT】6100软件FRER用户与6100软件Vxlan用户互访__张盼10295980__null__MEC-97764
master;NodeEngine测试用例集;L2组网特性;Vxlan;STOF开启TCP MSS协商代理__VGCG1+9102场景，NE启动TCP MSS协商代理，终端到本地vxlan TCP大包验证__张盼10295980__null__MEC-80508
master;NodeEngine测试用例集;L2组网特性;Vxlan;STOF开启TCP MSS协商代理__VGCG1+9102场景，NE启动TCP MSS协商代理，终端到本地FRER大包验证（无害验证）__张盼10295980__null__MEC-80560
master;NodeEngine测试用例集;L2组网特性;Vxlan;STOF开启TCP MSS协商代理__VGCG1+6100场景，NE启动TCP MSS协商代理，终端到本地vxlan TCP大包验证__张盼10295980__null__MEC-80554
master;NodeEngine测试用例集;L2组网特性;Vxlan;STOF开启TCP MSS协商代理__VGCG1+9102场景，NE启动TCP MSS协商代理，终端互访Vxlan TCP大包验证__张盼10295980__null__MEC-80517
master;NodeEngine测试用例集;L2组网特性;Vxlan;认证方式_MEC-8432__本地认证，单CPE访问与园区APP（主动上行业务和主动下行业务分别覆盖）__张盼10295980__null__MEC-65278
master;NodeEngine测试用例集;L2组网特性;Vxlan;认证方式_MEC-8432__Radius认证，单CPE访问与园区APP（主动上行业务和主动下行业务分别覆盖）__张盼10295980__null__MEC-65280
master;NodeEngine测试用例集;L2组网特性;Vxlan;性能对比测试_MEC-8314__VxLAN开关开与关，单CPE访问与园区APP主动上行FTP上传/下载速率对比测试__张盼10295980__null__MEC-65291
master;NodeEngine测试用例集;L2组网特性;Vxlan;性能对比测试_MEC-8314__VxLAN开关开与关，单CPE访问与园区APP环回ping包时延对比测试__张盼10295980__null__MEC-65290
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【RT】vxlan场景，部分带vlan tag业务配置__张盼10295980__null__MEC-81863
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【RT】多SE多VNI vxlan场景，层二子接口数据正常传输__张盼10295980__null__MEC-81868
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【RT】vxlan场景，只有不带vlan tag业务配置__张盼10295980__null__MEC-89933
master;NodeEngine测试用例集;L2组网特性;Vxlan;二层子接口统一配置__【RT】vxlan场景，只有带vlan tag业务配置__张盼10295980__null__MEC-90044
master;NodeEngine测试用例集;L2组网特性;Vxlan;网管参数支持增删改__【RT】V1100动态Vxlan场景，VxlanGW网管配置增删改__张盼10295980__null__MEC-92589
master;NodeEngine测试用例集;L2组网特性;Vxlan;VXLAN网管网桥域BDID（网桥ID配置）配置成合适值_MEC-135806__【RT】新版本配置BDID为65535，同时做Vxlan和Frer业务__张盼10295980__null__MEC-137042
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【RT】【V1100】9102动态vxlan场景，vxlan计数器查询__张盼10295980__null__MEC-96331
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【RT】【V1100】动态vxlan场景，vxlan用户数指标上报__张盼10295980__null__MEC-126750
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__【RT】多SE vxlan场景，vxlan用户数指标上报__张盼10295980__null__MEC-127739
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE相同组，相同VNI；访问同一app；CPE互访通__张盼10295980__null__MEC-65250
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE相同组，相同VNI；访问不同app（同网桥，同vlan)；CPE互访通__张盼10295980__null__MEC-65251
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE相同组，相同VNI；访问不同app（同网桥，不同vlan）__张盼10295980__null__MEC-65252
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE不同组，相同VNI，访问同一app；CPE互访通__张盼10295980__null__MEC-65253
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE不同组，相同VNI，访问不同app（同网桥，同vlan）__张盼10295980__null__MEC-65254
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE不同组，相同VNI，访问不同app（同网桥，不同vlan）__张盼10295980__null__MEC-65255
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-多CPE不同组，不同VNI，访问不同app（不同网桥，不同vlan）；CPE互访不通__张盼10295980__null__MEC-65256
master;NodeEngine测试用例集;L2组网特性;Vxlan;动态Vxlan隧道，业务测试_MEC-8314__动态Vxlan隧道-NE测不带vlan__张盼10295980__null__MEC-67746
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE相同组，相同VNI；访问同一app；CPE互访通__张盼10295980__null__MEC-65267
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE相同组，相同VNI；访问不同app（同网桥，同vlan)；CPE互访通__张盼10295980__null__MEC-65266
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE相同组，相同VNI；访问不同app（同网桥，不同vlan）__张盼10295980__null__MEC-65265
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE不同组，相同VNI，访问同一app；CPE互访通__张盼10295980__null__MEC-65269
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE不同组，相同VNI，访问不同app（同网桥，同vlan）__张盼10295980__null__MEC-65264
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE不同组，相同VNI，访问不同app（同网桥，不同vlan）__张盼10295980__null__MEC-65268
master;NodeEngine测试用例集;L2组网特性;Vxlan;静态Vxlan隧道，业务测试_MEC-8314__静态Vxlan隧道-多CPE不同组，不同VNI，访问不同app（不同网桥，不同vlan）；CPE互访不通__张盼10295980__null__MEC-65270
master;NodeEngine测试用例集;L2组网特性;Vxlan;动静态vxlan隧道切换__【RT】删除静态隧道，不影响动态隧道__张盼10295980__null__MEC-95847
master;NodeEngine测试用例集;L2组网特性;Vxlan;子接口配置__多VNI不带vlan场景，二三层子接口不能同时配置相同vlan__张盼10295980__null__MEC-69229
master;NodeEngine测试用例集;L2组网特性;Vxlan;异常测试_MEC-8314__容器异常重启（MQTTServer容器/Vxlangw/Vxlanac/STOF容器）__张盼10295980__null__MEC-65320
master;NodeEngine测试用例集;L2组网特性;Vxlan;异常测试_MEC-8314__SE侧反复重新建立vxlan隧道__张盼10295980__null__MEC-65318
master;NodeEngine测试用例集;L2组网特性;Vxlan;异常测试_MEC-8314__NE侧表空间满配后的业务测试__张盼10295980__null__MEC-65319
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__告警测试-和MQTT通信异常__张盼10295980__null__MEC-65300
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan Action命令&告警&计数器测试_MEC-18589__MQTT和vxlanAC重启，告警上报校验__张盼10295980__null__MEC-69661
master;NodeEngine测试用例集;L2组网特性;Vxlan;异常测试_MEC-8314__基站空口利用率高场景，Frer&Vxlan业务正常__张盼10295980__null__MEC-67778
master;NodeEngine测试用例集;L2组网特性;Vxlan;异常测试_MEC-8314__上下行极限流量场景，Frer隧道不删建__张盼10295980__null__MEC-67708
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan一键式采集日志_MEC-18536__UME一键式采集日志-vxlanac日志上报__张盼10295980__null__MEC-65308
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlan一键式采集日志_MEC-18536__UME一键式采集日志-vxlangw日志上报__张盼10295980__null__MEC-65309
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlangw解析SE6100时间戳__【RT】SE6100对接V1100场景，解析报文时间戳__张盼10295980__null__MEC-86937
master;NodeEngine测试用例集;L2组网特性;Vxlan;Vxlangw解析SE6100时间戳__【RT】SE6100 vxlan、SE9102 vxlan、SE9102 FRER共存场景，解析报文时间戳__张盼10295980__null__MEC-86941
master;NodeEngine测试用例集;L2组网特性;Vxlan;认证方式_MEC-8432__本地认证，认证失败场景验证__张盼10295980__null__MEC-65279
master;NodeEngine测试用例集;L2组网特性;Vxlan;认证方式_MEC-8432__Radius认证，认证失败场景验证__张盼10295980__null__MEC-65281
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;拆分自动化用例__（RT）基站制式复位，拷机测试__杨龙10306822__null__MEC-77551
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;拆分自动化用例__（RT）容器重启，拷机验证__杨龙10306822__null__MEC-76715
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;拆分自动化用例__（RT）OBU与vRSU上下行消息发送，拷机测试__杨龙10306822__null__MEC-76719
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;三段时延下沉__（RT）OBU收到VRSU发送的BDI消息，时延测试，拷机__杨龙10306822__null__MEC-69031
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;三段时延下沉__（RT）OBU收到VRSU发送的上行视频传输时延数据（RDI消息），时延测试__杨龙10306822__null__MEC-69030
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;高精度__（RT）站内异频切换测试，时延测试__杨龙10306822__null__MEC-67050
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;高精度__（RT）站间同频切换测试，时延测试__杨龙10306822__null__MEC-67053
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;efs网管参数配置__（RT）参数随机修改，异常复位场景烤机测试__杨龙10306822__null__MEC-68242
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;多摄像头融合__（RT)异常复位场景测试__杨龙10306822__null__MEC-84652
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M12 EVS;支持交通流量特征识别__(RT)参数随机修改，异常复位场景烤机测试__杨龙10306822__null__MEC-80399
master;NodeEngine测试用例集;车联网特性;M1 摄像头支持车联网;M11 VRSU;vRSU支持与OBU对接__（RT）OBU与vRSU上下行消息发送，拷机测试__董霞00190042__null__MEC-66967
