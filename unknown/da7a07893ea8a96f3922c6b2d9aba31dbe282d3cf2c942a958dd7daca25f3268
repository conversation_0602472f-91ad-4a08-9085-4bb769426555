# coding=utf-8

import os

from PyQt5.QtWidgets import QAction, QTableWidgetItem, QApplication

from controller.system_plugin.SignalDistributor import SignalDistributor
from model.CurrentItem import CurrentItem
from utility.UIRepository import UIRepository


class TableMenu(object):

    def __init__(self, table_obj):
        self._table_obj = table_obj
        self._table = table_obj._table

    def load_menu(self):
        self._menu = self._table_obj._menu
        self._menu.addAction(self._delete_rows_action)
#         self._menu.addAction(self._move_up_action)
#         self._menu.addAction(self._move_down_action)

    def load_shortcut(self):
        self._set_delete_rows()
#         self._set_move_up()
#         self._set_move_down()

    def _set_delete_rows(self):
        self._delete_rows_action = QAction('Delete')
        self._delete_rows_action.triggered.connect(self._delete_rows)
        self._table.addAction(self._delete_rows_action)

    def _delete_rows(self):
        if self._table.selectedIndexes():
            row = self._table.selectedIndexes()[0].row()
            self._table.removeRow(row)
            self._table_obj._current_row = row
            self._modify_data()

    def _set_move_up(self):
        self._move_up_action = QAction('Move Up')
        self._move_up_action.setShortcut('Ctrl+Up')
        self._move_up_action.triggered.connect(self._move_up)
        self._table.addAction(self._move_up_action)

    def _move_up(self):
        row = self._table.selectedIndexes()[0].row()
        if row == 0:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row, cols)
        for c in range(cols):
            self._table.setItem(row, c, QTableWidgetItem(row_data[c]))
            self._table.setItem(row - 1, c, QTableWidgetItem(prev_row_data[c]))
#         self._current_row = row - 1
#         self._modify_data()

    def _get_two_row_data(self, row, cols):
        row_data = []
        prev_row_data = []
        for c in range(cols):
            if self._table.item(row, c):
                row_data.append(self._table.item(row, c).text())
            else:
                row_data.append('')
            if self._table.item(row - 1, c):
                prev_row_data.append(self._table.item(row - 1, c).text())
            else:
                prev_row_data.append('')
        return prev_row_data, row_data

    def _set_move_down(self):
        self._move_down_action = QAction('Move Down')
        self._move_down_action.setShortcut('Ctrl+Down')
        self._move_down_action.triggered.connect(self._move_down)
        self._table.addAction(self._move_down_action)

    def _move_down(self):
        row = self._table.selectedIndexes()[0].row()
        if row == self._table.rowCount() - 1:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row + 1, cols)
        for c in range(cols):
            self._table.setItem(row, c, QTableWidgetItem(prev_row_data[c]))
            self._table.setItem(row + 1, c, QTableWidgetItem(row_data[c]))
#         self._current_row = row + 1
#         self._modify_data()

    def _modify_data(self):
        if self._table_obj._current_row is not None:
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'type': 'None', 'operator': 'modify'})
            self._table_obj._modify_data()
