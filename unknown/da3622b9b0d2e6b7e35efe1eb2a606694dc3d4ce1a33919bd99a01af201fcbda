# coding=utf8
'''
Created on 2019年10月18日

@author: 10140129
'''
from resources.Loader import Loader
from settings.UserSettings import UserSettings
from utility.log.Logger import get_plugin_logger,get_system_logger


class SystemLibrary(object):

    @staticmethod
    def test():
        print("demo plugin is activated successfully")

    @staticmethod
    def get_current_version():
        return UserSettings().get_value("VERSION")

    @staticmethod
    def get_image_path(image_name):
        return Loader().get_path(image_name)

    @staticmethod
    def get_log_handler(plugin_name=None):
        if plugin_name:
            logger = get_plugin_logger(plugin_name)
        else:
            logger = get_system_logger()
        return logger

    @staticmethod
    def log(msg, plugin_name=None, log_level='info'):
        logger = SystemLibrary.get_log_handler(plugin_name)
        if log_level == 'debug':
            logger.debug(msg)
        elif log_level == 'error':
            logger.error(msg)
        elif log_level == 'info':
            logger.info(msg)
        elif log_level == 'warn':
            logger.warn(msg)
        else:
            raise Exception('log_level {} not support'.format(log_level))


if __name__ == '__main__':
    SystemLibrary.log('333')
