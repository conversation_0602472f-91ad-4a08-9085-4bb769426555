# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
from PyQt5.QtWidgets import QVBoxLayout
from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.DefaultEdit import DefaultEdit


class UserKeywordItemEdit(DefaultEdit):

    def __init__(self, parent):
        super().__init__(parent)
        self._layout = None

    def load(self):
        if self._layout:
            return self._layout
        super().load()
        self._arguments = self._set_arguments_area()
        self._return_value = self._set_return_value_area()
        self._set_layout()
        return self._layout

    def _set_layout(self):
        self._layout = QVBoxLayout()
        self._layout.addLayout(self._path)
        self._layout.addLayout(self._settings)
        self._layout.addLayout(self._documentation.get_layout())
        self._layout.addLayout(self._arguments.get_layout())
        self._layout.addLayout(self._teardown.get_layout())
        self._layout.addLayout(self._timeout.get_layout())
        self._layout.addLayout(self._return_value.get_layout())
        self._layout.addLayout(self._table.get_layout())

    def _set_arguments_area(self):
        arguments = LineEditArea(self._parent_window, 'Arguments')
        arguments.load()
        return arguments

    def _set_return_value_area(self):
        return_value = LineEditArea(self._parent_window, 'Return Value')
        return_value.load()
        return return_value

    def fill_data(self, parsed_item):
        super().fill_data(parsed_item)
        # 对arguements做额外处理，为了让arguements为['', '']时未None，解决点击关键字标星号的问题
        arguments = None if isinstance(parsed_item.arguments, list) and parsed_item.arguments[0] == '' and parsed_item.arguments[1] == '' else parsed_item.arguments
        self._arguments.fill_data(arguments)
        self._return_value.fill_data(parsed_item.return_value)

    def _set_visible_area(self):
        super()._set_visible_area()
        self._arguments.set_visible(self._is_visible)
        self._return_value.set_visible(self._is_visible)
