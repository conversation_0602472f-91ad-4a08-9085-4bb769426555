# coding=utf-8
import atexit
import codecs
import os
import re
import shutil
import socketserver
import sys
import tempfile
import threading

from controller.test_runner.Process import Process
from controller.test_runner.StreamHandler import StreamHandler
from controller.test_runner.TestExecutionResults import TestExecutionResults
from pybot.PybotLoader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from robot.output.loggerhelper import LEVELS
from settings.LogProject import LogProject
from utility.CmdFormatter import format_normal_suite_path, \
    format_normal_test_case_path
from utility.LogPathRepository import LogPathRepository


# from context import IS_WINDOWS
# from robotide.robot import LOG_LEVELS
# from robot.utils.encoding import SYSTEM_ENCODING
ATEXIT_LOCK = threading.RLock()


class TestRunner(object):

    def __init__(self):
        self._output_dir = None
        self._process = None
        self._server = None
        self._server_thread = None
        self._pause_on_failure = False
        self._pid_to_kill = None
        self._results = TestExecutionResults()
        self.port = None
        self.profiles = {}

    @property
    def testState(self):
        return self._results.results

    def enable(self, result_handler):
        self._start_listener_server(result_handler)
        self._create_temporary_directory()

    def _create_temporary_directory(self):
        self._output_dir = LogPathRepository().find('output_dir')
        if not self._output_dir:
            self._output_dir = tempfile.mkdtemp(".d", "RIDE")
            LogPathRepository().add('output_dir', self._output_dir)
        else:
            if not os.path.exists(self._output_dir):
                self._output_dir = tempfile.mkdtemp(".d", "RIDE")
                LogPathRepository().add('output_dir', self._output_dir)
            else:
                if os.path.exists(self._output_dir + os.path.sep + 'log.html'):
                    os.remove(self._output_dir + os.path.sep + 'log.html')
        with open(LogPathRepository().find('output_dir') + os.path.sep + 'output.xml', 'w') as f:
            f.write('')
        self._write_out_put_path()
#         atexit.register(self._remove_temporary_directory)

    def _write_out_put_path(self):
        LogProject.write("OUT_PUT", self._output_dir)

    def _remove_temporary_directory(self):
        with ATEXIT_LOCK:
            if os.path.exists(self._output_dir):
                shutil.rmtree(self._output_dir)

    def add_profile(self, name, item):
        self.profiles[name] = item

    def get_profile(self, name):
        return self.profiles[name]

    def get_profile_names(self):
        return sorted(self.profiles.keys())

    def _start_listener_server(self, result_handler):

        def handle(*args):
            self._result_handler(*args)
            result_handler(*args)

        self._server = RideListenerServer(RideListenerHandler, handle)
        self._server_thread = threading.Thread(
            target=self._server.serve_forever)
        self._server_thread.setDaemon(True)
        self._server_thread.start()
        self.port = self._server.server_address[1]

    def _result_handler(self, event, *args):
        if event == 'pid':
            self._pid_to_kill = int(args[0])
        if event == 'port' and self._process:
            self._process.set_port(args[0])
        if event == 'start_test':
            longname = args[1]['longname']
            longname = self._format_longname(longname)
            self._results.set_running(longname)
        if event == 'end_test':
            longname = args[1]['longname']
            longname = self._format_longname(longname)
            if args[1]['status'] == 'PASS':
                self._results.set_passed(longname)
            else:
                self._results.set_failed(longname)

    def _format_longname(self, longname):
        return longname.upper().replace(" ", "_")

    def clear_server(self):
        self._server = None

    def shutdown_server(self):
        if self._server:
            self._server.shutdown()

    def test_execution_started(self):
        self._results.test_execution_started()

    def kill_process(self):
        if self._process:
            self._process.kill(force=True)

    def set_pause_on_failure(self, pause):
        self._pause_on_failure = pause
        self._send_pause_on_failure_information()

    def _send_pause_on_failure_information(self):
        if self._process:
            self._process.pause_on_failure(self._pause_on_failure)

    def send_stop_signal(self):
        if self._process:
            self._process.kill(killer_pid=self._pid_to_kill)

    def send_pause_signal(self):
        if self._process:
            self._process.pause()

    def send_continue_signal(self):
        if self._process:
            self._process.resume()

    def send_step_next_signal(self):
        if self._process:
            self._process.step_next()

    def send_step_over_signal(self):
        if self._process:
            self._process.step_over()

    def run_command(self, command, cwd):
        self._pid_to_kill = None
        self._process = Process(cwd)
        self._process.run_command(command)

    def get_command(self, run_args):
        if sys.platform != "linux":
            pybot_path = "{0}{1}pybot.bat".format(PybotLoader().get_root_path(), os.path.sep)
        else:
            pybot_path = "{0}{1}pybot_linux.bat".format(PybotLoader().get_root_path(), os.path.sep)
        command = [pybot_path] + run_args.get('arguments', '').split()
        argfile = os.path.join(self._output_dir, "argfile.txt")
        command.extend(["--argumentfile", argfile])
        command.extend(["--listener", self._get_listener_to_cmd()])
        command.append(run_args.get('workdir'))
        self._write_argfile(argfile, self._create_standard_args(command, run_args))
        return command

    @staticmethod
    def get_message_log_level(command):
        min_log_level_number = LEVELS['INFO']
        if '-L' in command:
            switch = '-L'
        elif '--loglevel' in command:
            switch = '--loglevel'
        else:
            return min_log_level_number
        i = command.index(switch)
        if len(command) == i:
            return
        level = command[i + 1].upper().split(':')[0]
        return LEVELS.get(level, min_log_level_number)

    def _get_listener_to_cmd(self):
        path = "{0}{1}TestRunnerAgent.py".format(PybotLoader().get_root_path(), os.path.sep)
        if path[-1] in ['c', 'o']:
            path = path[:-1]
        return '%s:%s:%s' % (path, self.port, self._pause_on_failure)

    def _create_standard_args(self, command, run_args):
        standard_args = []
        if run_args.get('run_tag', None):
            standard_args.append('--include=%s' % run_args.get('run_tag'))
        if run_args.get('skip_tag', None):
            standard_args.append('--exclude=%s' % run_args.get('skip_tag'))

        self._add_tmp_outputdir_if_not_given_by_user(command, standard_args)
        if run_args.get('testcases', None):
            for suite, test in run_args.get('testcases'):
                suite = format_normal_suite_path(suite)
                test = format_normal_test_case_path(test)
                standard_args += ['--suite', suite, '--test', test]
        return standard_args

    def _add_tmp_outputdir_if_not_given_by_user(self, command, standard_args):
        if "--outputdir" not in command and "-d" not in command:
            standard_args.extend(["--outputdir", self._output_dir])
        else:
            index = command.index("-d") if "-d" in command else command.index("--outputdir")
            outdir = command[index + 1]
            LogPathRepository().add('output_dir', outdir)

    @staticmethod
    def _add_pythonpath_if_in_settings_and_not_given_by_user(
            command, standard_args, pythonpath):
        if '--pythonpath' in command:
            return
        if '-P' in command:
            return
        if not pythonpath:
            return
        standard_args.extend(['--pythonpath', ':'.join(pythonpath)])

    @staticmethod
    def _write_argfile(argfile, args):
        f = codecs.open(argfile, "w", "utf-8")
        f.write("\n".join(args))
        f.close()

    def get_output_and_errors(self):
        return self._process.get_output(), self._process.get_errors()

    def is_running(self):
        return self._process and self._process.is_alive()

    def command_ended(self):
        self._process = None


class RideListenerServer(socketserver.TCPServer):
    allow_reuse_address = True

    def __init__(self, RequestHandlerClass, callback):
        socketserver.TCPServer.__init__(self, ("", 0), RequestHandlerClass)
        self.callback = callback


class RideListenerHandler(socketserver.StreamRequestHandler):

    def handle(self):
        decoder = StreamHandler(self.request.makefile('r'))
        while True:
            try:
                (name, args) = decoder.load()
                self.server.callback(name, *args)
            except (EOFError, IOError):
                break


if __name__ == "__main__":
    currentPath = sys.argv[0]
    print(currentPath)
    print(os.getcwd())
    print(os.path.abspath(os.path.join(os.getcwd(), "../../..")))
