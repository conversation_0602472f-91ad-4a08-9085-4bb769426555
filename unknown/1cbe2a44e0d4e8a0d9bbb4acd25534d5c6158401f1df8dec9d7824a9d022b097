#coding=utf-8
import os
import re
import traceback


class UserKeyword(object):

    def __init__(self, keywordDir=r'C:\robotWorkspace\RfHelper\script_v2\5GNR\test\userkeywords'):
        self.keywordCountDict = {}
        self._keywordDir = keywordDir
        self.keywords = {}


    def generate_all_keyword(self, keywordDir):
        if not os.path.isdir(keywordDir):
            return None
        files = os.listdir(keywordDir)
        try:
            for f in files:
                filePath = os.path.join(keywordDir, f)
                if os.path.isfile(filePath):
                    self._append_keywords(filePath)
                elif os.path.isdir(filePath):
                    self.generate_all_keyword(filePath)
        except Exception:
            traceback.print_exc()

    def _append_keywords(self, filePath):
        ext = os.path.splitext(filePath)[1]
        print(filePath)
        if not ext == '.tsv' and not ext == '.txt' and not ext == '.robot':
            return
        if ext == '.robot':
            splittext = '    '
        else:
            splittext = '\t'
        flag = True
        lines = []
        try:
            with open(filePath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except Exception as e:
            with open(filePath, 'r', encoding='GBK') as f:
                lines = f.readlines()  
        for line in lines:
            flag = self._get_flag(line, flag)
            if flag and \
               not self._is_start_with_space(line) and \
               not re.search('\*\s?Keywords\s?\*', line):
                line = line.strip().strip(r'\n')
                keyword = line.split(splittext)[0]
                self.keywords[keyword] = {}
                self.keywordCountDict.update({line.split(splittext)[0]: [0, filePath.replace('..\..\..\..\\', '')]})

    def _get_flag(self, line, flag):
        if not line:
            return False
        if re.search('\*\s?Settings\s?\*', line) or \
           re.search('\*\s?Test Cases\s?\*', line) or\
           re.search('\*\s?Variables\s?\*', line):
            return False
        if re.search('\*\s?Keywords\s?\*', line):
            return True
        return flag

    def replace_keywords(self, keywordDir=None):
        if not keywordDir:
            keywordDir = self._keywordDir
        if not os.path.isdir(keywordDir):
            return None
        try:
            files = os.listdir(keywordDir)
        except:
            return
        for f in files:
            filePath = os.path.join(keywordDir, f)
            if os.path.isfile(filePath):
                self._replace_keywords(filePath)
            elif os.path.isdir(filePath):
                self.replace_keywords(filePath)

    def _replace_keywords(self, filePath):
        ext = os.path.splitext(filePath)[1]
        if not ext == '.tsv' and not ext == '.robot':
            return
        if ext == '.robot':
            splittext = '    '
        else:
            splittext = '\t'
        lines = []
        try:
            with open(filePath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except Exception as e:
            with open(filePath, 'r', encoding='GBK') as f:
                lines = f.readlines()  
        for line in lines:
            line = line.strip('').strip(r'\n')
            kwsInLine = line.split(splittext)
            if not self._is_start_with_space(line):
                kwsInLine = kwsInLine[1:]
            for keyword in kwsInLine:
                keyword = self._fix_right_kw_name(keyword)
                if not keyword:
                    continue
                self.keywordCountDict[keyword] = [self.keywordCountDict[keyword][0] + 1, self.keywordCountDict[keyword][1]]


    def _fix_right_kw_name(self, keyword):
        if keyword in self.keywords:
            return keyword
        if keyword.split('.', 1)[-1] in self.keywords:
            return keyword.split('.', 1)[-1]

    def _is_start_with_space(self, line):
        return re.search('^\s', line)

    def _is_start_with_bracket(self, line):
        return re.search('^\\[', line)

    def _is_start_with_point(self, line):
        return re.search('^\\.', line)


if __name__ == '__main__':
#     ukr = UserKeyword(r'D:\workspace\autobmcv4\SYSTEST\RESOURCE\01基本操作')
#     ukr.generate_all_keyword(ukr._keywordDir)
#     ukr.replace_keywords(r'D:\workspace\autobmcv4\SYSTEST\接口测试\snmp')
#     ex = Excel(u'userkeywords.xlsx')
#     print(ukr.keywordCountDict)
#     ex.test(ukr.keywordCountDict, 2, 'sheet3')
#     ex.save_excel()
#     print(ukr.keywordCountDict)
#     print(len(ukr.keywordCountDict))
    
#     ukr = UserKeyword(r'D:\script_v2\5GNR\test\userkeywords')
#     ukr.generate_all_keyword(ukr._keywordDir)
#     ukr.replace_keywords(r'D:\script_v2\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell')
#     print(ukr.keywordCountDict)
#     print(len(ukr.keywordCountDict))
# 
    ukr = UserKeyword(r'D:\script_v2\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell')
    ukr.generate_all_keyword(ukr._keywordDir)
    ukr.replace_keywords(r'D:\script_v2\5GNR\test\testcase\UnifiedTestcase_ITRAN\1_规格用例库\QCell')
    print(ukr.keywordCountDict)
#     ex.test(ukr.keywordCountDict, 1, 'sheet2')
#     ex.save_excel()


