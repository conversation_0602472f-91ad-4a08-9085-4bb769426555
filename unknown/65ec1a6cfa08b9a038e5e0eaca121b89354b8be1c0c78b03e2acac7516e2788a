#!/usr/bin/env python
# encoding: utf-8
'''
@author: 10243352, 10154402, 10244931
@file: TableFactory.py
@time: 2019/10/17 18:37
@desc:
'''
import logging

from model.data_file.KeyWords import KeyWords
from model.data_file.Settings import Settings
from model.data_file.TestCases import TestCases
from model.data_file.Variables import Variables
import traceback


BLOCK_TYPE_MAP = {
    "settings": Settings,
    "test cases": TestCases,
    "variables": Variables,
    "keywords": KeyWords,
    "keyword": KeyWords,
    "test case": TestCases,
    "testcase": TestCases,
    "testcases": TestCases
}


class TableFactory(object):

    @staticmethod
    def get_instance(blockType):
        try:
            return BLOCK_TYPE_MAP.get(blockType.lower())()
        except Exception as e:
            traceback.print_exc()
            return None
