# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import re, random
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiStu<PERSON><PERSON><PERSON>
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3


class AiThreadSSP(QThread):
    rf_finished_ssp = pyqtSignal(str)
    caseInfoDic = {}

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

    def run(self):
        asa= AiStudioApi({'account': self.parent.account, "token": self.parent.token})
        testId = self.parent.TabSSP.queryEdit5.text()
        ret = asa.ssp_rf_generate(testId)
        print(ret)
        if ret:
            pid = ret['bo']['docId']
            result = ret['bo']['result']
            if 'AuthFailed' in result:
                self.rf_finished_ssp.emit('AuthFailed')
                return
            listRet = result.split('\n')
            listOutput = self.append_ai_code_id(listRet, pid)
            listOutput.append(f'\n\n    Comment    Started by AICoder, pid:{pid}')
            listOutput.append(f'    Comment    Ended by AICoder, pid:{pid}')
            self.rf_finished_ssp.emit('\n'.join(listOutput))
        else:
            self.rf_finished_ssp.emit('大模型返回异常！')

    def _is_start_with_space(self, line):
        return re.search('^\s+', line) or re.search('^\\ ', line) or re.search('^\n', line) or re.search('^\r\n', line) or re.search('^\.\.\.', line)

    def _restore_text(self, result, similarTestCaseText, similarTestCaseName, tempTestcaseName):
        result = re.sub('```[rR]?obot\n[\s\S]+$', '```Robot\n\n' + similarTestCaseText + '\n```', result)
        result = re.sub(similarTestCaseName, tempTestcaseName, result)
        return result
        
    def append_ai_code_id(self, listRet, pid):
        listOutput = []
        isPair = True
        testcaseBegin = 0
        switch = True
        for line in listRet:
            if 'by AICoder' in line or 'by AI-AutoRFTGen' in line:
                continue
            if line == '```':
                print('777')
                switch = False
            if re.search(r'\*\s?Test Cases\s?\*', line) or re.search(r'\*\s?Keywords\s?\*', line):
                if testcaseBegin != 0:
                    if not isPair:
                        listOutput = self._append_aicoder_end(listOutput, pid)
                        listOutput.append('\n\n')
                        isPair = True
                testcaseBegin = 1
                listOutput.append(line)
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 1:
                testcaseBegin = 2
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 2 and len(line) > 2:
                if not isPair:
                    listOutput = self._append_aicoder_end(listOutput, pid)
                    isPair = True
                if re.search(r'\*\s?Variables\s?\*', line) or re.search(r'\*\s?Settings\s?\*', line):
                    listOutput.append(line)
                    testcaseBegin = 0
                    continue
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            listOutput.append(line)
        if not isPair:
            listOutput = self._append_aicoder_end(listOutput, pid)
            isPair = True
        return listOutput

    def _append_aicoder_end(self, listOutput, pid):
        if 'Started by AICoder' in listOutput[-1]:
            return listOutput[0:-1]
        else:
            listOutput.append('    Comment    Ended by AICoder, pid:{0}'.format(pid))
            return listOutput