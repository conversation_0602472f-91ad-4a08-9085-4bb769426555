# coding=utf-8
'''
Created on 2019年10月25日

@author: 10240349
'''
from PyQt5.QtWidgets import QHBoxLayout
from controller.system_plugin.text_edit.TextEditor import TextEditor
from utility.PluginRepository import PluginRepository


class TextEditPlugin(object):

    def __init__(self, parent=None):
        self._parent_window = parent
        self.currentLineNumber = None
        self._text_edit = TextEditor()
        self._text_edit.setShortcutEnabled(True)

    def load(self, file_path=None):
        PluginRepository().add('TEXT_EDIT', self._text_edit)
        layout = QHBoxLayout()
        self._text_edit.load()
        layout.addLayout(self._text_edit.get_layout())
        return layout
