# -*- coding: utf-8 -*-
'''
Created on 2019年10月22日

@author: 10247557
'''
import threading
import inspect
import ctypes

from utility.log.SystemLogger import logger
from utility.ThreadRepository import ThreadRepository

class ThreadManager():

    @staticmethod
    def start(cmdStr, plugin_id):
        if ThreadRepository().find(plugin_id):
            if not ThreadRepository().find(plugin_id).is_alive():
                ThreadRepository().delete(plugin_id)
            else:
                logger.warn("plugin {} is running, reload is not support".format(plugin_id))
                return
        thread = ThreadManager.execute_by_thread(cmdStr, plugin_id)
        ThreadRepository().add(plugin_id, thread)

    @staticmethod
    def execute_by_thread(cmdStr, plugin_id):
        thread = SubThread(cmdStr)
        thread.setDaemon(True)
        thread.setName(plugin_id)
        thread.start()
        return thread

    @staticmethod
    def force_interrupt(plugin_id):
        thread = ThreadRepository().find(plugin_id)
        ThreadManager._async_raise(thread.ident, SystemExit)

    @staticmethod
    def _async_raise(tid, exctype):
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError('invalid thread id')
        elif res != 1:
            ctypes.pythonapi.PythreadState_SetAsyncExc(tid, None)
            raise SystemError("PythreadState_SetAsyncExc failed")
        return True


class SubThread(threading.Thread):

    def __init__(self, cmdStr):
        super().__init__()
        self._cmdStr = cmdStr
        self._result = ''

    def run(self):
        cmd = compile(self._cmdStr, '', 'exec')
        try:
            exec(cmd)
        except Exception as e:
            logger.error(e)

    def get_result(self):
        return self._result

#     @contextlib.contextmanager
#     def stdout_io(self, stdout=None):
#         old = sys.stdout
#         if stdout is None:
#             stdout = StringIO()
#         sys.stdout = stdout
#         print ('aaa\nbbb')
#         yield stdout
#         logger.info(stdout.getvalue())
#         sys.stdout = old