from transformers import AutoModelForCausalLM, AutoTokenizer
from fastapi import FastAPI, HTTPException, Depends, Header
from pydantic import BaseModel
import secrets, torch
from fastapi import Depends, HTTPException
from fastapi.responses import StreamingResponse
from transformers import TextStreamer  # 假设使用 Hugging Face 的流式生成工具
import torch

# 加载模型和分词器
model_path = "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"  # 或本地路径（如 "./model"）
# 检查是否有可用的 GPU
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"使用设备：{device}")
model = AutoModelForCausalLM.from_pretrained(model_path)
tokenizer = AutoTokenizer.from_pretrained(model_path)
# 将模型移到 GPU 上
model = model.to(device)

# 定义API请求格式
class QueryRequest(BaseModel):
    prompt: str
    max_length: int = 50

# 生成一个随机密钥（替换为您自己的固定密钥）
API_KEY = 'wPrp7rgP3Z4uMQaTpQHyHQ'  # 示例：生成16位随机密钥
print(f"生成的API密钥：{API_KEY}")  # 保存此密钥供插件调用

# 创建FastAPI服务
app = FastAPI()

# 密钥验证依赖
async def verify_api_key(api_key: str = Header(None, alias="X-API-Key")):
    print(api_key)
    if api_key != API_KEY:
        print(api_key)
        # raise HTTPException(status_code=401, detail="无效的API密钥")
    return api_key

""" # 定义推理接口
@app.post("/generate/chat/completions")
async def generate_text(
    request: QueryRequest,
    api_key: str = Depends(verify_api_key)
):
    print(request)
    inputs = tokenizer(request.prompt, return_tensors="pt")
    outputs = model.generate(
        inputs.input_ids,
        max_length=request.max_length
    )
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return {"response": response}
 """


@app.post("/generate/chat/completions")
async def generate_text(
    request: QueryRequest,
    api_key: str = Depends(verify_api_key),
):
    print(request)
    inputs = tokenizer(request.prompt, return_tensors="pt")
    # 创建流式生成器
    streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)
    # 定义流式生成函数
    def generate_stream():
        with torch.no_grad():
            for output in model.generate(
                inputs.input_ids,
                max_length=request.max_length,
                streamer=streamer,  # 使用流式生成器
            ):
                yield tokenizer.decode(output, skip_special_tokens=True) + "\n"

    # 返回流式响应
    return StreamingResponse(generate_stream(), media_type="text/plain")

# --------------------- 启动服务 ---------------------
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
