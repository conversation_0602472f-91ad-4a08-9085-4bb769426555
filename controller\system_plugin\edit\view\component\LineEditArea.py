# coding=utf-8
'''
Created on 2019年11月4日

@author: 10247557
'''
import logging
import traceback

from PyQt5.Qt import Qt, QApplication, QCursor
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtWidgets import QHBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtGui import QTextOption, QTextCursor

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from controller.system_plugin.edit.view.component.Formator import parse_value
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer, \
    ColorizationSettings
from model.CurrentItem import CurrentItem
from view.common.dialog.SettingsDialog import SettingsDialog
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper


class LineEditArea(object):

    def __init__(self, parent, lable_name):
        self._parent = parent
        self._lable_name = lable_name
        self._text = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._label = QLabel(self._lable_name)
        self._label.setFixedWidth(170)
        self._line = LineEdit()
        self._line.setFixedHeight(25)
        self._line.setReadOnly(True)

        # 设置文本垂直居中对齐
        self._line.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self._line.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self._set_color()
        self._line.clicked.connect(self._show_dialog)
        self._line.jump.connect(self._jump)
        self._line.textChanged.connect(self._modify_data)
        self._clear_btn = QPushButton('Clear', self._parent)
        self._clear_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._clear_btn.setFixedSize(120, 22)
        self._clear_btn.clicked.connect(self._clear_edit_area)
        self._set_layout()

        # 连接主题变化信号
        self._connect_theme_signal()

        # 应用初始主题到Clear按钮
        self._apply_clear_button_theme()

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._set_color()
        self._apply_clear_button_theme()
        # 主题切换后重新居中文本，因为不同主题的字体大小可能不同
        QTimer.singleShot(100, self._center_text_vertically)

    def _jump(self):
        self._specified_keyword_jumper = SpecifiedKeywordJumper()
        line = self._line.toPlainText()
        keyword_name = line.split('|')[0].strip()
        keyword_path = self._specified_keyword_jumper.get_keyword_path_from_local_repository(keyword_name)
        self._specified_keyword_jumper.get_keyword_item(keyword_path, keyword_name)

    def _center_text_vertically(self):
        """确保文本垂直居中显示"""
        try:
            # 临时断开textChanged信号，避免在设置边距时触发_modify_data
            try:
                self._line.textChanged.disconnect(self._modify_data)
                signal_disconnected = True
            except:
                signal_disconnected = False

            # 先重置文档边距，确保计算准确
            self._line.document().setDocumentMargin(0)

            # 强制重绘文档，使用正确的方法
            self._line.update()

            # 重新连接信号
            if signal_disconnected:
                self._line.textChanged.connect(self._modify_data)

            # 等待布局完成后再计算
            QTimer.singleShot(10, self._do_center_text)
        except Exception as e:
            print(f"文本垂直居中失败: {e}")

    def _do_center_text(self):
        """执行实际的文本居中操作"""
        try:
            # 临时断开textChanged信号，避免在设置边距时触发_modify_data
            try:
                self._line.textChanged.disconnect(self._modify_data)
                signal_disconnected = True
            except:
                signal_disconnected = False

            # 获取文档和视口高度
            doc_height = self._line.document().size().height()
            viewport_height = self._line.viewport().height()

            # 只有当视口高度大于文档高度时才进行居中
            if viewport_height > doc_height and doc_height > 0:
                # 计算需要的上边距来实现垂直居中
                margin = max(0, (viewport_height - doc_height) / 2)
                self._line.document().setDocumentMargin(margin)

            # 移动光标到文档开始位置，避免选中文本
            cursor = self._line.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.clearSelection()
            self._line.setTextCursor(cursor)

            # 重新连接信号
            if signal_disconnected:
                self._line.textChanged.connect(self._modify_data)
        except Exception as e:
            print(f"执行文本居中失败: {e}")

    def _set_color(self):
        """根据主题设置颜色"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            colors = theme_manager.get_theme_colors(current_theme)

            has_content = bool(self._line.toPlainText())

            # 获取主题相关的颜色配置
            if current_theme == 'eye_protect_green':
                # 墨绿色主题：统一使用深墨绿色背景
                bg_color = colors.get('editor_bg', '#1B2B1B')
                text_color = colors.get('editor_text', '#E8F5E8') if has_content else colors.get('editor_placeholder', '#A5D6A7')
                border_color = colors.get('border_color', '#336633')

                style = f"""
                    QTextEdit {{
                        background-color: {bg_color};
                        color: {text_color};
                        border: 1px solid {border_color};
                        border-radius: 4px;
                        padding: 4px 8px;
                        selection-background-color: #388E3C;
                        selection-color: #FFFFFF;
                    }}
                    QTextEdit:focus {{
                        border-color: #4CAF50;
                    }}
                """

            elif current_theme == 'dark':
                # 深色主题
                bg_color = colors.get('editor_bg', '#2B2B2B')
                text_color = colors.get('editor_text', '#E0E0E0') if has_content else colors.get('editor_placeholder', '#A0A0A0')
                border_color = colors.get('border_color', '#555555')

                style = f"""
                    QTextEdit {{
                        background-color: {bg_color};
                        color: {text_color};
                        border: 1px solid {border_color};
                        border-radius: 4px;
                        padding: 4px 8px;
                        selection-background-color: #4A90E2;
                        selection-color: #FFFFFF;
                    }}
                    QTextEdit:focus {{
                        border-color: #4A90E2;
                    }}
                """

            elif current_theme == 'cool_blue':
                # 淡蓝色主题
                bg_color = colors.get('editor_bg', '#E0EFFF')
                text_color = colors.get('editor_text', '#333333') if has_content else colors.get('editor_placeholder', '#666666')
                border_color = colors.get('border_color', '#A0C0FF')

                style = f"""
                    QTextEdit {{
                        background-color: {bg_color};
                        color: {text_color};
                        border: 1px solid {border_color};
                        border-radius: 4px;
                        padding: 4px 8px;
                        selection-background-color: #4A90E2;
                        selection-color: #FFFFFF;
                    }}
                    QTextEdit:focus {{
                        border-color: #4A90E2;
                    }}
                """

            else:
                # 浅色主题（默认）
                bg_color = colors.get('editor_bg', '#FFFFFF') if has_content else colors.get('editor_placeholder_bg', '#F0F0F0')
                text_color = colors.get('editor_text', '#000000') if has_content else colors.get('editor_placeholder', '#666666')
                border_color = colors.get('border_color', '#D0D0D0')

                style = f"""
                    QTextEdit {{
                        background-color: {bg_color};
                        color: {text_color};
                        border: 1px solid {border_color};
                        border-radius: 4px;
                        padding: 4px 8px;
                        selection-background-color: #4A90E2;
                        selection-color: #FFFFFF;
                    }}
                    QTextEdit:focus {{
                        border-color: #4A90E2;
                    }}
                """

            self._line.setStyleSheet(style)

        except Exception as e:
            print(f"设置LineEditArea颜色失败: {e}")
            # 降级处理：使用简单的样式
            self._line.setStyleSheet("background-color: #FFFFFF; color: #000000;")

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._label, 1)
        self._layout.addWidget(self._line, 6)
        self._layout.addWidget(self._clear_btn, 1)

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._line.setVisible(bool_value)
        self._clear_btn.setVisible(bool_value)

    def _clear_edit_area(self):
        self._line.clear()
        self._set_color()

    def fill_data(self, text, force_update=False):
        try:
            # 检查是否需要更新数据，避免覆盖用户的修改
            # 但如果是强制更新（比如从对话框调用），则跳过检查
            if not force_update and text == self._text:
                # 数据没有变化，不需要重新填充
                return

            self._text = text
            if text is None:
                text = ''

            # 临时断开textChanged信号，避免在设置文本时触发_modify_data
            self._line.textChanged.disconnect(self._modify_data)

            # 先重置文档状态，确保一致性
            self._line.document().setDocumentMargin(0)

            if isinstance(text, list):
                keyword = text[0].split(' | ')[0]
                if text[0] and text[1]:
                    text = ' | ' .join(text)
                else:
                    text = '' .join(text)
            else:
                keyword = parse_value(text)[0].split(' | ')[0]
            if Colorizer()._is_keyword(keyword):
                color = ColorizationSettings().get_keyword_color()
                color = (int(color[0]), int(color[1]), int(color[2]))
                # 使用纯文本而不是HTML，避免背景色被覆盖
                self._line.setPlainText(text)
                # 通过样式表设置关键字颜色，而不是HTML
                from controller.system_plugin.style.ThemeManager import ThemeManager
                theme_manager = ThemeManager()
                current_theme = theme_manager.get_current_theme()
                colors = theme_manager.get_theme_colors(current_theme)

                # 根据主题获取背景色
                if current_theme == 'eye_protect_green':
                    bg_color = colors.get('editor_bg', '#1B2B1B')
                elif current_theme == 'dark':
                    bg_color = colors.get('editor_bg', '#2B2B2B')
                elif current_theme == 'cool_blue':
                    bg_color = colors.get('editor_bg', '#E0EFFF')
                else:
                    bg_color = colors.get('editor_bg', '#FFFFFF')

                # 设置关键字样式，保持背景色一致
                keyword_color = f"rgb({color[0]}, {color[1]}, {color[2]})"
                border_color = colors.get('border_color', '#D0D0D0')

                style = f"""
                    QTextEdit {{
                        background-color: {bg_color};
                        color: {keyword_color};
                        border: 1px solid {border_color};
                        border-radius: 4px;
                        padding: 4px 8px;
                        selection-background-color: #4A90E2;
                        selection-color: #FFFFFF;
                    }}
                    QTextEdit:focus {{
                        border-color: #4A90E2;
                    }}
                """
                self._line.setStyleSheet(style)
            else:
                self._line.setFontUnderline(False)
                self._line.setPlainText(text)
                # 对于非关键字，使用普通的颜色设置
                self._set_color()

            # 重新连接textChanged信号
            self._line.textChanged.connect(self._modify_data)

            # 延迟执行文本垂直居中，确保文本设置完成后再居中
            QTimer.singleShot(50, self._center_text_vertically)
        except Exception:
            logging.error('============teardown or timeout or arguments or return value except=========')
            traceback.print_exc()

    def get_data(self):
        text = self._line.toPlainText().rstrip('\n')
        return parse_value(text) if text else None

    def _modify_data(self):
        result = self.get_data()
        if self._text != result:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify(self._lable_name.replace(' ', '_').lower(), result)
            # 更新内部文本状态，确保下次fill_data时不会覆盖用户的修改
            self._text = result

    def _show_dialog(self):
        self.dialog = SettingsDialog(self, self._lable_name)
        self.dialog.show()
        self.dialog.set_text(parse_value(self._line.toPlainText()))

    def _apply_clear_button_theme(self):
        """应用主题到Clear按钮"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            colors = theme_manager.get_theme_colors(current_theme)

            if current_theme == 'eye_protect_green':
                # 墨绿色主题 - 使用绿色系按钮
                button_style = """
                    QPushButton {
                        background-color: #2E7D32;
                        color: #C8E6C9;
                        border: 1px solid #336633;
                        border-radius: 4px;
                        padding: 3px;
                        font-size: 12px;
                        padding-left: 5px;
                        padding-right: 5px;
                        min-width: 40px;
                    }
                    QPushButton:hover {
                        background-color: #388E3C;
                        border-color: #4CAF50;
                        color: #FFFFFF;
                    }
                    QPushButton:pressed {
                        background-color: #1B5E20;
                        border-color: #1B5E20;
                        color: #FFFFFF;
                    }
                """
            elif current_theme == 'dark':
                # 深色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#404040')};
                        color: {colors.get('dialog_button_text', '#E0E0E0')};
                        border: 1px solid {colors.get('border_color', '#555555')};
                        border-radius: 4px;
                        padding: 3px;
                        font-size: 12px;
                        padding-left: 5px;
                        padding-right: 5px;
                        min-width: 40px;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#4A90E2')};
                        color: #FFFFFF;
                    }}
                    QPushButton:pressed {{
                        background-color: #357ABD;
                        color: #FFFFFF;
                    }}
                """
            elif current_theme == 'cool_blue':
                # 淡蓝色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#C4DAFF')};
                        color: {colors.get('dialog_button_text', '#333333')};
                        border: 1px solid {colors.get('border_color', '#A0C0FF')};
                        border-radius: 4px;
                        padding: 3px;
                        font-size: 12px;
                        padding-left: 5px;
                        padding-right: 5px;
                        min-width: 40px;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#4A90E2')};
                        color: #FFFFFF;
                    }}
                    QPushButton:pressed {{
                        background-color: #357ABD;
                        color: #FFFFFF;
                    }}
                """
            else:
                # 浅色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#F0F0F0')};
                        color: {colors.get('dialog_button_text', '#000000')};
                        border: 1px solid {colors.get('border_color', '#D0D0D0')};
                        border-radius: 4px;
                        padding: 3px;
                        font-size: 12px;
                        padding-left: 5px;
                        padding-right: 5px;
                        min-width: 40px;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#E0E0E0')};
                    }}
                    QPushButton:pressed {{
                        background-color: #D0D0D0;
                    }}
                """

            self._clear_btn.setStyleSheet(button_style)

        except Exception as e:
            print(f"应用Clear按钮主题失败: {e}")


class LineEdit(QTextEdit):
    clicked = pyqtSignal()
    jump = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        # 设置单行模式的属性
        self.setLineWrapMode(QTextEdit.NoWrap)
        self.setWordWrapMode(QTextOption.NoWrap)

    def resizeEvent(self, event):
        """窗口大小改变时重新居中文本"""
        super().resizeEvent(event)
        # 延迟执行居中，确保布局完成
        QTimer.singleShot(100, self._center_text_if_needed)

    def _center_text_if_needed(self):
        """如果需要的话，重新居中文本"""
        try:
            # 先重置边距
            self.document().setDocumentMargin(0)

            # 强制重绘，使用正确的方法
            self.update()

            # 再次延迟计算，确保布局完成
            QTimer.singleShot(10, self._do_actual_center)
        except Exception:
            pass

    def _do_actual_center(self):
        """执行实际的居中操作"""
        try:
            doc_height = self.document().size().height()
            viewport_height = self.viewport().height()

            # 只有当视口高度大于文档高度且文档有内容时才居中
            if viewport_height > doc_height and doc_height > 0:
                margin = max(0, (viewport_height - doc_height) / 2)
                self.document().setDocumentMargin(margin)
        except Exception:
            pass

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if QApplication.keyboardModifiers() == Qt.ControlModifier:
                self.jump.emit()
            else:
                self.clicked.emit()


if __name__ == "__main__":
    str = 'sdfa| fgsg '
    result = str.rsplit('| #', 1)
    print(result)
    print(result[1].replace('\\|', '|'))
