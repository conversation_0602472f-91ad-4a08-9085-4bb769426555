# -*- coding: UTF-8 -*-
import os
import re
import stat 
import shutil
import time
import subprocess


OS_PWD = os.getcwd()
SCRIPTS_STATUS = 'normal'
SCRIPTS_DIR = r'D:\workspace\autobmcv4'


class GitOp:
    def __init__(self, parent=None):
        self.parent = parent
        self.SCRIPTS_STATUS = 'normal'

    def get_cmd_execute_status(self, cmd):
        result = os.system(cmd)
        return result

    def get_cmd_execute_output(self, cmd):
#         typ = os.sys.getfilesystemencoding()
#         result = os.popen(cmd).read()
#         return result

        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        return stdout.decode('utf-8', errors='ignore')  # 指定 UTF-8 解码，并忽略解码错误

    def get_cmd_execute_output_err(self, cmd, cwd):
        SubProc = subprocess.Popen(cmd, shell=False, stderr=subprocess.PIPE, stdout=subprocess.PIPE, cwd=cwd)
        time.sleep(3)
        err = SubProc.stderr.read()
        out = SubProc.stdout.read()
        stdoutput = err + out
        return stdoutput

    def git_pull(self, path='D:\\script_v2'):
        if not os.path.exists(path):
            return 'normal'
        os.chdir(path)
        if os.path.isfile('C:/Program Files/Git/bin/bash.exe'):
            cmd = '"C:/Program Files/Git/bin/bash.exe" --login -c "git pull"'
        elif os.path.isfile('C:/Program Files (x86)/Git/bin/bash.exe'):
            cmd = '"C:/Program Files (x86)/Git/bin/bash.exe" --login -c "git pull"'
        else:
            cmd = 'bash.exe --login -c "git pull"'
        p = self.get_cmd_execute_output(cmd)
        os.chdir(OS_PWD)
        print('git pull')
        print(p)

    def git_reset(self, path=SCRIPTS_DIR):
        os.chdir(path)
        cmd = 'git reset --hard origin/master'
        p = self.get_cmd_execute_output(cmd)
        os.chdir(OS_PWD)
        print(p)
        if 'HEAD is now at' in p:
            print('git_reset success!')
            return True
        else:
            print('git_reset failed!')
            return False

    def git_clone(self, path='D:\\ATPi_CloudTest\\workspace'):
        os.chdir(path)
        if os.path.exists(path + '\\ATPI_framework'):
            self.delete_file(path + '\\ATPI_framework')
        cmd = 'git clone http://wxshcloudtest:<EMAIL>/a/gult/FDD_Test/ATPI_framework'
        p = self.get_cmd_execute_output_err(cmd, path)
        os.chdir(OS_PWD)
        if 'Checking out files: 100%' in p:
            print('git_clone success!')
            return True
        else:
            print('git_clone failed!')
            return False

    def git_fetch(self, path=SCRIPTS_DIR):
        os.chdir(path)
        cmd = 'git fetch --all'
        p = self.get_cmd_execute_output(cmd)
        os.chdir(OS_PWD)
        print(p)
        if 'Fetching origin' in p:
            print('git_reset success!')
            return True
        else:
            print('git_reset failed!')
            return False

