
import os


class BaseProfile(object):

    plugin = None
    default_settings = {}

    def __init__(self, plugin):
        '''plugin is required so that the profiles can save their settings'''
        self.plugin = plugin

    def delete_pressed(self):
        '''Handle delete key pressing'''
        pass

    def get_custom_args(self):
        '''Return a list of arguments unique to this profile.

        Returned arguments are in format accepted by Robot Framework's argument
        file.
        '''
        return []

    def get_command_prefix(self):
        '''Returns a command and any special arguments for this profile'''
        #return ["pybot.bat" if os.name == "nt" else "pybot"]
        return ["pybot.bat" if os.name == "nt" else "pybot_linux.bat"]

    def set_setting(self, name, value):
        '''Sets a plugin setting

        setting is automatically prefixed with profile's name and it can be
        accessed with direct attribute access. See also __getattr__.
        '''
        self.plugin.save_setting(self._get_setting_name(name), value, delay=2)

    def __getattr__(self, name):
        """Provides attribute access to profile's settings

        If for example default_settings = {'setting1' = ""} is defined
        then setting1 value can be used like self.setting1
        set_setting is used to store the value.
        """
        try:
            return getattr(self.plugin, self._get_setting_name(name))
        except AttributeError:
            try:
                return getattr(self.plugin, name)  # Support users old saved values
            except AttributeError:
                if name in self.default_settings:
                    return self.default_settings[name]
                raise

    def _get_setting_name(self, name):
        """Adds profile's name to the setting."""
        return "%s_%s" % (self.name.replace(' ', '_'), name)
