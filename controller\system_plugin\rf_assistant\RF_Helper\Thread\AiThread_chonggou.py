# -*- coding: utf-8 -*-
from PyQt5.QtCore import QThread, pyqtSignal
from typing import Dict, List, Optional, Tuple
import re
import traceback
import random
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiStudioApi
from controller.system_plugin.rf_assistant.RF_Helper.api.SiliconFlowAPI import SiliconFlowAPI
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3

TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'


class AiThread(QThread):
    """AI 辅助生成 Robot Framework 测试用例的线程类
    
    职责：
    - 从 AI Studio 和数据库获取相似测试用例
    - 生成 Robot Framework 测试脚本
    - 处理 AI 生成结果
    
    属性：
        rf_finished: 用例生成完成信号
        case_info_cache: 用例信息缓存
        _api_client: AI API 客户端实例
        _db_client: 数据库客户端实例
    """
    
    rf_finished = pyqtSignal(str)
    case_info_cache: Dict[str, Dict] = {}

    def __init__(self, parent=None):
        """初始化线程
        
        Args:
            parent: 父组件，用于访问 UI 元素和其他上下文
        """
        super().__init__(parent)
        self.parent = parent
        self._api_client = None
        self._db_client = None

    def get_test_setting(self, case_info: Dict) -> Tuple[str, str]:
        """从用例信息中提取测试设置和关键字定义
        
        Args:
            case_info: 包含用例信息的字典
            
        Returns:
            包含测试设置和关键字定义的元组
        """
        settings = self._extract_test_settings(case_info)
        keywords = self._extract_keyword_definitions(case_info)
        return settings, keywords

    def get_sample_test_from_dnstudio(
        self, 
        testcase_path: str, 
        testcase_name: str, 
        max_terms: int = 10
    ) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """从 AI Studio 获取相似测试用例
        
        Args:
            testcase_path: 测试用例路径
            testcase_name: 测试用例名称 
            max_terms: 最大返回用例数量
            
        Returns:
            包含相似用例ID列表、相似用例字典和第一个用例信息的元组
        """
        test_cases = self._get_similar_test_cases()
        return self._process_test_cases(test_cases, max_terms)

    def get_similar_test_from_db(
        self, 
        sample_case_ids: List[str]
    ) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """从数据库获取相似测试用例
        
        Args:
            sample_case_ids: 用例ID列表
            
        Returns:
            包含相似用例ID列表、相似用例字典和第一个用例信息的元组
        """
        db = MongoDb3()
        similar_tests = {}
        similar_ids = []
        first_case = None
        
        for case_id in sample_case_ids:
            case_info = self._get_case_info_from_db(db, case_id)
            if not case_info:
                continue
                
            if not first_case:
                first_case = case_info
                
            test_data = self._create_test_data(case_info)
            similar_tests[case_id] = test_data
            similar_ids.append(case_id)
            
        return similar_ids, similar_tests, first_case

    def run(self):
        """主运行方法，负责生成 Robot Framework 测试脚本"""
        try:
            self._generate_robot_framework_script()
        except Exception as e:
            self._handle_generation_error(e)

    def _generate_robot_framework_script(self):
        """生成 Robot Framework 测试脚本的核心逻辑"""
        self._api_client = self._init_api_client()
        testcase_name, testcase_step = self._get_user_input()
        
        if not self._validate_user_input(testcase_name, testcase_step):
            return
            
        similar_ids, similar_tests, first_case = self._get_similar_test_cases()
        prompt = self._generate_prompt(similar_ids, similar_tests, first_case)
        self._generate_test_script(self._api_client, prompt)

    def _validate_user_input(self, testcase_name: str, testcase_step: str) -> bool:
        """验证用户输入是否有效
        
        Args:
            testcase_name: 测试用例名称
            testcase_step: 测试用例步骤
            
        Returns:
            如果输入有效返回 True，否则返回 False
        """
        if not testcase_name or not testcase_step:
            self.rf_finished.emit('未查询到用例名称或者用例步骤信息！')
            return False
        return True

    def _handle_generation_error(self, error: Exception):
        """处理脚本生成过程中的错误
        
        Args:
            error: 捕获的异常对象
        """
        detailed_error = traceback.format_exc()
        print(f'Error generating test script: {detailed_error}')
        self.rf_finished.emit(f'生成测试脚本时发生错误：{str(error)}')

    def _extract_test_settings(self, case_info: Dict) -> str:
        """提取测试设置部分"""
        settings = []
        
        if case_info.get('suite_setup'):
            settings.append(f"Suite Setup    {case_info['suite_setup']}")
            
        if case_info.get('suite_teardown'):
            settings.append(f"Suite Teardown    {case_info['suite_teardown']}")
            
        if case_info.get('variables'):
            settings.extend(case_info['variables'])
            
        if case_info.get('resource'):
            settings.extend(case_info['resource'])
            
        return '\n'.join(settings) + '\n' if settings else ''

    def _extract_keyword_definitions(self, case_info: Dict) -> str:
        """提取关键字定义部分"""
        keywords = []
        keyword_list = self._get_keyword_list(case_info)
        
        for key in keyword_list:
            if case_info.get('suite_keywords_detail', {}).get(key):
                details = '\n'.join(case_info['suite_keywords_detail'][key])
                keywords.append(f"{key}\n{details}\n")
                
        return '\n'.join(keywords) if keywords else ''

    def _get_keyword_list(self, case_info: Dict) -> List[str]:
        """获取关键字列表"""
        keywords = []
        
        if case_info.get('suite_setup'):
            keywords.append(case_info['suite_setup'].split(' ')[0])
            
        if case_info.get('suite_teardown'):
            keywords.append(case_info['suite_teardown'].split(' ')[0])
            
        return keywords

    def _get_similar_test_cases(self, testcase_path: str, testcase_name: str, max_terms: int = 10) -> List[str]:
        """从 AI Studio 获取相似测试用例列表
        
        Args:
            testcase_path: 测试用例路径
            testcase_name: 测试用例名称
            max_terms: 最大返回用例数量
            
        Returns:
            相似测试用例列表
        """
        asa = AiStudioApi({
            'account': self.parent.account, 
            "token": self.parent.token
        })
        query = f'__{self.parent.project}__{testcase_path}/{testcase_name}'
        ret = asa.get_similar_test(query)
        return ret['bo']['result'].split('\n\n ')

    def _process_test_cases(
        self, 
        test_cases: List[str], 
        max_terms: int
    ) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """处理并过滤测试用例"""
        db = MongoDb3()
        similar_tests = {}
        similar_ids = []
        first_case = {}
        random_num = random.randint(1, 4)
        
        for i, test in enumerate(test_cases[:max_terms], 1):
            case_info = self._extract_case_info(test, db)
            if not case_info:
                continue
                
            if i == random_num:
                first_case = case_info
                
            if len(case_info.get('case_content', [])) < 4:
                continue
                
            case_id = case_info['id']
            test_data = self._create_test_data(case_info)
            similar_tests[case_id] = test_data
            similar_ids.append(case_id)
            
        return similar_ids, similar_tests, first_case

    def _extract_case_info(self, test: str, db: MongoDb3) -> Optional[Dict]:
        """从测试用例字符串中提取用例信息"""
        test_info = test.split('__')
        if len(test_info) <= 4:
            return None
            
        case_id = test_info[-1]
        case_info = db.query({'id': case_id}, TEST_CASE_INFO_DB_TABLE)
        return case_info[0] if case_info else None

    def _create_test_data(self, case_info: Dict) -> Dict:
        """创建测试用例数据字典"""
        return {
            'id0': case_info.get('id0'),
            'id': case_info.get('id'),
            'script': '\n'.join(case_info.get('case_content', [])),
            'step': self.parent.get_rdc_test_info(case_info.get('id0'))[1],
            'name': self.parent.get_rdc_test_info(case_info.get('id0'))[0]
        }

    def _get_case_info_from_db(self, db: MongoDb3, case_id: str) -> Optional[Dict]:
        """从数据库查询用例信息"""
        case_info = db.query({'id': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
        if not case_info:
            case_info = db.query({'id0': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
        return case_info[0] if case_info else None

    def _init_api_client(self) -> SiliconFlowAPI:
        """初始化 API 客户端"""
        return SiliconFlowAPI(
            base_url="https://api.siliconflow.cn/v1",
            api_key="sk-ihnegbftkynylxenlbbrxksdladnraavqdycbzfkzyddroyr",
            model="deepseek-ai/DeepSeek-V3"
        )

    def _get_user_input(self) -> Tuple[str, str]:
        """获取用户输入的测试用例信息"""
        return (
            self.parent.testcaseNameEdit3.text(),
            self.parent.testcaseStepEdit3.toPlainText()
        )

    def _get_similar_test_cases(self) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """获取相似测试用例
        
        Returns:
            包含相似用例ID列表、相似用例字典和第一个用例信息的元组
        """
        max_terms = self._get_max_terms_based_on_mode()
        if self._has_custom_query():
            return self._get_custom_query_results(max_terms)
        return self._get_default_test_cases(max_terms)

    def _get_max_terms_based_on_mode(self) -> int:
        """根据开发模式获取最大返回用例数量
        
        Returns:
            最大返回用例数量
        """
        return 1 if self.parent.devMode == '新手模式' else 10

    def _has_custom_query(self) -> bool:
        """检查是否有自定义查询
        
        Returns:
            如果有自定义查询返回 True，否则返回 False
        """
        return bool(self.parent.queryEdit31.text())

    def _get_custom_query_results(self, max_terms: int) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """处理自定义查询结果
        
        Args:
            max_terms: 最大返回用例数量
            
        Returns:
            包含相似用例ID列表、相似用例字典和第一个用例信息的元组
        """
        sample_ids = self.parent.queryEdit31.text().split(',')
        similar_ids, similar_tests, first_case = self.get_similar_test_from_db(sample_ids)
        
        if not similar_ids:
            return self._get_default_test_cases(max_terms)
        return similar_ids, similar_tests, first_case

    def _get_default_test_cases(self, max_terms: int) -> Tuple[List[str], Dict[str, Dict], Optional[Dict]]:
        """获取默认测试用例
        
        Args:
            max_terms: 最大返回用例数量
            
        Returns:
            包含相似用例ID列表、相似用例字典和第一个用例信息的元组
        """
        return self.get_sample_test_from_dnstudio(
            self.parent.testcasePath,
            self.parent.testcaseNameEdit3.text(),
            max_terms
        )

    def _generate_prompt(
        self, 
        similar_ids: List[str], 
        similar_tests: Dict[str, Dict], 
        first_case: Optional[Dict]
    ) -> str:
        """生成 AI 提示词
        
        Args:
            similar_ids: 相似用例ID列表
            similar_tests: 相似用例字典
            first_case: 第一个用例信息
            
        Returns:
            生成的提示词字符串
        """
        if self.parent.devMode == '新手模式' and similar_tests:
            return self._generate_beginner_prompt(similar_tests, similar_ids[0])
        return self._generate_expert_prompt(similar_ids, similar_tests, first_case)

    def _generate_beginner_prompt(self, similar_tests: Dict[str, Dict], case_id: str) -> str:
        """生成新手模式提示词
        
        Args:
            similar_tests: 相似用例字典
            case_id: 用例ID
            
        Returns:
            新手模式提示词字符串
        """
        return self._build_beginner_prompt(
            self._get_case_info(case_id),
            self._get_script_testcase_name()
        )

    def _build_beginner_prompt(self, case_info: Dict, script_name: str) -> str:
        """构建新手模式提示词模板
        
        Args:
            case_info: 用例信息字典
            script_name: 脚本名称
            
        Returns:
            格式化后的提示词字符串
        """
        return f'''
__XY__指令:
## 角色扮演：
你是一名Robot Framework开发新手。
## 任务描述：
- 请完整无遗漏地在代码块里输出我提供的Robot Framework（RF）代码.
- 请将用例标题替换成：{script_name}。
## 强调：
1.请在Robot Framework代码块里输出代码.
## 代码：
以下是我提供的<Robot Framework代码>：
```Robot
{self._get_test_case_text(case_info)}
```
'''

    def _generate_test_script(self, sfa: SiliconFlowAPI, prompt: str):
        """调用 AI 生成测试脚本
        
        Args:
            sfa: SiliconFlowAPI 实例
            prompt: 提示词字符串
        """
        try:
            result = sfa.generate(prompt)
            self._handle_generation_result(result)
        except Exception as e:
            self._handle_generation_error(e)

    def _handle_generation_result(self, result: Dict):
        """处理 AI 生成结果
        
        Args:
            result: AI 生成结果字典
        """
        if result and 'choices' in result and result['choices']:
            self.rf_finished.emit(result['choices'][0]['message']['content'])
        else:
            self.rf_finished.emit('AI 生成测试脚本失败，请重试！')

    def _generate_expert_prompt(
        self, 
        similar_ids: List[str], 
        similar_tests: Dict[str, Dict], 
        first_case: Optional[Dict]
    ) -> str:
        """生成专家模式提示词
        
        Args:
            similar_ids: 相似用例ID列表
            similar_tests: 相似用例字典
            first_case: 第一个用例信息
            
        Returns:
            专家模式提示词字符串
        """
        if not similar_tests:
            return self._build_minimal_expert_prompt()
            
        return self._build_full_expert_prompt(
            similar_ids,
            similar_tests,
            first_case,
            self.get_test_setting(first_case),
            self._get_script_testcase_name()
        )

    def _build_minimal_expert_prompt(self) -> str:
        """构建最小专家模式提示词
        
        Returns:
            最小专家模式提示词字符串
        """
        return f'''
【背景和指令】：你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{self._get_script_testcase_name()}'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
【强调一】：尽量使用示例脚本里已经存在的关键字，你可以新增关键字，但是要给出其定义和描述。
【用例步骤】：\n{self.parent.testcaseStepEdit3.toPlainText()}
'''

    def _build_full_expert_prompt(
        self,
        similar_ids: List[str],
        similar_tests: Dict[str, Dict],
        first_case: Dict,
        settings: str,
        keyword_text: str,
        script_name: str
    ) -> str:
        """构建完整专家模式提示词
        
        Args:
            similar_ids: 相似用例ID列表
            similar_tests: 相似用例字典
            first_case: 第一个用例信息
            settings: 设置部分文本
            keyword_text: 关键字文本
            script_name: 脚本名称
            
        Returns:
            完整专家模式提示词字符串
        """
        prompt = f'''
【背景和指令】：你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{script_name}'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
【强调一】：尽量使用示例脚本里已经存在的关键字，你可以新增关键字，但是要给出其定义和描述。
【强调二 】：以下内容作为脚本的*** settings ***，不要改变内容和格式：
{settings}
【强调三】： 以下内容加入到*** Keywords ***里，这是已经定义好的关键字，不要改变内容和格式：
{keyword_text}

【用例步骤】：\n{self.parent.testcaseStepEdit3.toPlainText()}
'''

        return self._append_example_cases(prompt, similar_ids, similar_tests)

    def _append_example_cases(
        self,
        prompt: str,
        similar_ids: List[str],
        similar_tests: Dict[str, Dict]
    ) -> str:
        """追加示例用例到提示词
        
        Args:
            prompt: 当前提示词
            similar_ids: 相似用例ID列表
            similar_tests: 相似用例字典
            
        Returns:
            追加示例后的提示词字符串
        """
        for i, case_id in enumerate(similar_ids):
            if i >= 20:
                break
                
            temp = f'''      
【示例用例步骤】：\n{similar_tests[case_id]['step']}
【示例脚本输出】：\n{similar_tests[case_id]['script']}
'''
            if len(prompt) + len(temp) < 31000:
                prompt += temp
            else:
                break
                
        return prompt

    def _is_start_with_space(self, line: str) -> bool:
        return bool(re.search(r'^\s+', line))

    def _restore_text(
        self, 
        result: str, 
        similar_test_case_text: str, 
        similar_test_case_name: str, 
        temp_testcase_name: str
    ) -> str:
        result = re.sub(
            r'```[rR]?obot\n[\s\S]+$', 
            f'```Robot\n\n{similar_test_case_text}\n```', 
            result
        )
        return result.replace(similar_test_case_name, temp_testcase_name)

    def append_ai_code_id(self, list_ret: List[str], pid: str) -> List[str]:
        """在生成的代码中添加 AI 标识"""
        output = []
        is_pair = True
        testcase_begin = 0
        switch = True
        
        for line in list_ret:
            if 'by AICoder' in line or 'by AI-AutoRFTGen' in line:
                continue
                
            if line == '```':
                switch = False
                
            if re.search(r'\*\s?Test Cases\s?\*', line) or re.search(r'\*\s?Keywords\s?\*', line):
                if testcase_begin != 0 and not is_pair:
                    output = self._append_aicoder_end(output, pid)
                    output.append('\n\n')
                    is_pair = True
                    
                testcase_begin = 1
                output.append(line)
                continue
                
            if not self._is_start_with_space(line):
                if testcase_begin == 1:
                    testcase_begin = 2
                elif testcase_begin == 2 and len(line) > 2:
                    if not is_pair:
                        output = self._append_aicoder_end(output, pid)
                        is_pair = True
                        
                    if re.search(r'\*\s?Variables\s?\*', line) or re.search(r'\*\s?Settings\s?\*', line):
                        output.append(line)
                        testcase_begin = 0
                        continue
                        
                output.append(line)
                if is_pair and switch:
                    output.append(f'    Comment    Started by AICoder, pid:{pid}')
                    is_pair = False
                continue
                
            output.append(line)
            
        if not is_pair:
            output = self._append_aicoder_end(output, pid)
            
        return output

    def _append_aicoder_end(self, output: List[str], pid: str) -> List[str]:
        """添加 AI 结束标识"""
        if 'Started by AICoder' in output[-1]:
            return output[:-1]
        output.append(f'    Comment    Ended by AICoder, pid:{pid}')
        return output
