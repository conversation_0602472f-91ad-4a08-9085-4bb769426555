from PIL import Image

# 打开 JPG 图像
img = Image.open("./images/ai.jpg").convert("RGBA")

# 获取图片的宽度和高度
width, height = img.size

# 遍历图片的每个像素点
for y in range(height):
    for x in range(width):
        r, g, b, a = img.getpixel((x, y))

        # 判断是否为白色背景（或接近白色）
        if r > 200 and g > 200 and b > 200:
            img.putpixel((x, y), (255, 255, 255, 0))  # 将白色像素设为透明

img.save("./images/ai.ico", format="ICO")
