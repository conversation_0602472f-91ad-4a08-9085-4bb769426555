# coding=utf-8
"""
文件编码处理工具类
用于统一处理文件编码检测和转换问题
"""

import os
import chardet
from utility.log.SystemLogger import logger


class EncodingHandler:
    """文件编码处理工具类"""
    
    # 常见编码列表，按优先级排序
    COMMON_ENCODINGS = [
        'utf-8',
        'gbk',
        'gb2312',
        'gb18030',
        'big5',
        'latin-1',
        'cp1252',
        'ascii'
    ]
    
    @staticmethod
    def detect_encoding(file_path, sample_size=8192):
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            sample_size: 采样大小，默认8KB
            
        Returns:
            str: 检测到的编码名称，如果检测失败返回'utf-8'
        """
        if not os.path.exists(file_path):
            logger.warn(f"文件不存在: {file_path}")
            return 'utf-8'
            
        if os.path.getsize(file_path) == 0:
            logger.info(f"文件为空: {file_path}")
            return 'utf-8'
        
        try:
            with open(file_path, 'rb') as f:
                # 读取文件样本进行编码检测
                sample = f.read(sample_size)
                
            if not sample:
                return 'utf-8'
                
            # 使用chardet检测编码
            detection_result = chardet.detect(sample)
            detected_encoding = detection_result.get('encoding')
            confidence = detection_result.get('confidence', 0)
            
            logger.debug(f"文件 {file_path} 编码检测结果: {detected_encoding}, 置信度: {confidence}")
            
            # 如果检测结果可靠且不是ascii，使用检测结果
            if detected_encoding and confidence > 0.7 and detected_encoding.lower() != 'ascii':
                # 处理编码别名
                return EncodingHandler._normalize_encoding(detected_encoding)
            else:
                # 检测结果不可靠，尝试常见编码
                return EncodingHandler._try_common_encodings(sample)
                
        except Exception as e:
            logger.warn(f"编码检测失败: {file_path}, 错误: {e}")
            return 'utf-8'
    
    @staticmethod
    def _normalize_encoding(encoding):
        """标准化编码名称"""
        encoding = encoding.lower()
        
        # 处理常见的编码别名
        encoding_map = {
            'gb2312': 'gbk',
            'utf-8-sig': 'utf-8',
            'windows-1252': 'cp1252',
        }
        
        return encoding_map.get(encoding, encoding)
    
    @staticmethod
    def _try_common_encodings(sample):
        """尝试常见编码解码样本"""
        for encoding in EncodingHandler.COMMON_ENCODINGS:
            try:
                sample.decode(encoding)
                logger.debug(f"成功使用编码 {encoding} 解码")
                return encoding
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，返回utf-8
        logger.warn("所有常见编码都无法解码，使用utf-8")
        return 'utf-8'
    
    @staticmethod
    def read_file_with_encoding(file_path, encoding=None, errors='ignore'):
        """
        使用指定编码读取文件
        
        Args:
            file_path: 文件路径
            encoding: 指定编码，如果为None则自动检测
            errors: 错误处理方式，默认'ignore'
            
        Returns:
            str: 文件内容
        """
        if not encoding:
            encoding = EncodingHandler.detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding, errors=errors) as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败: {file_path}, 编码: {encoding}, 错误: {e}")
            # 尝试使用utf-8和ignore错误模式
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
            except Exception as e2:
                logger.error(f"使用UTF-8读取文件也失败: {file_path}, 错误: {e2}")
                return ""
    
    @staticmethod
    def read_file_lines_with_encoding(file_path, encoding=None, errors='ignore'):
        """
        按行读取文件，自动处理编码
        
        Args:
            file_path: 文件路径
            encoding: 指定编码，如果为None则自动检测
            errors: 错误处理方式，默认'ignore'
            
        Yields:
            str: 文件的每一行
        """
        if not encoding:
            encoding = EncodingHandler.detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding, errors=errors) as f:
                for line_no, line in enumerate(f, 1):
                    yield line
        except Exception as e:
            logger.error(f"按行读取文件失败: {file_path}, 编码: {encoding}, 错误: {e}")
            # 尝试使用utf-8和ignore错误模式
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line_no, line in enumerate(f, 1):
                        yield line
            except Exception as e2:
                logger.error(f"使用UTF-8按行读取文件也失败: {file_path}, 错误: {e2}")
                return
    
    @staticmethod
    def convert_file_encoding(source_path, target_path=None, target_encoding='utf-8'):
        """
        转换文件编码
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径，如果为None则覆盖源文件
            target_encoding: 目标编码，默认utf-8
            
        Returns:
            bool: 转换是否成功
        """
        if not target_path:
            target_path = source_path
        
        try:
            # 读取源文件内容
            content = EncodingHandler.read_file_with_encoding(source_path)
            
            # 写入目标文件
            with open(target_path, 'w', encoding=target_encoding, errors='ignore') as f:
                f.write(content)
            
            logger.info(f"文件编码转换成功: {source_path} -> {target_encoding}")
            return True
            
        except Exception as e:
            logger.error(f"文件编码转换失败: {source_path}, 错误: {e}")
            return False
