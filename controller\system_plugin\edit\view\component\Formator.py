'''
Created on 2019年11月19日

@author: 10259183
'''
from utility.DataHandler import split_value, replace_value
from robot import utils
from robot.variables import isvar
from settings.SystemSettings import SystemSettings
ROBOT_VERSION = SystemSettings().read('ROBOT_VERSION')
import importlib
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing.robotreader')
RawRobotReader = getattr(robot_module, 'RobotReader')


def parse_value(value):
    if value is None:
        return value
    if value.startswith('#'):
        return ['', value.replace('\\|', '|')]
    value = value.rsplit(' | #', 1)
    value = [v for v in value]
    if len(value) == 1:
        return value + ['']
    return [value[0], '#' + value[1].replace('\\|', '|')]


class Comment(object):

    def __init__(self, comment_data):
        if isinstance(comment_data, str):
            comment_data = [comment_data] if comment_data else []
        self._comment = comment_data or []
    
    def get_comment(self):
        self._set_comment()
        return self.input_comment

    def __len__(self):
        return len(self._comment)

    def _set_comment(self):
        self.input_comment = ''
        if self._comment:
            self.input_comment = self._comment[0]
            if self._not_commented():
                self.input_comment = '# ' + self.input_comment     

    def as_list(self):
        if self._not_commented():
            self._comment[0] = '# ' + self._comment[0]
        return self._comment

    def _not_commented(self):
        return self._comment and self._comment[0] and self._comment[0][0] != '#'

 
class Fixture(object):

    def __init__(self, value, comment):
        self.value = value
        self.comment = Comment(comment)
   
    def display_value(self):
        value = self._as_list()
        if value[0]:
            return ' | ' .join(value)
        else:
            return '' .join(value)

    def _as_list(self):
        self._parse_value()
        value = [self.value[0]] + self.value[1] + self.comment.as_list()
        return replace_value(value)
             
    def _parse_value(self):
        value = split_value(self.value)
        self.value = (value[0], value[1:]) if value else ('', [])

 
class Timeout(object):

    def __init__(self, value, comment):
        self.value = value
        self.comment = Comment(comment)

    def display_value(self):
        value = self._as_list()
        if value[0]:
            return ' | ' .join(value)
        else:
            return '' .join(value)

    def _as_list(self):
        value = [self._timestr] + self.comment.as_list()
        return replace_value(value)
     
    def validate(self):
        time_tokens = split_value(self.value)
        self._timestr = ''
        if not time_tokens:
            return None
        timestr = time_tokens[0]
        try:
            secs = utils.timestr_to_secs(timestr)
            if secs <= 0:
                raise ValueError("Timestring must be over zero")
            time_tokens[0] = utils.secs_to_timestr(secs)
            self._timestr = time_tokens[0]
        except ValueError as err:
            if '${' not in timestr:
                return str(err)
        return None


class ArgumentTypes(object):
    SCALAR, DEFAULT, LIST, DICT = range(1, 5)


class Arguments(object):

    def __init__(self, value):
        self.value = value

    def validate(self):
        try:
            types = [self._get_type(arg)
                     for arg in split_value(self.value)]
        except ValueError as e:
            return "Invalid argument syntax '%s'" % str(e)  # DEBUG  was arg
        return self._validate_argument_order(types)

    def _get_type(self, arg):
        if isvar.is_scalar_var(arg):
            return ArgumentTypes.SCALAR
        elif isvar.is_scalar_var(arg.split("=")[0]):
            return ArgumentTypes.DEFAULT
        elif isvar.is_list_var(arg):
            return ArgumentTypes.LIST
        elif isvar.is_dict_var(arg):
            return ArgumentTypes.DICT
        else:
            raise ValueError(arg)  # py3

    def _validate_argument_order(self, types):
        prev = ArgumentTypes.SCALAR
        for t in types:
            if t < prev:
                return ("List and scalar arguments must be before named and "
                        "dictionary arguments")
            prev = t
        return None
