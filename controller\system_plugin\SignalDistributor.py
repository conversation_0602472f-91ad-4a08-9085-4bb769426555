# coding=utf-8
'''
Created on 2019年11月26日

@author: 10240349
'''
from PyQt5.Qt import QObject, pyqtSignal

from model.CurrentItem import CurrentItem
from utility.PluginRepository import PluginRepository
from utility.Singleton import Singleton
from utility.UIRepository import UIRepository


@Singleton
class SignalDistributor(QObject):

    text_editor_modify_item = pyqtSignal(object)
    editor_modify_item = pyqtSignal(object, dict)
    show_item = pyqtSignal(object, bool)
    show_only_in_edit = pyqtSignal(object, bool)
    highlight_keyword = pyqtSignal(str)
    text_editor_save_item = pyqtSignal(object, dict)
    text_editor_update_item = pyqtSignal(object, dict)
    update_item = pyqtSignal(object, object)
    star_modify = pyqtSignal(object)
    modify_table = pyqtSignal()
    format_save_item = pyqtSignal()
    global_save_content = pyqtSignal()
    refresh_text_edit = pyqtSignal(object)
    modify_table_item = pyqtSignal(str)
    all_item_save = pyqtSignal()
    bash_load = pyqtSignal()
    rf_assistant_load = pyqtSignal()
    rf_assistant_unload = pyqtSignal()
    close_event = pyqtSignal()
    locate_varible = pyqtSignal(int)

    def highlight_selected_keyword(self, keyword):
        self.highlight_keyword.emit(keyword)

    def text_editor_modify(self, view_item):
        self.text_editor_modify_item.emit(view_item,)

    def editor_modify(self, view_item, data_dict):
        self.editor_modify_item.emit(view_item, data_dict)

    def modify_table_event(self):
        self.modify_table.emit()

    def modify_table_item_event(self, text):
        self.modify_table_item.emit(text)

    def start_bash_load(self):
        self.bash_load.emit()

    def show(self, view_item, is_add=False):
        try:
            text_edit = PluginRepository().find('TEXT_EDIT')

            # 安全获取当前路径
            try:
                current_path = CurrentItem().get().get("path")
            except Exception as e:
                print(f"获取当前路径时出错: {e}")
                current_path = None

            if CurrentItem().name and current_path:
                text_edit.set_last_file_path(current_path)

            # 检查view_item是否有效
            if view_item is None:
                print("警告：view_item为None，跳过设置")
                return

            # 检查view_item是否还有有效的父节点（对于子项）
            if hasattr(view_item, 'parent') and callable(view_item.parent):
                item_type = type(view_item).__name__
                if item_type in ['TestcaseItem', 'UserKeywordItem', 'VariableItem']:
                    if view_item.parent() is None:
                        print(f"警告：{item_type} 的父节点为None，可能已失效")
                        # 可以选择不设置，或者尝试其他处理方式
                        return

            CurrentItem().set(view_item)

            try:
                current_path = CurrentItem().get().get("path")
                print('show---CurrentItem file path: ', current_path)
            except Exception as e:
                print(f"设置后获取路径时出错: {e}")

            if not UIRepository().find('project_tree_items'):
                UIRepository().update('project_tree_items', [CurrentItem().get_current_item()])
            elif UIRepository().find('project_tree_items')[-1] != CurrentItem().get_current_item():
                UIRepository().update('project_tree_items', UIRepository().find('project_tree_items') +
                                      [CurrentItem().get_current_item()])
            self.show_item.emit(view_item, is_add)

        except Exception as e:
            print(f"SignalDistributor.show方法出错: {e}")
            # 即使出错也要尝试发射信号，保证程序继续运行
            try:
                self.show_item.emit(view_item, is_add)
            except Exception as emit_error:
                print(f"发射信号时出错: {emit_error}")

    def text_editor_save(self, item, modified_values):
        self.text_editor_save_item.emit(item, modified_values)

    def text_editor_update(self, item, modified_values):
        self.text_editor_update_item.emit(item, modified_values)

    def update(self, item, parent):
        self.update_item.emit(item, parent)

    def del_star_modify(self, item):
        self.star_modify.emit(item)

    def format_save(self):
        self.format_save_item.emit()

    def global_save(self):
        self.global_save_content.emit()

    def refresh_text_edit_content(self, item):
        self.refresh_text_edit.emit(item)

    def save_all(self):
        self.all_item_save.emit()

    def emit_close_event(self):
        self.close_event.emit()

    def emit_varible_locate(self, index):
        self.locate_varible.emit(index)
