'''
Created on 2019年12月10日

@author: 10154402,10240349
'''
from utility.Singleton import Singleton
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit
from settings.i18n.Loader import LanguageLoader
from settings.SystemSettings import SystemSettings
from PyQt5.Qt import Qt
from model.data_file.Repository import BuildInKeyWordRepository, \
    LocalKeyWordRepository


@Singleton
class KeywordDescription(object):

    '''
    classdocs
    '''

    def __init__(self):
        '''
        Constructor
        '''
        self._dialog = None

    def close_dialog(self):
        if self._dialog:
            self._dialog.close()
            self._dialog = None

    def open_dialog(self, current_item_name, x, y):
        self.close_dialog()
        self._dialog = QDialog()
        self._dialog.setLayout(self._set_ui(current_item_name))
        self._set_dialog_style(x, y)
        self._dialog.show()

    def _set_ui(self, current_item_name):
        v_layout = QVBoxLayout()
        keyword_des = self._generate_keyword_des(current_item_name)
        v_layout.addWidget(keyword_des)
        return v_layout

    def _set_dialog_style(self, x, y):
        self._dialog.setWindowTitle(LanguageLoader().get('KEYWORD_DES'))
        self._dialog.setWindowFlags(Qt.Tool | Qt.WindowCloseButtonHint)
        self._dialog.setStyleSheet("background:white")
        self._dialog.setAttribute(Qt.WA_ShowWithoutActivating)
        self._dialog.setGeometry(x, y,
                                 SystemSettings().get_value('COMPLETER_DIALOG_WIDTH'),
                                 SystemSettings().get_value('COMPLETER_DIALOG_HEIGHT'))

    def _generate_keyword_des(self, name):
        text_des = QTextEdit()
        self._set_text_style(text_des)
        des = self._set_des_from_repo(name)
        text_des.setText(self._assemble_content(des, name))
        return text_des

    def get_keyword_arguments(self, name):
        des = self._set_des_from_repo(name)
        return des[name][0]['arguments']

    def _set_text_style(self, text_des):
        text_des.setLineWrapMode(QTextEdit.NoWrap)
        text_des.setStyleSheet("background:transparent;border-width:0;border-style:outset")
        text_des.setFontFamily(SystemSettings().get_value('FONT'))

    def _set_des_from_repo(self, name):
        des = BuildInKeyWordRepository().query(name)
        if not des:
            des = LocalKeyWordRepository().query(name)
        return des

    def _assemble_content(self, des, name):
        text = ''
        for d in des[name]:
            text += '【Source】: ' + d['path'] + '\n\n' + \
                    '【Arguments】: ' + str(d['arguments']) + '\n\n' + '【功能说明】: ' + d['documentation']
            break
        return text
