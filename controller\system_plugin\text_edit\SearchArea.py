# coding=utf-8
'''
Created on 2019年12月16日

@author: 10247557
'''
from _functools import partial

from PyQt5.Qt import Qt, QCursor
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import <PERSON><PERSON>oxLayout, QHBoxLayout, QLineEdit, QPushButton, \
    QAction, QWidget, QLabel, QRadioButton


WIDTH = 300


class SearchArea():

    def __init__(self, text_editor):
        self._text_editor = text_editor
        self._search_text = ''
        self._set_shut_cut()
        self._is_search_forward = True
        self._search_line = 0
        self._isSecondSearch = False
        self._firstFindCursor = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._layout = QVBoxLayout()
        self._layout.addLayout(self._set_search_area())
        self._set_visible()

    def _set_search_area(self):
        layout = QHBoxLayout()
        self._line_editor = QLineEdit()
        self._line_editor.setMaximumWidth(WIDTH)
        self._search_btn = QPushButton('Search')
        self._search_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._search_btn.clicked.connect(partial(self.search_text, self))
        layout.addWidget(self._line_editor)
        layout.addWidget(self._search_btn, 0, Qt.AlignLeft)
        return layout

    def _set_visible(self):
        self._line_editor.setVisible(False)
        self._search_btn.setVisible(False)

    def _get_search_text(self):
        search_text = self._line_editor.text()
        return search_text

    @staticmethod
    def search_text(self):
        search_text = self._get_search_text()
        self._execute_search_text(search_text)

    def _search_text_by_dialog(self, search_text):
        self._execute_search_text(search_text)
        if self._text_editor.hasSelectedText():
            self._dialog.set_replace_enable(True)
        else:
            self._dialog.set_replace_enable(False)

    def _execute_search_text(self, search_text):
        if self._search_text.lower() != search_text.lower():
            self._search_line = 0
            if not self._is_search_forward:
                self._search_line = self._text_editor.lines() - 1
        self._search_text = search_text
        if self._is_search_forward:
            is_finded = self._text_editor.findFirst(search_text, True, False, True, True)
        else:
            is_finded = self._search_backward(search_text)
        self._set_search_tips(is_finded)
        
    def _search_forward(self, search_text):
        is_finded = False
        for line in range(self._search_line, self._text_editor.lines()):
            index = self._text_editor.text(line).lower().find(search_text.lower())
            if index != -1:
                self._text_editor.setSelection(line, len(search_text) + index, line, index)
                is_finded = True
                self._search_line = line + 1
                break
            if line == self._text_editor.lines() - 1:
                self._search_line = 0
        return is_finded

    def _search_backward(self, search_text):
        is_finded = False
        for line in range(self._search_line, -1, -1):
            index = self._text_editor.text(line).lower().find(search_text.lower())
            if index != -1:
                self._text_editor.setSelection(line, len(search_text) + index, line, index)
                is_finded = True
                self._search_line = line - 1
                break
            if line == 0:
                self._search_line = self._text_editor.lines() - 1
        return is_finded

    def _set_shut_cut(self):
        self._dialogs_action = QAction('Open Dialogs')
        self._dialogs_action.setShortcut('Ctrl+F')
        self._dialogs_action.triggered.connect(self._open_dialogs)
        self._text_editor.addAction(self._dialogs_action)

    def _replace_and_find_text(self, replace_find_data):
        search_text, replace_text = replace_find_data
        self._replace_text(replace_text)
        self._execute_search_text(search_text)

    def _replace_text(self, replace_text):
        if self._text_editor.hasSelectedText():
            self._text_editor.replaceSelectedText(replace_text)

    def _replace_all_text(self, replace_all_data):
        self._isSecondSearch , self._firstFindCursor = False, None
        search_text, replace_text = replace_all_data
        self._dialog.clear_search_tips()
        while True:
            is_finded = self._text_editor.findFirst(search_text, True, False, True, True)
            if is_finded:
                if self._is_second_search(replace_text):
                    break
                self._replace_text(replace_text)
            else:
                break
        if not is_finded:
            self._dialog.show_search_tips()

    def _is_second_search(self, replace_text):
        if self._firstFindCursor is None:
            self._firstFindCursor = self._text_editor.getCursorPosition()
        else:
            firstFindLine, firstFindColmn = self._firstFindCursor
            currentLine, currentColmn = self._text_editor.getCursorPosition()
            if currentLine == firstFindLine and currentColmn <= firstFindColmn + len(replace_text):
                self._isSecondSearch = True
            if self._isSecondSearch and currentColmn >= firstFindColmn:
                return True
        return False

    def _set_direction(self, flag):
        self._is_search_forward = flag

    def _set_search_tips(self, is_finded):
        if is_finded:
            self._dialog.clear_search_tips()
        else:
            self._dialog.show_search_tips()

    def _open_dialogs(self):
        self._dialog = Dialog('Find/Replace')
        self._dialog.find_pressed.connect(self._search_text_by_dialog)
        self._dialog.replace_find_pressed.connect(self._replace_and_find_text)
        self._dialog.replace_pressed.connect(self._replace_text)
        self._dialog.replace_all_pressed.connect(self._replace_all_text)
        self._dialog.search_direction.connect(self._set_direction)
        self._dialog.show()


class Dialog(QWidget):

    find_pressed = pyqtSignal(str)
    replace_find_pressed = pyqtSignal(list)
    replace_pressed = pyqtSignal(str)
    replace_all_pressed = pyqtSignal(list)
    search_direction = pyqtSignal(bool)

    def __init__(self, title):
        super().__init__()
        self._set_ui(title)
        self.load()
        self._search_text = None

    def _set_ui(self, title):
        self.setWindowTitle(title)
        self.setWindowModality(Qt.ApplicationModal)
        self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.resize(300, 300)

    def load(self):
        self._find_area = self._set_find_area()
        self._replace_area = self._set_replace_area()
        self._direction_area = self._set_direction_area()
        self._blank_area = QHBoxLayout()
        self._find_btn_area = self._set_find_btn_area()
        self._replace_btn_area = self._set_replace_btn_area()
        self._close_btn = self._set_close_area()
        self._add_layout()

    def _add_layout(self):
        layout = QVBoxLayout()
        layout.addLayout(self._find_area)
        layout.addLayout(self._replace_area, 1)
        layout.addLayout(self._direction_area)
        layout.addLayout(self._blank_area, 1)
        layout.addLayout(self._find_btn_area)
        layout.addLayout(self._replace_btn_area)
        layout.addLayout(self._close_btn)
        self.setLayout(layout)

    def _set_find_area(self):
        layout = QHBoxLayout()
        self._find_label = QLabel('Find:        ')
        self._find_line = QLineEdit()
        self._find_line.setFixedHeight(25)
        self._find_line.textChanged.connect(self._set_enable)
        layout.addWidget(self._find_label, 0, Qt.AlignTop)
        layout.addWidget(self._find_line, 0, Qt.AlignTop)
        return layout

    def _set_replace_area(self):
        layout = QHBoxLayout()
        self._replace_label = QLabel('Replace with:')
        self._replace_line = QLineEdit()
        self._replace_line.setFixedHeight(25)
        layout.addWidget(self._replace_label, 0, Qt.AlignTop)
        layout.addWidget(self._replace_line, 0, Qt.AlignTop)
        return layout

    def _set_direction_area(self):
        layout = QVBoxLayout()
        direction = QLabel('Direction')
        self._forward_rb = QRadioButton('Forward', self)
        self._backward_rb = QRadioButton('Backward', self)
        self._forward_rb.setChecked(True)
        self._forward_rb.toggled.connect(self._set_direction)
        layout.addWidget(direction, 0, Qt.AlignLeft)
        layout.addWidget(self._forward_rb, 0, Qt.AlignLeft)
        layout.addWidget(self._backward_rb, 0, Qt.AlignLeft)
        return layout

    def _set_find_btn_area(self):
        self._find_btn = QPushButton('Find', self)
        self._find_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._replace_and_find_btn = QPushButton('Replace/Find')
        self._replace_and_find_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._find_btn.setFixedSize(80, 25)
        self._find_btn.setEnabled(False)
        self._find_btn.clicked.connect(self._send_find_text)
        self._replace_and_find_btn.setFixedSize(80, 25)
        self._replace_and_find_btn.setEnabled(False)
        self._replace_and_find_btn.clicked.connect(self._send_replace_and_find_data)
        hbox = QHBoxLayout()
        hbox.addWidget(self._find_btn, 0, Qt.AlignRight)
        hbox.addWidget(self._replace_and_find_btn)
        return hbox

    def _set_replace_btn_area(self):
        self._replace_btn = QPushButton('Replace', self)
        self._replace_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._replace_all_btn = QPushButton('Replace All')
        self._replace_all_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._replace_btn.setFixedSize(80, 25)
        self._replace_btn.setEnabled(False)
        self._replace_btn.clicked.connect(self._send_replace_data)
        self._replace_all_btn.setFixedSize(80, 25)
        self._replace_all_btn.setEnabled(False)
        self._replace_all_btn.clicked.connect(self._send_replace_all_data)
        hbox = QHBoxLayout()
        hbox.addWidget(self._replace_btn, 0, Qt.AlignRight)
        hbox.addWidget(self._replace_all_btn)
        return hbox

    def _set_close_area(self):
        hbox = QHBoxLayout()
        close_btn = QPushButton('Close')
        close_btn.setCursor(QCursor(Qt.PointingHandCursor))
        close_btn.setFixedSize(80, 25)
        close_btn.clicked.connect(self._close_dialog)
        self._search_tips = QLabel()
        hbox.addWidget(self._search_tips, 0, Qt.AlignLeft)
        hbox.addWidget(close_btn, 0, Qt.AlignRight)
        return hbox

    def clear_search_tips(self):
        self._search_tips.setText('')

    def show_search_tips(self):
        self._search_tips.setText('String Not Found')

    def _set_enable(self):
        if not self._find_line.text():
            self._find_btn.setEnabled(False)
            self._replace_and_find_btn.setEnabled(False)
            self._replace_btn.setEnabled(False)
            self._replace_all_btn.setEnabled(False)
        else:
            self._find_btn.setEnabled(True)
            self._replace_all_btn.setEnabled(True)

    def set_replace_enable(self, flag):
        self._replace_and_find_btn.setEnabled(flag)
        self._replace_btn.setEnabled(flag)

    def _set_direction(self):
        if self._forward_rb.isChecked():
            self.search_direction.emit(True)
        else:
            self.search_direction.emit(False)

    def _send_find_text(self):
        text = self._find_line.text()
        self.find_pressed.emit(text)

    def _send_replace_and_find_data(self):
        find_text = self._find_line.text()
        replace_text = self._replace_line.text()
        self.replace_find_pressed.emit([find_text, replace_text])

    def _send_replace_data(self):
        replace_text = self._replace_line.text()
        self.replace_pressed.emit(replace_text)

    def _send_replace_all_data(self):
        find_text = self._find_line.text()
        replace_text = self._replace_line.text()
        self.replace_all_pressed.emit([find_text, replace_text])

    def _close_dialog(self):
        self.close()
