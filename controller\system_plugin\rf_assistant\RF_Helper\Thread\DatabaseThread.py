from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3

TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'

class DatabaseThread(QThread):
    data_loaded = pyqtSignal(str, dict, dict)
    caseInfoDic = {}
    caseInfoDic0 = {}

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

    def run(self):
        self.db = MongoDb3()
        self.caseInfoDic = {}
        self.caseInfoDic0 = {}
        case_info = self.db.query({'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
        for case in case_info:
            self.caseInfoDic[case['id']] = case
        for case in case_info:
            if case.get('id0'):
                self.caseInfoDic0[case['id0']] = case
        self.data_loaded.emit('', self.caseInfoDic, self.caseInfoDic0)