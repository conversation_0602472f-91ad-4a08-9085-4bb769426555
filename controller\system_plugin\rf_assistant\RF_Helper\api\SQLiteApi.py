import sqlite3

class AQLiteApi:
    def __init__(self, db_name):
        self.db_name = db_name
        self.connection = None

    def connect(self):
        """Establish a connection to the SQLite database."""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_name)
        return self.connection

    def close(self):
        """Close the connection to the SQLite database."""
        if self.connection:
            self.connection.close()
            self.connection = None

    def execute_query(self, query, params=None):
        """Execute a query against the SQLite database."""
        if params is None:
            params = []
        cursor = self.connect().cursor()
        try:
            cursor.execute(query, params)
            self.connection.commit()
            return cursor.fetchall()
        except sqlite3.Error as e:
            print(f"An error occurred: {e}")
            return None
        finally:
            cursor.close()

    def create_table(self, table_name, columns):
        """Create a new table in the SQLite database."""
        columns_with_types = ", ".join([f"{col} {col_type}" for col, col_type in columns.items()])
        query = f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_with_types})"
        self.execute_query(query)

    def insert(self, table_name, data):
        """Insert a new record into a table."""
        placeholders = ", ".join(["?" for _ in data])
        columns = ", ".join(data.keys())
        values = list(data.values())
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        self.execute_query(query, values)

    def update(self, table_name, data, condition):
        """Update records in a table."""
        set_clause = ", ".join([f"{key} = ?" for key in data.keys()])
        values = list(data.values())
        query = f"UPDATE {table_name} SET {set_clause} WHERE {condition}"
        self.execute_query(query, values)

    def delete(self, table_name, condition):
        """Delete records from a table."""
        query = f"DELETE FROM {table_name} WHERE {condition}"
        self.execute_query(query)

def main():
    # Initialize the API with a database name
    db_api = AQLiteApi('test.db')
    db_api.connect()

    # Create a table
    db_api.create_table('users', {
        'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
        'name': 'TEXT NOT NULL',
        'age': 'INTEGER',
        'email': 'TEXT'
    })

    # Insert a record
    db_api.insert('users', {'name': 'Alice', 'age': 30, 'email': '<EMAIL>'})

    # Query the table
    users = db_api.execute_query('SELECT * FROM users')
    print('Users:', users)

    # Update a record
    db_api.update('users', {'age': 31}, 'name = "Alice"')

    # Query the table again
    users = db_api.execute_query('SELECT * FROM users')
    print('Updated Users:', users)

    # Delete a record
    db_api.delete('users', 'name = "Alice"')

    # Query the table again
    users = db_api.execute_query('SELECT * FROM users')
    print('After Deletion:', users)

    # Close the connection
    db_api.close()

if __name__ == '__main__':
    main()