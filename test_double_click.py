#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试双击事件的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.Qsci import QsciScintilla

class SimpleTextEditor(QsciScintilla):
    def __init__(self):
        super().__init__()
        self.setUtf8(True)
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        print(f"mousePressEvent: button={event.button()}")
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """处理鼠标双击事件"""
        print(f"双击事件被触发: button={event.button()}, pos={event.pos()}")

        if event.button() == Qt.LeftButton:
            # 获取当前行文本
            line, _ = self.getCursorPosition()
            line_text = self.text(line)
            print(f"双击行号: {line}, 行内容: '{line_text}'")

            # 模拟expand_by_path的行为
            if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                item_name = line_text.strip()
                if item_name:
                    print(f"模拟展开路径: {item_name}")

        super().mouseDoubleClickEvent(event)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("双击事件测试")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 添加说明标签
        info_label = QLabel("请双击编辑器中的任意行来测试双击事件。查看控制台输出。")
        layout.addWidget(info_label)

        # 创建简单的TextEditor
        self.text_editor = SimpleTextEditor()
        layout.addWidget(self.text_editor)

        # 设置一些测试文本
        test_content = """Test Case 1
    Log    This is a test step
    Should Be Equal    ${var}    expected

Test Case 2
    Log    Another test step
    Should Contain    ${text}    substring

*** Keywords ***
My Custom Keyword
    Log    This is a custom keyword
    Return From Keyword    success
"""
        self.text_editor.setText(test_content)

        print("测试窗口已创建，请尝试双击文本编辑器中的行")

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("应用程序已启动")
    print("请双击编辑器中的任意行来测试双击事件")
    print("查看控制台输出以确认事件是否被触发")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
