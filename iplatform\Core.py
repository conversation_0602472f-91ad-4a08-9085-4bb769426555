# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     core
   Description :
   Author :       10140129
   date：          2019/10/16
-------------------------------------------------
   Change Activity:
                   2019/10/16:
-------------------------------------------------
"""
import os
import sys
import json5

from model.Plugin import Plugin
from iplatform.PluginDrawer import PluginDrawer
from iplatform.PluginRegistry import PluginRegistry
from settings.SystemSettings import SYSTEM_ROOT_DIR
from settings.UserSettings import UserSettings
from utility.Singleton import Singleton
from iplatform.Activator import Activator

PLUGINS=[]


@Singleton
class Core(object):

    def __init__(self):
        self._plugins = UserSettings().get_plugins()
        self._init_plugins()
        self._scan_plugins()
        if sys.platform != "linux":
            self._load_plugins()

    def _init_plugins(self):
        for plugin in self._plugins:
            if self.sign_up(plugin):
                PluginDrawer.add_widgets(PluginRegistry().find(plugin))

    def sign_up(self, plugin_package):
        plugin = self._get_plugin_obj(plugin_package)
        if plugin:
            return PluginRegistry().add(plugin_package, plugin)
        else:
            return False

    def _get_plugin_obj(self, packageName):
        plugin_path = '{0}/../plugins/{1}'.format(os.path.abspath(os.path.dirname(__file__)), packageName)
        package = "{}/packages.json".format(plugin_path)
        if not os.path.exists(package):
            print("plugin not exists")
            return None
        with open(package, 'rb') as f:
            plugin_dict = json5.loads(f.read())
        plugin_dict.update({"path": plugin_path})
        return Plugin(plugin_dict)

    def _scan_plugins(self):
        dirs = []
        for root, d, files in os.walk('{}/plugins/'.format(SYSTEM_ROOT_DIR)):
            dirs = d
            break
        for _dir in dirs:
            if not _dir.startswith("_"):
                PLUGINS.append(_dir)

    def get_plugins(self):
        return PLUGINS

    def _load_plugins(self):
        for plugin_id in self._plugins:
            plugin_obj = PluginRegistry().find(plugin_id)
            if plugin_obj and plugin_obj.activationEvents == "init":
                Activator.activate(plugin_id)

    def load(self, name):
        if self.sign_up(name):
            if PluginRegistry().find(name).activationEvents == "init":
                Activator.activate(name)


if __name__ == "__main__":
    print(Core().get_plugins())
