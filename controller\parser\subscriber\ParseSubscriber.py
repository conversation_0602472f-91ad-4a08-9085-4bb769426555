'''
Created on 2019年11月27日

@author: 10243352
'''
from PyQt5.Qt import QObject

from controller.parser.subscriber.LocalRepoUpdater import LocalRepoUpdater
from controller.system_plugin.SignalDistributor import SignalDistributor


class ParseSubscriber(QObject):

    def __init__(self):
        self._signal_distributor = SignalDistributor()
#         self._signal_distributor.show_item.connect(self.show_editor)

#     @staticmethod
#     def show_editor(view_tree_item):
#         print("222222222222222222")
#         updater = LocalRepoUpdater(view_tree_item)
#         updater.update()
#         SignalDistributor().show(item)
