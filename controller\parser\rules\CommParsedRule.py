'''
Created on 2019年10月24日

@author: 10243352
'''
from robot.libraries.Collections import NOT_SET
PY_2_RF_VALUE = {
    False: "${False}",
    True: "${True}",
    None: "${None}",
    "": "${EMPTY}",
    " ": "${SPACE}",
    NOT_SET: "${EMPTY}"
}


def is_cross_row(tag):
    return tag == "..."


def is_cross_for(tag):
    return tag == "" or tag == "\\"


def is_comment(tag):
    pass


def is_blank_line(cells):
    flag = True
    for item in cells:
        flag = flag and (not bool(item))
    return flag


if __name__ == '__main__':
    print(is_blank_line(["", ""]))
