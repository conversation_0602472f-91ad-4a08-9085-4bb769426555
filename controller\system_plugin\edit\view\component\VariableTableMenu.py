# coding=utf-8
'''
Created on 2019年12月2日

@author: 10247557
'''
from PyQt5.QtWidgets import QAction, QTableWidgetItem, QApplication
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.view.component.TableMenu import TableMenu
from model.CurrentItem import CurrentItem
from utility.UIRepository import UIRepository


class VariableTableMenu(TableMenu):

    def _delete_rows(self):
        if self._table.selectedIndexes():
            row = self._table.selectedIndexes()[0].row()
            self._table.removeRow(row)
            self._current_row = row
            self._modify_data({'value': None,'operator': 'delete'})

    def _move_up(self):
        row = self._table.selectedIndexes()[0].row()
        if row == 0:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row, cols)
        for c in range(cols):
            self._table.setItem(row, c, QTableWidgetItem(row_data[c]))
            self._table.setItem(row - 1, c, QTableWidgetItem(prev_row_data[c]))
        self._current_row = row - 1
        self._modify_data({'value': prev_row_data,'operator': 'move'})

    def _move_down(self):
        row = self._table.selectedIndexes()[0].row()
        if row == self._table.rowCount() - 1:
            return
        cols = self._table.columnCount()
        row_data, prev_row_data = self._get_two_row_data(row + 1, cols)
        for c in range(cols):
            self._table.setItem(row, c, QTableWidgetItem(prev_row_data[c]))
            self._table.setItem(row + 1, c, QTableWidgetItem(row_data[c]))
        self._current_row = row + 1
        self._modify_data({'value': row_data,'operator': 'move'})

    def _modify_data(self, oprator_dict):
        result = {'type': 'variables','index': self._current_row}
        result.update(oprator_dict)
        if self._current_row is not None:
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), result)
