# coding=utf-8
'''
Created on 2020年3月4日

@author: 10247557
'''
import threading
import time

import requests

from controller.userinfo.UserInfo import UserInfo
from utility.jobschedule.JobScheduler import JobScheduler
from utility.log import SystemLogger


class UserInfoManager():

    def __init__(self):
        self._userInfo = UserInfo()

    def start(self):
        thread = threading.Thread(target=self.send_user_info)
        thread.setDaemon(True)
        thread.start()

    def send_user_info(self):
        self.post()
        s = JobScheduler()
        s.add_cron_job("send_user_info", self.post, args=[], hour='10', minute='0', second='0')
        s.start()
        while True:
            time.sleep(10)

    def post(self):
        """发送用户信息到服务器，改进错误处理和超时设置"""
        url = 'http://zxmte.zte.com.cn/userinfo'
        user_info = self._get_user_info()
        try:
            # 添加超时设置和更详细的错误处理
            response = requests.post(url, user_info, timeout=10)
            if response.status_code == 200:
                SystemLogger.info('用户信息发送成功')
            else:
                SystemLogger.warn(f'服务器响应异常: {response.status_code}')
        except requests.exceptions.Timeout:
            SystemLogger.warn('连接服务器超时')
        except requests.exceptions.ConnectionError:
            SystemLogger.warn('无法连接到服务器，可能是网络问题')
        except requests.exceptions.RequestException as e:
            SystemLogger.warn(f'发送用户信息失败: {e}')
        except Exception as e:
            SystemLogger.error(f'发送用户信息时出现未知错误: {e}')

    def _get_user_info(self):
        data = {}
        try:
            date = time.strftime("%Y-%m-%d", time.localtime())
            name = self._userInfo.get_pc_name()
            ip = self._userInfo.get_pc_ip()
            mac_address = self._userInfo.get_mac_address()
            sys_type = self._userInfo.get_operator_system_type()
            data.update({'date': date, 'pcName': name, 'ip': ip,
                         'macAddress': mac_address, 'systemType': sys_type})
        except Exception as e:
            SystemLogger.error(e)
        return data

if __name__ == "__main__":
    import time
    userInfoManager = UserInfoManager()
    userInfoManager.start()
    time.sleep(10)
