# coding=utf-8
'''
Created on 2019年11月4日

@author: 10247557
'''
from PyQt5.Qt import Qt, QCursor
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import QHBoxLayout, QLabel, QTextEdit, QPushButton, \
    QInputDialog, QLineEdit

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from settings.DialogHelps import get_help
from utility.ShowStatusSwitcher import switch_show_status
from view.common.dialog.Dialog import Dialog
from view.common.dialog.DocumentationDialog import DocumentationDialog


class Documentation(object):

    def __init__(self, parent):
        self._parent = parent

    def get_layout(self):
        return self._layout

    def load(self):
        self._set_label()
        self._edit_area = TextEdit()
        self._edit_area.setFixedHeight(80)
        self._edit_area.clicked.connect(self._set_edit_area_text)
        self._edit_area.textChanged.connect(self._modify_data)
#         self._set_edit_btn()
        self._clear_btn = QPushButton('Clear', self._parent)
        self._clear_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._clear_btn.setFixedSize(120, 22)
        self._clear_btn.clicked.connect(self._clear_edit_area)
        self._set_layout()

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._label, 1)
        self._layout.addWidget(self._edit_area, 6)
        self._layout.addWidget(self._clear_btn, 1, Qt.AlignTop)

    def _set_edit_btn(self):
        self._edit_btn = QPushButton('Edit', self._parent)
        self._edit_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._edit_btn.clicked.connect(self._set_edit_area_text)

    def _set_edit_area_text(self):
        self.dialog = DocumentationDialog('Documentation')
        self.dialog.fill_data(self.get_data())
        self.dialog.ok_pressed.connect(self._set_text)
        self.dialog.show()

    def set_visible(self, bool_value):
        self._label.setVisible(bool_value)
        self._edit_area.setVisible(bool_value)
        self._clear_btn.setVisible(bool_value)

    def _clear_edit_area(self):
        self._edit_area.clear()

    def get_edit_area(self):
        return self._edit_area

    def _set_label(self):
        self._label = QLabel('Documentation')
        self._label.setFixedWidth(170)
        self._label.setAlignment(Qt.AlignTop)

    def fill_data(self, text):
        @switch_show_status(self)
        def _fill_data(self, text):
            self._text = text
            self._edit_area.setPlainText(text)
        return _fill_data(self, text)

    def _set_text(self, text):
        self._edit_area.setPlainText(text)

    def get_data(self):
        text = self._edit_area.toPlainText().rstrip('\n')
        return text if text else None

    def _modify_data(self):
        result = self.get_data()
        if not self._is_show and result != self._text:
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'documentation': result})
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify('documentation', result)


class TextEdit(QTextEdit):
    clicked = pyqtSignal()
    def mouseReleaseEvent(self, QMouseEvent):
        if QMouseEvent.button() == Qt.LeftButton:
            self.clicked.emit()
