import requests
import json

# 服务器地址
SERVER_URL = "http://127.0.0.1:8002/v1/chat/completions/"

# 要发送的数据
data = {
    'prompt': '你好，请问你是谁？',
    'max_length': 50000,
    'deep_thinking': False
}

try:
    # 发送 POST 请求，设置 stream=True 以支持流式响应
    with requests.post(SERVER_URL, json=data, stream=True) as response:
        # 检查响应状态码
        if response.status_code == 200:
            print("请求成功！开始接收流式响应：")
            # 逐块处理响应内容
            for chunk in response.iter_content(chunk_size=None):
                if chunk:
                    # 解码并打印响应块
                    chunk_str = chunk.decode('utf-8')
                    print(chunk_str, end='', flush=True)
        else:
            print(f"请求失败，状态码：{response.status_code}")
            print(f"响应内容：{response.text}")

except requests.exceptions.RequestException as e:
    print(f"请求过程中发生错误：{e}")