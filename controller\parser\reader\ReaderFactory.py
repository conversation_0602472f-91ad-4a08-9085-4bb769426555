#!/usr/bin/env python
# encoding: utf-8
'''
@author: 10243352, 10154402, 10244931
@file: ReaderFactory.py
@time: 2019/10/17 14:39
@desc:
'''
import os

from controller.parser.reader.RobotReader import RobotReader
from controller.parser.reader.TxtReader import TxtReader
from controller.parser.reader.TsvReader import TsvReader
from controller.parser.reader.HtmlReader import HtmlReader

SUFFIX_READER_MAP = {
    "txt": TxtReader,
    "tsv": TsvReader,
    #     "html": HtmlReader,
    "robot": RobotReader
}


class ReaderFactory(object):

    @staticmethod
    def get_instance(path):
        suffix = os.path.splitext(path)[-1][1:]
        return SUFFIX_READER_MAP.get(suffix)(path)
