# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     CurrentItem
   Description :
   Author :       10140129
   date：          2019/11/9
-------------------------------------------------
   Change Activity:
                   2019/11/9:
-------------------------------------------------
"""
from utility.Singleton import Singleton
# from view.explorer.tree_item.PyItem import PyItem
# from view.explorer.tree_item.TestcaseItem import TestcaseItem
# from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
# from view.explorer.tree_item.VariableItem import VariableItem


@Singleton
class CurrentItem(object):

    def __init__(self):
        self._set_default()

    def set(self, current_item):

        self._set_default()
        if current_item:
            try:
                self._current_item = current_item
                self._name = current_item.text(0)
                self._set_path()
                self._set_suite()
                self._type = type(current_item).__name__
            except Exception as e:
                print(f"设置CurrentItem时出错: {e}")
                # 设置基本信息，避免完全失败
                self._current_item = current_item
                self._name = getattr(current_item, '_name', str(current_item))
                self._type = type(current_item).__name__
                self._path = ""
                self._index = 0

    def set_by_text(self, parent, text):
        """
        从parent里查找所有child项，找到text(0)跟输入的text相同的item，设置为当前项

        Args:
            parent: 父级树项
            text: 要查找的文本内容

        Returns:
            bool: 是否找到并设置成功
        """
        if not parent or not text:
            return False
        try:
            # 遍历父项的所有子项
            for i in range(parent.childCount()):
                child = parent.child(i)
                if child and child.text(0) == text:
                    # 找到匹配的子项，设置为当前项
                    self.set(child)

                    # 同时更新项目树的选中状态
                    from utility.ProjectTreeRepository import ProjectTreeRepository
                    tree = ProjectTreeRepository().find("PROJECT_TREE")
                    if tree:
                        tree.setCurrentItem(child)
                        child.setSelected(True)
                    print(f"已通过文本'{text}'设置当前项: {child.text(0)}")
                    return True

            print(f"未找到文本为'{text}'的子项")
            return False

        except Exception as e:
            print(f"set_by_text执行时出错: {e}")
            return False
        
    @property
    def name(self):
        if hasattr(self, "_name"):
            return self._name
        return None

    def _set_default(self):
        self._current_item = None
        self._name = None
        self._path = None
        self._suite = None
        self._type = 'Blank'
        self._index = None

    def _set_path(self):
        self._index = None
        if type(self._current_item).__name__ == 'TestcaseItem' or type(self._current_item).__name__ == 'UserKeywordItem' or \
                type(self._current_item).__name__ == 'VariableItem':
            print('self._current_item: ',self._current_item)
            print('self._current_item.parent(): ',self._current_item.parent())

            # 安全检查：确保parent()不为None
            parent = self._current_item.parent()
            if parent is None:
                print(f"警告：{type(self._current_item).__name__} 的父节点为None，可能是由于用例树重新加载导致的")
                # 尝试通过路径重新查找item
                if hasattr(self._current_item, '_path') and self._current_item._path:
                    self._path = self._current_item._path
                    self._index = 0  # 默认索引
                else:
                    # 如果无法获取路径，使用默认值
                    self._path = ""
                    self._index = 0
                return

            try:
                path = (parent.get_path(), parent.get_child_index(self._current_item))
                self._path = path[0]
                self._index = path[1]
            except Exception as e:
                print(f"获取路径时出错: {e}")
                # 使用备用方案
                if hasattr(self._current_item, '_path') and self._current_item._path:
                    self._path = self._current_item._path
                else:
                    self._path = ""
                self._index = 0
        else:
            try:
                self._path = self._current_item.get_path()
            except Exception as e:
                print(f"获取路径时出错: {e}")
                self._path = ""

    def _set_suite(self):
        if type(self._current_item).__name__ == 'TestcaseItem' or type(self._current_item).__name__ == 'UserKeywordItem' or \
                type(self._current_item).__name__ == 'VariableItem':
            if type(self._current_item.parent()).__name__ == 'SuiteItem':
                self._suite = self._current_item.parent()
        else:
            self._suite = None

    def get_parent(self):
        if hasattr(self, '_current_item'):
            return type(self._current_item.parent()).__name__
        else:
            return None

    def get(self):
        result = {"name": self._name, "path": self._path, "type": self._type, "suite": self._suite}
        if self._index is None:
            return result
        result.update({"index": self._index})
        return result

    def get_current_item(self):
        return self._current_item

if __name__ == "__main__":
    # CurrentItem().set("a","/media/b","suit")
    # print(CurrentItem().get())
    # CurrentItem().set("a", ("/media/b",1), "suit")
    print(type(None).__name__)
