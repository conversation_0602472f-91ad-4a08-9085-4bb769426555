# -*- coding: utf-8 -*-
import requests
import json
import hashlib
import socket


class RdcApi(object):
    
    def __init__(self, para={}):
        self.user = para.get('account', '')
        self.password = para.get('password', '')
        self.token = para.get('token', '')
        self.workspace = para.get('workspace', 'RAN')
        self.auth = ''
        self.tokenStatus = 0
        self.hosetIp = ''
        if not self.token:
            print(self.token_ok())
        else:
            self.tokenStatus = 1
        self.headers = {"X-Tenant-Id": "ZTE",
           "X-Emp-No": self.user,
           "X-Auth-Value":self.token,
           "appcode": "5a04672c46944750bd4c5ce5db39bd6f",
           #"appcode": "57dc3350da4242749126321df13513d1",
           "Content-Type": "application/json",
           "X-Workspace-Id": self.workspace
           }
		   
  
    def get_request(self, url, headers=None):
        response = requests.get(url, headers=headers)
        print(response)
        if response.status_code == 200:
            return response.json()
        else:
            response.raise_for_status()
    
    def post_request(self, url, headers, data):
        response = requests.post(url, data=json.dumps(data), headers=headers)
        print(response.status_code)
        return response.json()

    def get_item_path(self, caseids):
        try:
            if self.token_ok():
                url = 'https://icosg.dt.zte.com.cn/rdc/testcenter-testlib/api/v1/testlib/branch/testcase/belong-directories/batch'
                if not isinstance(caseids, list):
                    caseids = [caseids]
                body = {"testcaseSystemIds": caseids}
                respondence = self.post_request(url, self.headers, body)
                return respondence
        except Exception as e:
            print(str(e))
            self.tokenStatus = 0
            return {}

    def get_item_info(self, systemId):
        try:
            if self.token_ok():
                url = "https://icosg.dt.zte.com.cn/ZXRDCloud/RDCloud/WIC/workspaces/{1}/work_items/{0}?workItemTypeKey=TestCase".format(systemId, self.workspace)
                respondence = self.get_request(url, self.headers)
                return respondence
        except Exception as e:
            print(str(e))
            self.tokenStatus = 0
            return {}

    def get_item_info_with_testcase_id(self, caseid):
        try:
            if self.token_ok():
                url = "https://icosg.dt.zte.com.cn/ZXRDCloud/RDCloud/WIC/rest/workspaces/{0}/queries/query_work_items".format(self.workspace)
                body = {"wiql": "select * from TestCase where TestCenterCaseId = '{0}'".format(caseid)}
                respondence = self.post_request(url, self.headers, body)
                if respondence:
                    if len(respondence['bo']['result']['items']) > 0:
                        return respondence['bo']['result']['items'][0]
                    else:
                        return {}
                else:
                    return {}
        except Exception as e:
            print(str(e))
            self.tokenStatus = 0
            return {}

    def get_item_info_with_system_id(self, System_Id):
        print('----get_item_info_with_system_id----')
        try:
            if self.token_ok():
                url = "https://icosg.dt.zte.com.cn/ZXRDCloud/RDCloud/WIC/rest/workspaces/{0}/queries/query_work_items".format(self.workspace)
                body = {"wiql": "select * from TestCase where System_Id = '{0}'".format(System_Id)}
                respondence = self.post_request(url, self.headers, body)
                print(respondence)
                if respondence:
                    if len(respondence['bo']['result']['items']) > 0:
                        return respondence['bo']['result']['items'][0]
                    else:
                        return {}
                else:
                    return {}
        except Exception as e:
            print(str(e))
            self.tokenStatus = 0
            return {}

    def token_ok(self):
        if self.tokenStatus == 0 and self.password:
            ret = self.get_token(self.user, self.password)
            if ret['code']['code'] == '0000' and ret['bo']['code'] == '0000':
                self.token = ret['other']['token']
                print(self.token)
                self.auth = requests.auth.HTTPDigestAuth(self.user, ret['other']['token'])
                self.tokenStatus = 1
                return True
            else:
                return False
        else:
            return True

    def get_host_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            self.hosetIp = s.getsockname()[0]
            return self.hosetIp
        finally:
            s.close()

    def get_token(self, user, passwd):
        url = "http://uac.zte.com.cn/uaccommauth/auth/comm/verify.serv"
        clientip = self.get_host_ip()
        text =\
            {
                "account": user,
                "passWord": passwd,
                "loginClientIp": clientip,
                "loginSystemCode": 'Portal',
                "originSystemCode": '',
                "other": {
                    "networkArea": '1',
                    "networkAccessType": '1'
                },
                "verifyCode": hashlib.md5(str(user + passwd + clientip + 'Portal').encode(encoding='utf-8')).hexdigest()
            }
        headers = {'Content-type': 'application/json'}
        content = requests.post(url, data=json.dumps(text), headers=headers)
        return content.json()



if __name__ == "__main__":
    Rdc = RdcApi({'account': "********", 'password': 'Flirt-999', 'workspace': 'RAN', 'token': ''})
    result = Rdc.get_item_info('RAN-4052975')
    print(result)
    # result = Rdc.get_item_info_with_system_id('RAN-6137574')
    # print(result)
    # result = Rdc.get_item_path('RAN-6137574')
    # print(result)
