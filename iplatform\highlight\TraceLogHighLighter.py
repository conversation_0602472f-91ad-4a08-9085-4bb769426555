# -*- coding: utf-8 -*-
"""
-------------------------------------------------
   File Name：     TraceLogHighlighter
   Description :
   Author :       10140129
   date：          2019/10/24
-------------------------------------------------
   Change Activity:
                   2019/10/24:
-------------------------------------------------
"""
from PyQt5.QtCore import QRegExp,Qt
from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QColor, QFont, QCursor
from PyQt5.QtWidgets import QApplication


class TraceLogHighlighter(QSyntaxHighlighter):

    _rules = []
    Formats = {}

    def __init__(self, parent=None):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(parent)
        self._initialize_formats()
        FAIL = ['FAIL']
        ERROR = ['ERROR']
        CONSTANTS = ["False", "True", "None", "NotImplemented",
                     "Ellipsis"]
        TraceLogHighlighter._rules.append((QRegExp(r"\bFAIL\b"), "fail"))
        TraceLogHighlighter._rules.append((QRegExp(r"\bERROR\b"), "error"))
        TraceLogHighlighter._rules.append((QRegExp(r"\bERROR\b"), "error"))
        TraceLogHighlighter._rules.append((QRegExp("|".join([r"\b%s\b" % constant 
                                                            for constant in CONSTANTS])), "constant"))
        TraceLogHighlighter._rules.append((QRegExp(
                r"\b[+-]?[0-9]+[lL]?\b"
                r"|\b[+-]?0[xX][0-9A-Fa-f]+[lL]?\b"
                r"|\b[+-]?[0-9]+(?:\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\b"),
                "number"))
        TraceLogHighlighter._rules.append((QRegExp(
                r"\bPyQt4\b|\bQt?[A-Z][a-z]\w+\b"), "pyqt"))
        TraceLogHighlighter._rules.append((QRegExp(r"\b@\w+\b"),
                "decorator"))
        stringRe = QRegExp(r"""(?:'[^']*'|"[^"]*")""")
        stringRe.setMinimal(True)
        TraceLogHighlighter._rules.append((stringRe, "string"))
        self.stringRe = QRegExp(r"""(:?"["]".*"["]"|'''.*''')""")
        self.stringRe.setMinimal(True)
        TraceLogHighlighter._rules.append((self.stringRe, "string"))
        self._triple_single_re = QRegExp(r"""'''(?!")""")
        self._triple_double_re = QRegExp(r'''"""(?!')''')

    @staticmethod
    def _initialize_formats():
        baseFormat = QTextCharFormat()
        baseFormat.setFontFamily("courier")
        baseFormat.setFontPointSize(12)
        for name, color in (("normal", Qt.black),
                ("keyword", Qt.darkBlue), ("builtin", Qt.darkRed),
                ("constant", Qt.darkGreen),
                ("decorator", Qt.darkBlue), ("comment", Qt.darkGreen),
                ("string", Qt.darkYellow), ("number", Qt.darkMagenta),
                ("Error", Qt.darkRed), ("fail", Qt.darkCyan),
                ("FAIL", Qt.darkRed), ("error", Qt.darkCyan)):
            format = QTextCharFormat(baseFormat)
            format.setForeground(QColor(color))
            if name in ("keyword", "decorator"):
                format.setFontWeight(QFont.Bold)
            if name == "comment":
                format.setFontItalic(True)
            TraceLogHighlighter.Formats[name] = format

    def highlightBlock(self, text):
        NORMAL, TRIPLESINGLE, TRIPLEDOUBLE, ERROR = range(4)

        textLength = len(text)
        prevState = self.previousBlockState()

        self.setFormat(0, textLength,
                       TraceLogHighlighter.Formats["normal"])

        if text.startswith("Traceback") or text.startswith("Error: "):
            self.setCurrentBlockState(ERROR)
            self.setFormat(0, textLength,
                           TraceLogHighlighter.Formats["error"])
            return
        if (prevState == ERROR and not text.startswith("#")):
            self.setCurrentBlockState(ERROR)
            self.setFormat(0, textLength,
                           TraceLogHighlighter.Formats["error"])
            return

        for regex, format in TraceLogHighlighter._rules:
            i = regex.indexIn(text)
            while i >= 0:
                length = regex.matchedLength()
                self.setFormat(i, length,
                               TraceLogHighlighter.Formats[format])
                i = regex.indexIn(text, i + length)

        # Slow but good quality highlighting for comments. For more
        # speed, comment this out and add the following to __init__:
        # TraceLogHighlighter.Rules.append((QRegExp(r"#.*"), "comment"))
        if not text:
            pass
        elif text[0] == "#":
            self.setFormat(0, len(text),
                           TraceLogHighlighter.Formats["comment"])
        else:
            stack = []
            for i, c in enumerate(text):
                if c in ('"', "'"):
                    if stack and stack[-1] == c:
                        stack.pop()
                    else:
                        stack.append(c)
                elif c == "#" and len(stack) == 0:
                    self.setFormat(i, len(text),
                                   TraceLogHighlighter.Formats["comment"])
                    break

        self.setCurrentBlockState(NORMAL)

        if self.stringRe.indexIn(text) != -1:
            return
        # This is fooled by triple quotes inside single quoted strings
        for i, state in ((self._triple_single_re.indexIn(text),
                          TRIPLESINGLE),
                         (self._triple_double_re.indexIn(text),
                          TRIPLEDOUBLE)):
            if self.previousBlockState() == state:
                if i == -1:
                    i = len(text) if isinstance(text, str) else text.length()
                    self.setCurrentBlockState(state)
                self.setFormat(0, i + 3,
                               TraceLogHighlighter.Formats["string"])
            elif i > -1:
                self.setCurrentBlockState(state)
                l = len(text) if isinstance(text, str) else text.length()
                self.setFormat(i, l,
                               TraceLogHighlighter.Formats["string"])

    def rehighlight(self):
        QApplication.setOverrideCursor(QCursor(Qt.WaitCursor))
        QSyntaxHighlighter.rehighlight(self)
        QApplication.restoreOverrideCursor()