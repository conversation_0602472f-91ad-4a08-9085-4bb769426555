# -*- coding: utf-8 -*-
# python
from openai import OpenAI
import httpx
import types
import collections
import types
from httpx import Timeout


class NebulacoderAPI:
    def __init__(self, api_key, api_base, model="nebulacoder-v5.2"):
        self.api_key = api_key
        self.api_base = api_base
        self.model = model
        timeouts = Timeout(
            connect=30.0,   # 连接建立超时
            read=30.0,     # 响应读取超时
            write=30.0,    # 请求写入超时
            pool=30.0      # 连接池获取超时
        )
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.api_base,
            http_client=httpx.Client(
                trust_env=False,
                headers={'Content-Type': 'application/json; charset=utf-8'},
                timeout=timeouts
            )
        )

    def create_chat_completion(self, messages, stream=True, **kwargs):
        return self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=stream,
            **kwargs
        )

    def handle_stream_response(self, response, callback=None):
        res = ''
        docId = ''
        try:
            for chunk in response:
                if not docId:
                    if chunk.id:
                        docId = chunk.id
                print(chunk.choices[0].delta.content or "", end="")
                res += chunk.choices[0].delta.content
                if callback:
                    callback(chunk.choices[0].delta.content)
            return {'bo': {'docId': docId, 'result': res}}
        except Exception as err:
            print(f"Error: {err}")

    def handle_non_stream_response(self, response):
        try:
            print(response)
            print("\n\n")
            output = response.choices[0].message.content
            print(f"[Output]\n{output}")
        except Exception as err:
            print(f"Error: {err}")
    
    @staticmethod
    def get_llm_result(user, modelName, systemPrompt, prompt, callback=None):
        api = NebulacoderAPI(
            api_key=user,
            api_base="http://nebulacoder.dev.zte.com.cn:40081/v1/", 
            model=modelName
        )
        response = api.create_chat_completion(
            messages=[{"role": "system", "content": systemPrompt}, {"role": "user", "content": prompt}],
            stream=True,
            max_tokens=30000,
            temperature=0.1
        )
        if isinstance(response, (types.GeneratorType, collections.abc.Iterable)):
            return api.handle_stream_response(response, callback)
        else:
            return api.handle_non_stream_response(response)

# 使用示例
if __name__ == "__main__":

    prompt_rule = """" 
<新用例开发规范>
你是一个经验丰富的软件测试专家和服务器测试专家，具备丰富的Robotframework自动化开发经验，能够参考已经实现用例操作步骤代码块，准确的开发出新用例。

为了开发出准确的用例脚本，Claude在开发用例时必须时刻优先考虑**诚实、准确、专业、严格**，同时在输出代码前能够进行自我检验准确性，确保输出的准确性和可用性，思考过程遵循<anthropic_thinking_protocol>。

如下是你开发新用例自动化的总体要求，需要严格遵守：
- 你分析用例开发过程时，需要用专业的软件测试知识分析测试用例，设计合理的Robotframework自动化结构；
- 你开发新用例所使用的关键字必须是已经在自动化框架中定义好的，或者实例脚本中已经使用过的关键字；
- 你不可以新增加或新创造关键字，新用例所使用的关键字都应该是框架已定义或内建关键字；

新用例开发请你按照**新用例分析**、**用例实现**、**结果检查**三个步骤及子步骤进行开发，输出格式为Robotframework代码。
## 1、新用例分析：
### 用例分析
> 为了更好的输出代码，优先对测试用例进行分析
- 使用5GNR和多模基站专业领域知识分析新用例测试操作步骤和预期结果的测试意图；
- 分析用例中的[操作][预期结果]，设计合理的测试自动化用例实现结构；
- 被测环境有设置或破坏的操作需要增加恢复；
- 注意识别用例中预置条件中的测试步骤；

### 代码块复用分析
> 为了确保代码输出引用的准确性，对每个测试步骤识别已有不是不可以  触发方式 填写方代码的利用情况
- 输出引用代码块的分析过程，查找时关注测试步骤意图，操作方法相似不是不可以  触发方式 填写方的地方；
- 复用分析输出格式:
```markdown
步骤x分析:
[操作]：%Documentation中操作%
[预期结果]：%Documentation中预期结果%
[分析结果]：%代码复用的原因%
[操作拆解]：%操作拆解步骤%
[可用关键字]:

## 2、用例实现
> 根据上述用例和代码块分析结果，按要求生成测试用例
### 自动化开发
- 按照用例分析结果，参考已有用例的风格设计合理的Robotframework代码结构；
- 按照代码块复用分析结果，引用准确的代码块和已定义的关键字完成脚本开发
- 新用例中[Documentation]中用例信息不需要修改和替换，保持原状；
- 使用LogStep在代码中标记处测试步骤|操作，格式为：LogStep    N <步骤|操作>，注意空格不要错了，与[Documentation]中操作描述保持一致；
- 根据<新用例>测试场景，添加合适的[Setup]和[Teardown]，不要设置[Timeout]；

### 结果输出要求
- 脚本符合robotframework3语法规范，如：FOR循环不能嵌套等；
- 输出格式为Robotframework代码，不能包含其他任何不符合语法规范的信息信息；
- 输出格式为Robotframework格式，兼容Python3.7；


## 3、结果检查
> 对已输出的新用例测试脚本进行自检，输出检查结果
### 基础检查
- [过程检查]回顾整个用例实现过程，排查是否按照用例分析、代码复用分析后，再生成代码，如有遗漏请说明并重新生成；
输出：
> ```markdown
过程检查       - ✔/✘
```
### **关键字检查**
> Claude在思考和仿写自动化用例时，有可能出现所谓的大模型幻觉，Claude应该避免这种情况，如Claude可能会自主设计开发新的关键字并在开发中引用，这是我不想要的结果，望Claude理解，希望通过如下方式回顾检查。
- 严格检查新用例引用的关键字在[case_step_x.tsv]、[keyword_des_x.tsv]文件中有定义或使用；
- 希望Claude检查时先读取项目里已有的关键字，找到文件中定义的关键字起始行，检查时不能为了追求返回速度而忽略了准确性
</新用例开发规范>
    """
    prompt = '''
背景和指令】：你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
    请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'增删改小区：进入载波关断后，修改TNR FDL TDL小区频点及带宽__RAN-6162886'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
    【强调一】：尽量使用示例脚本里已经存在的关键字，你可以新增关键字，但是要给出其定义和描述。
    【强调二 】：以下内容作为脚本的*** settings ***，不要改变内容和格式：
    Suite Setup    加载配置    ConfigRequest    ${dataset}
Suite Teardown    删除配置
Variables	ConfigRequest.py						
Resource	variable/resource.tsv						
Resource	../../../../../../../testlib5g/infrastructure/resource/resource.tsv						
Resource	../../../../../../userkeywords/basic_multi/resource.tsv						
Resource	../../template.tsv						
Resource	keywords.tsv						



    【强调三】： 以下内容加入到*** Keywords ***里，这是已经定义好的关键字，不要改变内容和格式：

    加载配置    [Arguments]    ${scene}	${dataset}

    ${params}    获取资源    ${scene}    ${dataset}

    创建基站_多模    ${ENODEB}+${GNODEB}    ${UME}    ${FDDFUNCTION}

    ${XML_PATH}    导出基站XML并备份    ${ENODEB}    ${UME}    False    普通小区节能

    导入基站数据_多模    ${GNODEB}    ${XML_PATH}

    实例化单板_多模    ${ENODEB}    ${XML_PATH}

    实例化无线配置_多模    ${ENODEB}    ${XML_PATH}

    创建VSW_多模    ${GNODEB}    ${VSW}

    创建PDN    ${PDN}

    获取所有小区别名

    批量删除基站配置_多模    ${GNODEB}    PrruSavingArray

    批量删除基站配置_多模    ${GNODEB}    PrruPowerSaving

    同步规划区数据_多模    ${GNODEB}

    run keyword and ignore error    拆分NR超级小区_多模    ${GNODEB}    1

    同步基站时间_多模    ${GNODEB}

    ${attrDic}    create dictionary    enableEnergySavingService=1

    修改节能服务配置开关    ${GNODEB}    ${attrDic}

    关闭ITRAN-LTE-FDD载波关断总开关



删除配置    [Arguments]    

    释放实例化无线配置_多模    ${ENODEB}

    释放实例化单板_多模    ${ENODEB}

    删除基站_多模    ${ENODEB}+${GNODEB}

    删除UE对象    ${CPE}

    删除PDN    ${PDN}





    

    【用例步骤】：

预置条件：

多prru场景建立小区，各通道各制式都配置NR和LTE载波



操作步骤1：

环境上所有频段的载波都生效了载波关断后，修改所有TNR FDL TDL小区频点及带宽

预期结果1：

修改成功，不影响TNR FDL TDL小区节能状态

1、Son日志:TDL TNR FDL小区无更新,



2、MO编辑器DU小区配置:“小区节能状态”为载波关断;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为载波关断;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为载波关断;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为射频单元载波关断和不生效；



4、prru诊断测试:

TNR TDL FDL小区节能后载波无数字功率，非虚配prru PA关闭状态；



6、验证计数器：进入节能后有非0值上报：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 载波关断时长 C613680002、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：载波关断功能生效时长 C613680012、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：载波关断功能生效时长 C616710002；



4G计数器：

1）小区载波关断时长 C373475044

2）设备级载波关断时长 C374590000



prru计数器：RRU载波关断时长 C370580000、RRU节能时长 C370580004

操作步骤2：

关闭所有TNR FDL TDL小区载波关断

预期结果2：

1、Son日志:TDL TNR FDL小区上报停止载波关断,



2、MO编辑器DU小区配置:“小区节能状态”为非节能;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为非节能;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为非节能;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为空和不生效；



4、prru诊断测试:

TNR TDL FDL小区载波有数字功率，非虚配prru PA打开状态；



6、验证计数器：退出节能后上报0：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 载波关断时长 C613680002、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：载波关断功能生效时长 C613680012、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：载波关断功能生效时长 C616710002；



4G计数器：

1）小区载波关断时长 C373475044

2）设备级载波关断时长 C374590000



prru计数器：RRU载波关断时长 C370580000、RRU节能时长 C370580004



7、所有小区业务接入成功，流量等业务指标正常





                      

    【示例用例步骤】：

预置条件：

多prru场景建立小区，各通道各制式都配置NR和LTE载波



操作步骤1：

环境上所有频段的载波都生效了载波关断后，修改所有TNR FDL TDL小区频点及带宽

预期结果1：

修改成功，不影响TNR FDL TDL小区节能状态

1、Son日志:TDL TNR FDL小区无更新,



2、MO编辑器DU小区配置:“小区节能状态”为载波关断;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为载波关断;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为载波关断;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为射频单元载波关断和不生效；



4、prru诊断测试:

TNR TDL FDL小区节能后载波无数字功率，非虚配prru PA关闭状态；



6、验证计数器：进入节能后有非0值上报：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 载波关断时长 C613680002、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：载波关断功能生效时长 C613680012、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：载波关断功能生效时长 C616710002；



4G计数器：

1）小区载波关断时长 C373475044

2）设备级载波关断时长 C374590000



prru计数器：RRU载波关断时长 C370580000、RRU节能时长 C370580004

操作步骤2：

关闭所有TNR FDL TDL小区载波关断

预期结果2：

1、Son日志:TDL TNR FDL小区上报停止载波关断,



2、MO编辑器DU小区配置:“小区节能状态”为非节能;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为非节能;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为非节能;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为空和不生效；



4、prru诊断测试:

TNR TDL FDL小区载波有数字功率，非虚配prru PA打开状态；



6、验证计数器：退出节能后上报0：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 载波关断时长 C613680002、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：载波关断功能生效时长 C613680012、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：载波关断功能生效时长 C616710002；



4G计数器：

1）小区载波关断时长 C373475044

2）设备级载波关断时长 C374590000



prru计数器：RRU载波关断时长 C370580000、RRU节能时长 C370580004



7、所有小区业务接入成功，流量等业务指标正常



    【示例脚本输出】：

    关闭所有频段载波关断开关

    sleep    30

    打开所有频段载波关断开关

    sleep    90

    确认LTE sonm节能上报成功判断    ${ENODEB}    Carrier Shutdown ES    ES Start    100    2

    Wait Until Keyword Succeeds    15min    60sec    确认FDL小区节能状态    ${ENODEB}    2    0;0;3;0;0;0

    确认LTE sonm节能上报成功判断    ${ENODEB}    Carrier Shutdown ES    ES Start    100    3

    Wait Until Keyword Succeeds    15min    60sec    确认TDL小区节能状态    ${ENODEB}    3    0;0;3;0;0;0

    确认sonm节能监控上报正确    ${GNODEB}    Carrier Shutdown ES    1    Carrier Shutdown    Start ES    50

    Wait Until Keyword Succeeds    15min    60sec    确认小区节能状态    ${cell1}    1

    Wait Until Keyword Succeeds    15min    60sec    确认PRRU节能状态    101-instance    rfCarrierShutdown

    Wait Until Keyword Succeeds    15min    60sec    确认PRRU节能状态    301-instance    rfCarrierShutdown

    Wait Until Keyword Succeeds    15min    60sec    确认所有频段载波PA开关状态    101-instance    Close

    Wait Until Keyword Succeeds    15min    60sec    确认所有频段载波PA开关状态    301-instance    Close

    模板修改NR小区带宽_多模    ${GNODEB}    ${cell1}    80    217

    修改LTE小区上下行频点_多模    ${fddCell1}    1825

    修改LTE小区15M带宽    ${tddCell3}

    检查激活配置    ${ENODEB}

    sleep    360

    Wait Until Keyword Succeeds    10min    60sec    确认小区节能状态    ${cell1}    1

    Wait Until Keyword Succeeds    10min    60sec    确认FDL小区节能状态    ${ENODEB}    1    0;0;3;0;0;0

    Wait Until Keyword Succeeds    10min    60sec    确认TDL小区节能状态    ${ENODEB}    3    0;0;3;0;0;0

    打开所有频段载波关断开关

    sleep    30

    关闭所有频段载波关断开关

    sleep    90

    确认sonm节能监控上报正确    ${GNODEB}    Carrier Shutdown ES    1    Carrier Shutdown    Stop ES    50

    Wait Until Keyword Succeeds    15min    60sec    确认小区节能状态    ${cell1}    0

    确认LTE sonm节能上报成功判断    ${ENODEB}    Carrier Shutdown ES    ES Stop    100    2

    Wait Until Keyword Succeeds    15min    60sec    确认TDL小区节能状态    ${ENODEB}    3    0;0;0;0;0;0

    确认LTE sonm节能上报成功判断    ${ENODEB}    Carrier Shutdown ES    ES Stop    100    3

    Wait Until Keyword Succeeds    15min    60sec    确认PRRU非节能状态    101-instance

    Wait Until Keyword Succeeds    15min    60sec    确认PRRU非节能状态    301-instance

    Wait Until Keyword Succeeds    15min    60sec    确认所有频段载波PA开关状态    101-instance    Open

    Wait Until Keyword Succeeds    15min    60sec    确认所有频段载波PA开关状态    301-instance    Open

    验证FDL小区    ${fddCell1}    ${CPE3}    ${PDN}

    验证TDL小区业务    ${tddCell3}    ${CPE3}    ${PDN}

    sleep    60

    验证NR小区    ${cell1}    ${CPE}    ${PDN}

    验证NR小区    ${cell7}    ${CPE2}    ${PDN}



                          

    【示例用例步骤】：

预置条件：

1.UME和基站建链；2.基站已运行正常，各单板上电正常；



操作步骤1：

请参考用例标题、预制条件或通过准则

预期结果1：





    【示例脚本输出】：

    ${almStart}    查询基站当前告警_多模    ${GNODEB}

    @{rruAlias}    根据类型获取实例化单板别名_多模    ${GNODEB}    128

    ${singleCells}    create list

    : FOR    ${rru}    IN    @{rruAlias}

    \    ${cell}    根据PRRU别名查询承载的NR小区别名_多模    ${rru}

    \    ${cellNum}    get length    ${cell}

    \    Run Keyword If    ${cellNum} ==1    Append to List    ${singleCells}    ${cell[0]}

    \    log    ${singleCells}

    log    ${singleCells}

    ${singleCells}    evaluate    list(set(${singleCells}))

    ${modifiedCells}    Create List

    : FOR    ${DUcell}    IN    @{singleCells}

    \    ${ssbFreq}    查询NR物理DU小区属性_多模    ${DUcell}    CellDefiningSSB    ssbFrequency

    \    Run Keyword If    2496<=${ssbFreq}<=2690    Append to List    ${modifiedCells}    ${DUcell}

    log    ${modifiedCells}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    模板修改NR小区带宽_多模    ${GNODEB}    ${modifiedCell}    100    273    False

    同步规划区数据_多模    ${GNODEB}

    sleep    120

    Wait Until Keyword Succeeds    10min    60sec    确认基站无新增告警_多模    ${GNODEB}    ${almStart}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    确认NR小区状态正常_多模    ${modifiedCell}

    Run Keyword If    '${CPE}'=='None'    log    不验证业务    ELSE    NR业务验证_多模    ${CPE}    ${PDN}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    模板修改NR小区带宽_多模    ${GNODEB}    ${modifiedCell}    80    217    False

    同步规划区数据_多模    ${GNODEB}

    sleep    120

    Wait Until Keyword Succeeds    10min    60sec    确认基站无新增告警_多模    ${GNODEB}    ${almStart}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    确认NR小区状态正常_多模    ${modifiedCell}

    Run Keyword If    '${CPE}'=='None'    log    不验证业务    ELSE    NR业务验证_多模    ${CPE}    ${PDN}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    模板修改NR小区带宽_多模    ${GNODEB}    ${modifiedCell}    60    162    False

    同步规划区数据_多模    ${GNODEB}

    sleep    120

    Wait Until Keyword Succeeds    10min    60sec    确认基站无新增告警_多模    ${GNODEB}    ${almStart}

    : FOR    ${modifiedCell}    IN    @{modifiedCells}

    \    确认NR小区状态正常_多模    ${modifiedCell}

    Run Keyword If    '${CPE}}'=='None'    log    不验证业务    ELSE    NR业务验证_多模    ${CPE}    ${PDN}



                          

    【示例用例步骤】：

预置条件：

多prru场景建立小区，各通道各制式都配置NR和LTE载波



操作步骤1：

环境上所有频段的载波都生效了符号关断后，修改所有TNR FDL TDL小区频点及带宽

预期结果1：

修改成功，不影响TNR FDL TDL小区节能状态

1、Son日志:TDL TNR FDL小区无更新,



2、MO编辑器DU小区配置:“小区节能状态”为符号关断;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为符号关断;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为符号关断;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为射频单元符号关断和不生效；



4、诊断测试：PB供电状态查询的PB网口供电功率，和修改前一致



6、验证计数器：进入节能后有非0值上报：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 符号关断时长 C613680000、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：符号关断功能生效时长 C613680010、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：符号关断功能生效时长 C616710001；



4G计数器：

1）小区符号关断子帧个数 C373475046

2）设备级符号关断时长 C374590002



prru计数器：RRU符号关断时长 C370580002、RRU节能时长 C370580004



7、所有小区业务接入成功，流量等业务指标正常

操作步骤2：

关闭所有TNR FDL TDL小区符号关断

预期结果2：

1、Son日志:TDL TNR FDL小区上报停止符号关断,



2、MO编辑器DU小区配置:“小区节能状态”为非节能;

MO编辑器E-UTRAN TDD小区:“小区节能状态”为非节能;

MO编辑器E-UTRAN FDD小区:“小区节能状态”为非节能;



3、MO编辑器可替换单元:节能模式和通道关断状态，分别显示为空和不生效；



4、诊断测试：PB供电状态查询的PB网口供电功率，回升到节能前；



6、验证计数器：退出节能后上报0：

5G计数器：

1)DU物理小区级设备真实节能生效时长： 符号关断时长 C613680000、小区节能态总时长 C613680004；

2)DU物理小区级节能功能生效时长：符号关断功能生效时长 C613680010、小区节能功能生效总时长 C613680014；

3)载波级节能功能生效时长：符号关断功能生效时长 C616710001；



4G计数器：

1）小区符号关断子帧个数 C373475046

2）设备级符号关断时长 C374590002



prru计数器：RRU符号关断时长 C370580002、RRU节能时长 C370580004



7、所有小区业务接入成功，流量等业务指标正常



    【示例脚本输出】：

    关闭所有频段符号关断开关

    sleep    30

    打开所有频段符号关断开关

    sleep    60

    判断sonm节能上报是否上报成功    ${GNODEB}    DTX ES    Symbol Shutdown    Start ES    30

    确认sonm节能监控上报正确    ${GNODEB}    DTX ES    1    Symbol Shutdown    Start ES    50

    Wait Until Keyword Succeeds    10min    60sec    确认小区节能状态    ${cell1}    3

    确认LTE sonm节能上报成功判断    ${ENODEB}    DTX ES    ES Start    20    1

    Wait Until Keyword Succeeds    10min    60sec    确认FDL小区节能状态    ${ENODEB}    1    1;0;0;0;0;0

    确认LTE sonm节能上报成功判断    ${ENODEB}    DTX ES    ES Start    20    3

    Wait Until Keyword Succeeds    10min    60sec    确认TDL小区节能状态    ${ENODEB}    3    1;0;0;0;0;0

    Wait Until Keyword Succeeds    10min    60sec    确认PRRU节能状态    101-instance    rfSymbolShutdown

    Wait Until Keyword Succeeds    10min    60sec    确认PRRU节能状态    301-instance    rfSymbolShutdown

    模板修改NR小区带宽_多模    ${GNODEB}    ${cell1}    80    217

    修改LTE小区上下行频点_多模    ${fddCell1}    1825

    修改LTE小区15M带宽    ${tddCell3}

    检查激活配置    ${ENODEB}

    sleep    360

    Wait Until Keyword Succeeds    10min    60sec    确认小区节能状态    ${cell1}    3

    Wait Until Keyword Succeeds    10min    60sec    确认FDL小区节能状态    ${ENODEB}    1    1;0;0;0;0;0

    Wait Until Keyword Succeeds    10min    60sec    确认TDL小区节能状态    ${ENODEB}    3    1;0;0;0;0;0

    打开所有频段符号关断开关

    sleep    30

    关闭所有频段符号关断开关

    sleep    60

    判断sonm节能上报是否上报成功    ${GNODEB}    DTX ES    Symbol Shutdown    Stop ES    30

    确认sonm节能监控上报正确    ${GNODEB}    DTX ES    1    Symbol Shutdown    Stop ES    50

    Wait Until Keyword Succeeds    10min    60sec    确认小区节能状态    ${cell1}    0

    确认LTE sonm节能上报成功判断    ${ENODEB}    DTX ES    ES Stop    20    1

    Wait Until Keyword Succeeds    10min    60sec    确认FDL小区节能状态    ${ENODEB}    1    0;0;0;0;0;0

    确认LTE sonm节能上报成功判断    ${GNODEB}    DTX ES    ES Stop    20    3

    Wait Until Keyword Succeeds    10min    60sec    确认TDL小区节能状态    ${ENODEB}    3    0;0;0;0;0;0

    Wait Until Keyword Succeeds    10min    60sec    确认PRRU非节能状态    101-instance

    Wait Until Keyword Succeeds    10min    60sec    确认PRRU非节能状态    301-instance

    验证FDL小区    ${fddCell1}    ${CPE3}    ${PDN}

    验证TDL小区    ${tddCell3}    ${CPE3}    ${PDN}

    sleep    60

    验证NR小区    ${cell1}    ${CPE}    ${PDN}

    验证NR小区    ${cell7}    ${CPE2}    ${PDN}



                          

    【示例用例步骤】：

预置条件：

基站各单板运行正常



操作步骤1：

请参考用例标题与预置条件

预期结果1：





    【示例脚本输出】：

    模板修改NR小区带宽_多模    ${GNODEB}    ${cell}    100    217

    sleep    60

    确认NR小区状态正常_多模    ${cell}

    模板修改NR小区带宽_多模    ${GNODEB}    ${cell}    100    273

    sleep    60

    确认NR小区状态正常_多模    ${cell}



                          

    【示例用例步骤】：

预置条件：

1.UME和基站建链；2.基站已运行正常，各单板上电正常，小区建立正常；



操作步骤1：

2级级联PB，射频合并后配置双载波小区，

预期结果1：



操作步骤2：

删除其中一个小区

预期结果2：



操作步骤3：

查看剩下的小区是否正常，是否有异常告警，业务是否正常

预期结果3：

剩余小区建立正常，无异常告警，业务接入成功

操作步骤4：

复位剩下小区中的部分pRRU (一个或多个，不能全部)

预期结果4：



操作步骤5：

增加原来删除的小区

预期结果5：



操作步骤6：

查看小区是否正常，是否有异常告警，业务是否正常

预期结果6：

所有小区建立正常，无异常告警，业务接入成功

操作步骤7：

删除另外一个小区

预期结果7：



操作步骤8：

查看剩下的小区是否正常，是否有异常告警，业务是否正常

预期结果8：

剩余小区建立正常，无异常告警，业务接入成功

操作步骤9：

复位剩下小区中的部分pRRU (一个或多个，不能全部)

预期结果9：



操作步骤10：

增加原来删除的小区

预期结果10：



操作步骤11：

查看小区是否正常，是否有异常告警，业务是否正常

预期结果11：

所有小区建立正常，无异常告警，业务接入成功



    【示例脚本输出】：

    ${almStart}    查询基站当前告警_多模    ${GNODEB}

    ${prruList}    evaluate    ${PRRUS1}+${PRRUS2}

    ${cell1}    动态创建Qcell-NR小区_多模    ${GNODEB}    1    ${VBP1}    ${prruList}    100    0    ALL    ${True}    ${False}

    ${cell2}    动态创建Qcell-NR小区_多模    ${GNODEB}    2    ${VBP1}    ${prruList}    60    100    ALL    ${True}    ${False}

    同步规划区数据_多模    ${GNODEB}

    sleep    300

    Wait Until Keyword Succeeds    20min    60sec    确认NR小区状态正常_多模    ${cell1}

    Wait Until Keyword Succeeds    3min    60sec    确认NR小区状态正常_多模    ${cell2}

    删除NR小区_多模    ${cell2}

    sleep    200

    : FOR    ${prru}    IN    @{PRRUS1}

    \    复位PRRU_多模    ${prru}

    验证相关信息正常    ${almStart}    ${prruList}    ${cell1}

    ${cell2}    动态创建Qcell-NR小区_多模    ${GNODEB}    2    ${VBP1}    ${prruList}    60    100    ALL    ${True}    ${False}

    同步规划区数据_多模    ${GNODEB}

    sleep    300

    Wait Until Keyword Succeeds    20min    60sec    确认NR小区状态正常_多模    ${cell1}

    Wait Until Keyword Succeeds    3min    60sec    确认NR小区状态正常_多模    ${cell2}

    验证PRRU功率    @{prruList}


'''

    api = NebulacoderAPI(
        api_key="10124054",
        api_base="http://nebulacoder.dev.zte.com.cn:40081/v1/",
        model="nebulacoder-v6.0"
    )
    response = api.create_chat_completion(
        messages=[{"role": "system", "content": prompt_rule}, {"role": "user", "content": prompt}],
        stream=True,
        max_tokens=10000,
        temperature=0.1
    )
    if isinstance(response, (types.GeneratorType, collections.Iterable)):
        print(api.handle_stream_response(response))
    else:
        api.handle_non_stream_response(response)
