
import sys
import copy
import os
import socket

import threading

import robot.utils.encoding
# PLATFORM = platform.python_implementation()
PY2 = sys.version_info[0] == 2
PY3 = not PY2

try:
    import SocketServer as socketserver
except ImportError:  # py3
    try:
        import socketserver
    except ImportError as e:
        raise e

try:
    # to find robot (we use provided lib)
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../lib'))
    from robot.errors import ExecutionFailed
    from robot.running import EXECUTION_CONTEXTS
    from robot.running.signalhandler import STOP_SIGNAL_MONITOR
    from robot.utils import encoding
    from robot.utils.encoding import SYSTEM_ENCODING
except ImportError:
    encoding = None
    # print("TestRunnerAgent: Maybe you did not installed RIDE under this Python?")  # DEBUG
    raise  # DEBUG

# print("DEBUG: console %s system %s" % (encoding.CONSOLE_ENCODING, encoding.SYSTEM_ENCODING))

if sys.hexversion > 0x2060000:
    import json
    _JSONAVAIL = True
else:
    try:
        import simplejson as json
        _JSONAVAIL = True
    except ImportError:
        _JSONAVAIL = False

try:
    import cPickle as pickle
except ImportError:  # py3
    import pickle as pickle

try:
    from cStringIO import StringIO
except ImportError:
    try:
        from StringIO import StringIO
    except ImportError:  # py3
        from io import StringIO

HOST = "localhost"

if sys.hexversion > 0x2060000:
    import json
    _JSONAVAIL = True
else:
    try:
        import simplejson as json
        _JSONAVAIL = True
    except ImportError:
        _JSONAVAIL = False

try:
    import cPickle as pickle
except ImportError:
    import pickle as pickle

try:
    from cStringIO import StringIO
except ImportError:
    from io import StringIO

HOST = "localhost"

try:
    from robot.running import EXECUTION_CONTEXTS

    def _is_logged(level):
        current = EXECUTION_CONTEXTS.current
        if current is None:
            return True
        out = current.output
        if out is None:
            return True
        return out._xmllogger._log_message_is_logged(level)
except ImportError:
    def _is_logged(level):
        from robot.output import OUTPUT
        if OUTPUT is None:
            return True
        return OUTPUT._xmllogger._log_message_is_logged(level)

# Setting Output encoding to UTF-8 and ignoring the platform specs
# RIDE will expect UTF-8
# Set output encoding to UTF-8 for piped output streams
robot.utils.encoding.OUTPUT_ENCODING = 'UTF-8'
# RF 2.6.3 and RF 2.5.7
robot.utils.encoding._output_encoding = robot.utils.encoding.OUTPUT_ENCODING


def dump(obj, fp):
    StreamHandler(fp).dump(obj)


def load(fp):
    return StreamHandler(fp).load()


def dumps(obj):
    fp = StringIO()
    StreamHandler(fp).dump(obj)
    return fp.getvalue()


def loads(s):
    fp = StringIO(s)
    return StreamHandler(fp).load()


class StreamHandler(object):

    loads = staticmethod(loads)
    dumps = staticmethod(dumps)

    def __init__(self, fp):
        if _JSONAVAIL:
            self._json_encoder = json.JSONEncoder(separators=(',', ':'),
                                                  sort_keys=True).encode
            self._json_decoder = json.JSONDecoder(strict=False).decode
        else:
            def json_not_impl(dummy):
                raise NotImplementedError(
                    'Python version < 2.6 and simplejson not installed. Please'
                    ' install simplejson.')
            self._json_decoder = staticmethod(json_not_impl)
            self._json_encoder = staticmethod(json_not_impl)
        self.fp = fp

    def dump(self, obj):
        write_list = []
        if _JSONAVAIL:
            write_list.append('J')
            s = self._json_encoder(obj)
            write_list.extend([str(len(s)), '|', s])
        else:
            write_list.append('P')
            s = pickle.dumps(obj, pickle.HIGHEST_PROTOCOL)
            write_list.extend([str(len(s)), '|', s])
        result = ''.join(write_list)
        self.fp.write(result.encode('utf-8'))

    def load(self):
        header = self._load_header()
        msgtype = header[0]
        msglen = header[1:]
        if not msglen.isdigit():
            raise DecodeError('Message header not valid: %r' % header)
        msglen = int(msglen)
        buff = StringIO()
        buff.write(self.fp.read(msglen))
        try:
            if msgtype == 'J':
                return self._json_decoder(buff.getvalue())
            elif msgtype == 'P':
                return pickle.loads(buff.getvalue())
            else:
                raise DecodeError("Message type %r not supported" % msgtype)
        except DecodeError.wrapped_exceptions as e:
            raise DecodeError(str(e))

    def _load_header(self):
        buff = StringIO()
        while len(buff.getvalue()) == 0 or buff.getvalue()[-1] != '|':
            recv_char = self.fp.read(1)
            if not recv_char:
                raise EOFError('File/Socket closed while reading load header')
            buff.write(recv_char)
        return buff.getvalue()[:-1]


class StreamError(Exception):

    """
    Base class for EncodeError and DecodeError
    """
    pass


class EncodeError(StreamError):
    wrapped_exceptions = (pickle.PicklingError,)


class DecodeError(StreamError):
    wrapped_exceptions = (pickle.UnpicklingError,)
    if _JSONAVAIL:
        if hasattr(json, 'JSONDecodeError'):
            wrapped_exceptions = (pickle.UnpicklingError, json.JSONDecodeError)


class TestRunnerAgent:

    """Pass all listener events to a remote listener

    If called with one argument, that argument is a port
    If called with two, the first is a hostname, the second is a port
    """
    ROBOT_LISTENER_API_VERSION = 2

    def __init__(self, *args):
        self.port = int(args[0])
        self.host = HOST
        self.sock = None
        self.filehandler = None
        self.streamhandler = None
        self._connect()
        self._send_pid()
        self._create_debugger((len(args) >= 2) and (args[1] == 'True'))
        self._create_kill_server()
#         print("TestRunnerAgent: Running under %s %s\n" %
#               (PLATFORM, sys.version.split()[0]))

    def _create_debugger(self, pause_on_failure):
        self._debugger = RobotDebugger(pause_on_failure)

    def _create_kill_server(self):
        self._killer = RobotKillerServer(self._debugger)
        self._server_thread = threading.Thread(
            target=self._killer.serve_forever)
        self._server_thread.setDaemon(True)
        self._server_thread.start()
        self._send_server_port(self._killer.server_address[1])

    def _send_pid(self):
        self._send_socket("pid", os.getpid())

    def _send_server_port(self, port):
        self._send_socket("port", port)

    def start_test(self, name, attrs):
        self._send_socket("start_test", name, attrs)

    def end_test(self, name, attrs):
        self._send_socket("end_test", name, attrs)

    def start_suite(self, name, attrs):
        attrs_copy = copy.copy(attrs)
#         del attrs_copy['doc']
        attrs_copy['is_dir'] = os.path.isdir(attrs['source'])
        self._send_socket("start_suite", name, attrs_copy)

    def end_suite(self, name, attrs):
        attrs_copy = copy.copy(attrs)
#         del attrs_copy['doc']
        attrs_copy['is_dir'] = os.path.isdir(attrs['source'])
        self._send_socket("end_suite", name, attrs_copy)

    def start_keyword(self, name, attrs):
        # pass empty args, see https://github.com/nokia/RED/issues/32

        # we're cutting args from original attrs dict, because it may contain
        # objects which are not json-serializable and we don't need them anyway
        attrs_copy = copy.copy(attrs)
#         try:
#             del attrs_copy['args']
#             del attrs_copy['doc']
#             del attrs_copy['assign']
#         except Exception:
#             pass
        self._send_socket("start_keyword", name, attrs_copy)
        if self._debugger.is_breakpoint(name, attrs):  # must check original
            self._debugger.pause()
        paused = self._debugger.is_paused()
        if paused:
            self._send_socket('paused')
            self._send_socket('disable_pause')
            self._send_socket('enable_continue')
        self._debugger.start_keyword()
        if paused:
            self._send_socket('continue')
            self._send_socket('enable_pause')
            self._send_socket('disable_continue')

    def end_keyword(self, name, attrs):
        # pass empty args, see https://github.com/nokia/RED/issues/32
        attrs_copy = copy.copy(attrs)
#         try:
#             del attrs_copy['args']
#             del attrs_copy['doc']
#             del attrs_copy['assign']
#         except Exception:
#             pass
        self._send_socket("end_keyword", name, attrs_copy)
        paused = self._debugger.end_keyword(attrs['status'] == 'PASS')
        # If paused due to failure, send signals to update UI
        if paused:
            self._send_socket('paused')
            self._send_socket('disable_pause')
            self._send_socket('enable_continue')

    def message(self, message):
        pass

    def log_message(self, message):
#         if _is_logged(message['level']):
        self._send_socket("log_message", message)

    def log_file(self, path):
        self._send_socket("log_file", path)

    def output_file(self, path):
        pass

    def report_file(self, path):
        self._send_socket("report_file", path)

    def summary_file(self, path):
        pass

    def debug_file(self, path):
        pass

    def close(self):
        self._send_socket("close")
        if self.sock:
            self.filehandler.close()
            self.sock.close()

    def _connect(self):
        """Establish a connection for sending data"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            # Iron python does not return right object type if not binary mode
            self.filehandler = self.sock.makefile('wb')
            self.streamhandler = StreamHandler(self.filehandler)
        except socket.error as e:
            print('unable to open socket to "%s:%s" error: %s'
                  % (self.host, self.port, str(e)))
            self.sock = None
            self.filehandler = None

    def _send_socket(self, name, *args):
        try:
            if self.filehandler:
                packet = (name, args)
                self.streamhandler.dump(packet)
                self.filehandler.flush()
        except Exception:
            import traceback
            traceback.print_exc(file=sys.stdout)
            sys.stdout.flush()
            raise


class RobotDebugger(object):

    def __init__(self, pause_on_failure=False):
        self._state = 'running'
        self._keyword_level = 0
        self._pause_when_on_level = -1
        self._pause_on_failure = pause_on_failure
        self._resume = threading.Event()

    @staticmethod
    def is_breakpoint(name, attrs):
        return name == 'BuiltIn.Comment' and attrs['args'] == ['PAUSE']

    def pause(self):
        self._resume.clear()
        self._state = 'pause'

    def pause_on_failure(self, pause):
        self._pause_on_failure = pause

    def resume(self):
        self._state = 'running'
        self._pause_when_on_level = -1
        self._resume.set()

    def step_next(self):
        self._state = 'step_next'
        self._resume.set()

    def step_over(self):
        self._state = 'step_over'
        self._resume.set()

    def start_keyword(self):
        while self._state == 'pause':
            self._resume.wait()
            self._resume.clear()
        if self._state == 'step_next':
            self._state = 'pause'
        elif self._state == 'step_over':
            self._pause_when_on_level = self._keyword_level
            self._state = 'resume'
        self._keyword_level += 1

    def end_keyword(self, passed=True):
        self._keyword_level -= 1
        if self._keyword_level == self._pause_when_on_level \
                or (self._pause_on_failure and not passed):
            self._state = 'pause'
            return True  # Return True to indicate we paused
        return False

    def is_paused(self):
        return self._state == 'pause'


class RobotKillerServer(socketserver.TCPServer):
    allow_reuse_address = True

    def __init__(self, debugger):
        socketserver.TCPServer.__init__(self, ("", 0), RobotKillerHandler)
        self.debugger = debugger


class RobotKillerHandler(socketserver.StreamRequestHandler):

    def handle(self):
        data = self.request.makefile('r').read().strip()
        if data == 'kill':
            self._signal_kill()
        elif data == 'pause':
            self.server.debugger.pause()
        elif data == 'resume':
            self.server.debugger.resume()
        elif data == 'step_next':
            self.server.debugger.step_next()
        elif data == 'step_over':
            self.server.debugger.step_over()
        elif data == 'pause_on_failure':
            self.server.debugger.pause_on_failure(True)
        elif data == 'do_not_pause_on_failure':
            self.server.debugger.pause_on_failure(False)

    @staticmethod
    def _signal_kill():
        try:
            STOP_SIGNAL_MONITOR(1, '')
        except ExecutionFailed:
            pass



