# coding=utf-8
'''
Created on 2020年1月14日

@author: 10240349
'''
from PyQt5.QtWidgets import QCheckBox, QLabel

from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository


class PrintTraceLogSwitch(object):

    def _set_trace_log_switch(self, layout):
        self._print_trace_log_check = QCheckBox()
        PluginRepository().add('TRACE_LOG_CHECK', self._print_trace_log_check)
        layout.addWidget(self._print_trace_log_check)
        label = QLabel(LanguageLoader().get('TRACE_LOG_SWITCH'))
        layout.addWidget(label)

    def is_open(self):
        """检查跟踪日志开关是否打开"""
        try:
            trace_log_check = PluginRepository().find('TRACE_LOG_CHECK')
            if trace_log_check and hasattr(trace_log_check, 'isChecked'):
                return not trace_log_check.isChecked()
            else:
                # 如果找不到控件或控件没有isChecked方法，默认返回True
                return True
        except Exception as e:
            print(f"检查跟踪日志开关状态时出错: {e}")
            return True  # 默认返回True，确保日志能够正常显示
