# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import re, random, difflib
from controller.system_plugin.rf_assistant.RF_Helper.api.AiStudioApi import AiStudioApi
from controller.system_plugin.rf_assistant.RF_Helper.api.MongoDb3 import MongoDb3
from controller.system_plugin.rf_assistant.RF_Helper.api.NebulacoderApi import NebulacoderAPI

TEST_CASE_FILE_DB_TABLE = 'RFHelperTestCaseFile'
TEST_CASE_INFO_DB_TABLE = 'RFHelperTestCases'
BASIC_KEAYWORDS_DB_TABLE = 'RFHelperBasicKeywords'
SUITE_KEYWORDS_DB_TABLE = 'RFHelperSuiteKeywords'

systemPrompt = '''
<新用例开发规范>
你是一个经验丰富的无线通信软件测试专家和服务器测试专家，具备丰富的Robotframework自动化开发经验，能够参考已经实现用例操作步骤代码块，准确的开发出新用例。

为了开发出准确的用例脚本，你在开发用例时必须时刻优先考虑**诚实、准确、专业、严格**，同时在输出代码前能够进行自我检验准确性，确保输出的准确性和可用性，思考过程遵循<anthropic_thinking_protocol>。

如下是你开发新用例自动化的总体要求，需要严格遵守：
- 你分析用例开发过程时，需要用专业的软件测试知识分析测试用例，设计合理的Robotframework自动化结构；
- 你开发新用例所使用的关键字必须是已经在自动化框架中定义好的，或者实例脚本中已经使用过的关键字；
- 你不可以新增加或新创造关键字，新用例所使用的关键字都应该是框架已定义或内建关键字；

新用例开发请你按照**新用例分析**、**用例实现**、**结果检查**三个步骤及子步骤进行开发，输出格式为Robotframework代码。
## 1、新用例分析：
### 用例分析
> 为了更好的输出代码，优先对测试用例进行分析
- 使用5GNR和多模基站专业领域知识以及算力服务器领域分析新用例测试操作步骤和预期结果的测试意图；
- 分析用例中的[操作][预期结果]，设计合理的测试自动化用例实现结构；
- 被测环境有设置或破坏的操作需要增加恢复；
- 注意识别用例中预置条件中的测试步骤；

### 代码块复用分析
> 为了确保代码输出引用的准确性，对每个测试步骤识别已有不是不可以  触发方式 填写方代码的利用情况
- 输出引用代码块的分析过程，查找时关注测试步骤意图，操作方法相似不是不可以  触发方式 填写方的地方；
- 复用分析输出格式:
```markdown
步骤x分析:
[操作]：%Documentation中操作%
[预期结果]：%Documentation中预期结果%
[分析结果]：%代码复用的原因%
[操作拆解]：%操作拆解步骤%
[可用关键字]:%从示例代码中提取的关键字%

## 2、用例实现
> 根据上述用例和代码块分析结果，按要求生成测试用例
### 自动化开发
- 按照用例分析结果，参考已有用例的风格设计合理的Robotframework代码结构；
- 按照代码块复用分析结果，引用准确的代码块和已定义的关键字完成脚本开发
- 新用例中[Documentation]中用例信息不需要修改和替换，保持原状；
- 使用Log    Step在代码中标记处测试步骤|操作，格式为：Log    Step N <步骤|操作>，注意空格不要错了，与[Documentation]中操作描述保持一致；
- 根据<新用例>测试场景，添加合适的[Setup]和[Teardown]，不要设置[Timeout]；

### 结果输出要求
- 脚本符合robotframework3语法规范，如：FOR循环不能嵌套等；
- 输出格式为Robotframework代码，不能包含其他任何不符合语法规范的信息信息；
- 输出格式为Robotframework格式，兼容Python3.7；


## 3、结果检查
> 对已输出的新用例测试脚本进行自检，输出检查结果。
### 基础检查
- [过程检查]回顾整个用例实现过程，排查是否按照用例分析、代码复用分析后，再生成代码，如有遗漏请说明并重新生成；
输出：
> ```markdown
### **关键字检查**
> 你在思考和仿写自动化用例时，有可能出现所谓的大模型幻觉，你应该避免这种情况，如你可能会自主设计开发新的关键字并在开发中引用，这是我不想要的结果，望你理解，希望通过如下方式回顾检查。
- 严格检查新用例引用的关键字在提供的脚本中有定义或使用；
- 希望你在检查时先读取项目里已有的关键字，找到文件中定义的关键字起始行，检查时不能为了追求返回速度而忽略了准确性。
</新用例开发规范>
'''

class AiThread(QThread):
    rf_finished = pyqtSignal(dict)
    caseInfoDic = {}

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

    def get_test_setting(self, case_info):
        text = ''
        keywordText = ''
        keywordsList = []
        if case_info.get('suite_setup'):
            text = text + 'Suite Setup    ' + case_info.get('suite_setup') + '\n'
            k = case_info.get('suite_setup').split(' ')[0]
            keywordsList.append(k)
        if case_info.get('suite_teardown'):
            text = text + 'Suite Teardown    ' + case_info.get('suite_teardown') + '\n'
            k = case_info.get('suite_teardown').split(' ')[0]
            keywordsList.append(k)
        for v in case_info.get('variables'):
            text = text + v + '\n'
        for r in case_info.get('resource'):
            text = text + r + '\n'
        for key in keywordsList:
            if case_info.get('suite_keywords_detail'):
                if case_info.get('suite_keywords_detail').get(key):
                    keywordText = keywordText + key + '\n'.join(case_info.get('suite_keywords_detail').get(key)) + '\n\n'
        return text, keywordText

    def get_sample_test_from_dnstudio(self, testcasePath, testcaseName, maxTerms=10):
        testCaseList = difflib.get_close_matches(testcasePath + '/' + testcaseName, self.parent.testcaseList, n=maxTerms, cutoff=0.01)
        print('-----get_sample_test_from_dnstudio-1096')
        print(testCaseList)
        db = MongoDb3()
        similarTestDic = {}
        similarTestList = []
        filePathList = []
        firstCaseInfo = {}
        i = 0
        random_num = random.randint(1, 4)
        for test in testCaseList:
            i = i + 1
            if i > maxTerms:
                break
            tempDic = {}
            testInfo = test.split('__')
            print('-----get_sample_test_from_dnstudio-1109')
            print(testInfo)
            if len(testInfo) > 4:
                case_id = testInfo[-1]
                case_info = db.query({'id': case_id}, TEST_CASE_INFO_DB_TABLE)
                if case_info:
                    if i == random_num:
                        firstCaseInfo = case_info[0]
                    tempDic['id0'] = case_info[0].get('id0')
                    tempDic['id'] = case_info[0].get('id')
                    text = ''
                    isReal = self._judge_test_case(case_info[0].get('case_content'))
                    if not isReal:
                        continue
                    for c in case_info[0].get('case_content'):
                        text = text + c + '\n'
                    tempDic['script'] = text
                    testName, testStep, _ = self.parent.get_rdc_test_info(case_info[0].get('id0'))
                    print('-----5555')
                    print(case_id)
                    print(case_info[0])
                    tempDic['step'] = testStep
                    tempDic['name'] = testName

                    similarTestDic[case_id] = tempDic
                    similarTestList.append(case_id)
                    filePathList.append(case_info[0].get('file_path'))
        print(similarTestDic.keys())
        return similarTestList, similarTestDic, firstCaseInfo, filePathList

    def _judge_test_case(self, case_content):
        realLen = 0
        for c in case_content:
            if 'log' not in c and '#' not in c:
                realLen = realLen + 1
        if realLen > 1:
            return True
        else:
            return False

    def get_similar_test_from_db(self, sampleCaseIdList):
        db = MongoDb3()
        similarTestDic = {}
        similarTestList = []
        firstCaseInfo = None
        for case_id in sampleCaseIdList:
            tempDic = {}
            case_info = db.query({'id': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
            if not case_info:
                case_info = db.query({'id0': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
            if not firstCaseInfo:
                firstCaseInfo = case_info[0]
            if case_info:
                tempDic['id0'] = case_info[0].get('id0')
                tempDic['id'] = case_info[0].get('id')
                text = ''
                for c in case_info[0].get('case_content'):
                    text = text + c + '\n'
                tempDic['script'] = text
                print(case_info)
                testName, testStep, _ = self.parent.get_rdc_test_info(case_info[0].get('id0'))
                tempDic['step'] = testStep
                tempDic['name'] = testName
                similarTestDic[case_id] = tempDic
                similarTestList.append(case_id)
        return similarTestList, similarTestDic, firstCaseInfo

    def run(self):
        try:
            similarTestCaseText = ''
            similarTestCaseName = 'XXXXXXXXXXXXX'
            similarTestList = []
            asa= AiStudioApi({'account': self.parent.account, "token": self.parent.token})
            testcasePath = self.parent.testcasePath
            testcaseName = self.parent.testcaseNameEdit3.text()
            testcaseStep = self.parent.testcaseStepEdit3.toPlainText()
            if not testcaseName or not testcaseStep:
                self.rf_finished.emit('未查询到用例名称或者用例步骤信息！')
                return
            testId = self.parent.queryEdit3.text()
            text = self.parent.queryEdit31.text()
            scriptTestcaseName = testcaseName + '__' + testId if self.parent.project != 'SSP' else testId + ' ' + testcaseName
            if self.parent.devMode == '新手模式':
                maxTerms = 1
            else:
                maxTerms = 10
            if text:
                sampleCaseIdList = text.split(',')
                print('----1183')
                similarTestList, similarTestDic, firstCaseInfo = self.get_similar_test_from_db(sampleCaseIdList)
                if not similarTestList:
                    print('----1186')
                    similarTestList, similarTestDic, firstCaseInfo, filePathList = self.get_sample_test_from_dnstudio(testcasePath, testcaseName, maxTerms)
            else:
                print('----1187')
                similarTestList, similarTestDic, firstCaseInfo, filePathList = self.get_sample_test_from_dnstudio(testcasePath, testcaseName, maxTerms)
            print(self.parent.devMode)
            print(len(similarTestDic))
            result = {'similarTestList': similarTestList, 'modelName': '', 'pid': '0', 'prompt': '', 'result': '', 'status': 'normal', 'caseId': testId, 'caseName': testcaseName, 'casePath': testcasePath, 'caseStep': testcaseStep, 'mode': self.parent.devMode}
            if self.parent.devMode == '新手模式' and len(similarTestDic) >= 1:
                case_id = similarTestDic[similarTestList[0]].get('id')
                print(case_id)
                db = MongoDb3()
                case_info = db.query({'id': case_id, 'project': self.parent.project}, TEST_CASE_INFO_DB_TABLE)
                print(case_info)
                if case_info:
                    case_info = case_info[0]
                print(case_info)
                similarTestCaseName = case_info.get('name', 'XXXXXXXXXXXXXX')
                testCaseText = self.parent.on_item_clicked(None, case_info)
                similarTestCaseText = testCaseText
                if len(testCaseText) > 30000:
                    testCaseText = testCaseText[0: 30000]
                prompt = f'''

    __DX__:
    ## 角色扮演：
    - 你是一名Robot Framework开发新手。
    ## 任务描述：
    - 请完整无遗漏地在代码块里输出我提供的Robot Framework（RF）代码.
    - 请将用例标题替换成：{scriptTestcaseName}。
    ## 强调：
    - 请在Robot Framework代码块里输出代码。
    ## 代码：
    - 以下是我提供的<Robot Framework代码>：
    ```Robot
    {testCaseText}
    ```
    '''
            else:
                if len(similarTestDic) > 0:
                    setting, keywordText = self.get_test_setting(firstCaseInfo)
                    prompt = f'''
    __DX__:
    ## 背景和指令：
    - 你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
    - 请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{scriptTestcaseName}'作为脚本名称，注意脚本要符合robotframework的语法规范和格式。
    ## 强调一：
    - 尽量使用示例脚本里已经存在的关键字。
    ## 强调二：
    - 以下内容作为脚本的*** settings ***，不要改变内容和格式：
        {setting}
    ## 强调三： 
    - 以下内容加入到*** Keywords ***里，这是已经定义好的关键字，不要改变内容和格式：
        {keywordText}

    ## 用例步骤】：
    - \n{testcaseStep}
    '''
                    i = 0
                    for caseid in similarTestList:
                        i = i + 1
                        if i > 20:
                            break
                        temp = f'''
    # 示例用例步骤：\n{similarTestDic[caseid]['step']}
    # 示例脚本输出：\n{similarTestDic[caseid]['script']}
    '''
                        if len(prompt) + len(temp) < 25000:
                            prompt = prompt + temp
                        else:
                            break
                else:
                    prompt = f'''
    ## 背景和指令：
    - 你是一位资深robotframework脚本开发专家，请根据提供的示例用例步骤和示例脚本输出将下面的用例步骤转化为robotframework脚本。
    - 请深入学习给出的示例脚本，找到用例步骤跟示例脚本关键字之间的联系，将用例步骤转换成一个用例脚本，用'{scriptTestcaseName}'作为脚本名称。
    ## 强调一：
    - 尽量使用示例脚本里已经存在的关键字，不要随意添加新的关键字。脚本要符合robotframework的语法规范和格式。
    ## 用例步骤：\n{testcaseStep}
    '''
            modelNameRaw = self.parent.llmComboBox.currentText()
            modelName = self.parent.llmNameDic.get(modelNameRaw, modelNameRaw)
            result.update({'modelName': modelName})
            termprature = 0.1
            print('正在生成脚本，请稍后...')
            print(prompt)
            if self.parent.devMode == '新手模式':
                ret = asa.few_shot_rf(prompt, 'ZTEAIM-Saturn', termprature)
            elif '星云大模型' in modelNameRaw or '电信大模型' in modelNameRaw:
                ret = asa.few_shot_rf(prompt, 'ZTEAIM-Saturn', termprature)
            else:
                ret = NebulacoderAPI.get_llm_result(self.parent.account, modelName, systemPrompt, prompt, self.parent.stream_genarate_rf_call_back)
            listOutput = []
            if ret:
                pid = ret['bo']['docId']
                response = ret['bo']['result']
                if 'AuthFailed' in response:
                    result.update({'result': 'AuthFailed', 'status': 'abnormal', 'prompt': prompt})
                    self.rf_finished.emit(result)
                    return
                if self.parent.devMode == '新手模式' and similarTestCaseText:
                    response = self._restore_text(response, similarTestCaseText, similarTestCaseName, scriptTestcaseName)
                listRet = response.split('\n')
                listOutput = self.append_ai_code_id(listRet, pid)
                result.update({'result': '\n'.join(listOutput), 'status': 'normal', 'prompt': prompt, 'pid': pid})
            self.rf_finished.emit(result)
        except Exception as e:
            print(e)
            result.update({'result': '生成脚本过程发生异常：\n'+ str(e), 'status': 'abnormal', 'prompt': prompt})
            self.rf_finished.emit(result)
        

    def _is_start_with_space(self, line):
        return re.search('^\s+', line) or re.search('^\\ ', line) or re.search('^\n', line) or re.search('^\r\n', line) or re.search('^\.\.\.', line)

    def _restore_text(self, result, similarTestCaseText, similarTestCaseName, tempTestcaseName):
        result = re.sub('```[rR]?obot\n[\s\S]+$', '```Robot\n\n' + similarTestCaseText + '\n```', result)
        print(result)
        print(similarTestCaseName)
        result = result.replace(similarTestCaseName, tempTestcaseName)
        return result
        
    def append_ai_code_id(self, listRet, pid):
        listOutput = []
        isPair = True
        testcaseBegin = 0
        switch = True
        for line in listRet:
            if 'by AICoder' in line or 'by AI-AutoRFTGen' in line:
                continue
            if line == '```':
                print('777')
                switch = False
            if re.search(r'\*\s?Test Cases\s?\*', line):
                if testcaseBegin != 0:
                    if not isPair:
                        listOutput = self._append_aicoder_end(listOutput, pid)
                        listOutput.append('\n\n')
                        isPair = True
                testcaseBegin = 1
                listOutput.append(line)
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 1:
                testcaseBegin = 2
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            if not self._is_start_with_space(line) and testcaseBegin == 2 and len(line) > 2:
                if not isPair:
                    listOutput = self._append_aicoder_end(listOutput, pid)
                    isPair = True
                if re.search(r'\*\s?Variables\s?\*', line) or re.search(r'\*\s?Settings\s?\*', line):
                    listOutput.append(line)
                    testcaseBegin = 0
                    continue
                listOutput.append(line)
                if isPair and switch:
                    listOutput.append('    Comment    Started by AICoder, pid:{0}'.format(pid))
                    isPair = False
                continue
            listOutput.append(line)
        if not isPair:
            listOutput = self._append_aicoder_end(listOutput, pid)
            isPair = True
        return listOutput

    def _append_aicoder_end(self, listOutput, pid):
        if 'Started by AICoder' in listOutput[-1]:
            return listOutput[0:-1]
        else:
            listOutput.append('    Comment    Ended by AICoder, pid:{0}'.format(pid))
            return listOutput
