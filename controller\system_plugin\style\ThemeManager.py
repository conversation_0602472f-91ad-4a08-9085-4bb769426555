# coding=utf-8
'''
Created on 2024年1月1日

@author: AI Assistant
'''

import os
import codecs
from PyQt5.QtCore import QObject, pyqtSignal

from settings.UserSettings import UserSettings
from utility.PluginRepository import PluginRepository
from utility.Singleton import Singleton


# 主题配置
THEMES = {
    'light': {
        'name': '浅色主题',
        'file': 'default.qss',
        'description': '经典的浅色界面主题'
    },
    'dark': {
        'name': '深色主题',
        'file': 'dark_theme.qss',
        'description': '护眼的深色界面主题'
    },
    'cool_blue': {
        'name': '酷炫淡蓝主题',
        'file': 'cool_blue.qss',
        'description': '清新淡雅的蓝色主题，保护眼睛'
    },
    'eye_protect_green': {
        'name': '酷炫护眼·墨绿色',
        'file': 'eye_protect_green.qss',
        'description': '酷炫墨绿色护眼主题，低亮度柔和色彩，适合长时间使用'
    }
}

THEME_SETTING_KEY = 'UI_THEME'
SYSTEM_ROOT_DIR = "{}/../../..".format(os.path.abspath(os.path.dirname(__file__)))


@Singleton
class ThemeManager(QObject):
    """主题管理器"""

    # 主题改变信号
    theme_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self._current_theme = 'light'
        self._pending_text_editor_theme = None  # 延迟应用的文本编辑器主题
        self._pending_table_theme = None  # 延迟应用的表格主题
        self._dialogs = []  # 需要同步主题的对话框列表
        self._load_current_theme()

    def register_dialog(self, dialog):
        """注册需要同步主题的对话框"""
        if dialog not in self._dialogs:
            self._dialogs.append(dialog)
            # 立即应用当前主题
            self.apply_dialog_theme(dialog)

    def get_themes(self):
        """获取所有可用主题"""
        return THEMES

    def get_current_theme(self):
        """获取当前主题"""
        return self._current_theme

    def set_theme(self, theme_id, save_preference=True):
        """设置主题"""
        if theme_id not in THEMES:
            print(f"未知的主题ID: {theme_id}")
            return False

        try:
            # 应用主题样式
            if self._apply_theme_style(theme_id):
                self._current_theme = theme_id
                # 保存主题偏好
                if save_preference:
                    self._save_theme_preference(theme_id)
                # 通知文本编辑器应用主题
                self.notify_text_editors(theme_id)
                # 通知表格颜色设置刷新
                self.notify_colorization_settings()
                # 通知执行页签刷新
                self.notify_execution_tab(theme_id)
                # 通知RfHelper切换主题
                self.notify_rf_helper(theme_id)
                # 发送主题改变信号
                self.theme_changed.emit(theme_id)
                # 更新所有已注册对话框的主题
                for dialog in self._dialogs:
                    self.apply_dialog_theme(dialog, theme_id)
                print(f"主题已切换到: {THEMES[theme_id]['name']}")
                return True
            else:
                print(f"应用主题失败: {theme_id}")
                return False

        except Exception as e:
            print(f"设置主题时出错: {e}")
            return False

    def _apply_theme_style(self, theme_id):
        """应用主题样式"""
        try:
            theme_info = THEMES[theme_id]
            style_file = os.path.join(SYSTEM_ROOT_DIR, 'resources', 'qss', theme_info['file'])

            if not os.path.exists(style_file):
                print(f"主题文件不存在: {style_file}")
                return False

            # 获取主窗口
            main_frame = PluginRepository().find("MAIN_FRAME")
            if not main_frame:
                print("未找到主窗口")
                return False

            # 读取并应用样式
            with codecs.open(style_file, "r", encoding='utf-8') as f:
                style = f.read()

            main_frame.setStyleSheet(style)
            return True

        except Exception as e:
            print(f"应用主题样式时出错: {e}")
            return False

    def _save_theme_preference(self, theme_id):
        """保存主题偏好设置"""
        try:
            user_settings = UserSettings()
            user_settings.set_value(THEME_SETTING_KEY, theme_id)
        except Exception as e:
            print(f"保存主题设置时出错: {e}")

    def _load_current_theme(self):
        """加载当前主题设置"""
        try:
            user_settings = UserSettings()
            saved_theme = user_settings.get_value(THEME_SETTING_KEY, False)

            # 如果没有保存的主题，使用默认的浅色主题
            if not saved_theme or saved_theme not in THEMES:
                saved_theme = 'light'

            self._current_theme = saved_theme

        except Exception as e:
            print(f"加载主题设置时出错: {e}")
            self._current_theme = 'light'

    def apply_saved_theme(self):
        """应用保存的主题（用于应用启动时）"""
        self.set_theme(self._current_theme, save_preference=False)

    def get_theme_info(self, theme_id):
        """获取主题信息"""
        return THEMES.get(theme_id, None)

    def is_dark_theme(self, theme_id=None):
        """判断是否为深色主题"""
        if theme_id is None:
            theme_id = self._current_theme
        return theme_id in ['dark', 'eye_protect_green']

    def get_theme_list(self):
        """获取主题列表（用于UI显示）"""
        return [(theme_id, theme_info['name'], theme_info['description'])
                for theme_id, theme_info in THEMES.items()]

    def reset_to_default(self):
        """重置为默认主题"""
        self.set_theme('light')

    def refresh_current_theme(self):
        """刷新当前主题（重新应用样式）"""
        self._apply_theme_style(self._current_theme)

    def notify_text_editors(self, theme_id):
        """通知所有文本编辑器应用主题"""
        try:
            from utility.PluginRepository import PluginRepository

            # 1. 查找主要的文本编辑器
            text_editor = PluginRepository().find('TEXT_EDIT')
            if text_editor and hasattr(text_editor, '_apply_theme_from_manager'):
                text_editor._apply_theme_from_manager(theme_id)
                print(f"已通知主文本编辑器应用主题: {theme_id}")

            # 2. 查找编辑插件控制器中的编辑器
            edit_plugin_controller = PluginRepository().find('edit_plugin_controller')
            if edit_plugin_controller and hasattr(edit_plugin_controller, '_editor'):
                editor = edit_plugin_controller._editor
                if editor and hasattr(editor, '_apply_theme_from_manager'):
                    editor._apply_theme_from_manager(theme_id)
                    print(f"已通知编辑插件控制器中的编辑器应用主题: {theme_id}")
                elif editor and hasattr(editor, '_set_theme'):
                    editor._set_theme('dark' if self.is_dark_theme(theme_id) else 'white')
                    print(f"已通知编辑插件控制器中的编辑器应用主题: {theme_id}")

            # 3. 查找其他可能的文本编辑器组件
            # 可以根据需要添加更多编辑器的查找逻辑

        except Exception as e:
            print(f"通知文本编辑器应用主题时出错: {e}")

    def notify_rf_helper(self, theme_id):
        """通知RfHelper应用主题"""
        try:
            from utility.PluginRepository import PluginRepository
            
            # 查找RfHelper组件
            rf_helper = PluginRepository().find('RF_HELPER')
            if rf_helper and hasattr(rf_helper, 'set_theme'):
                rf_helper.set_theme(theme_id)
                print(f"已通知RfHelper应用主题: {theme_id}")
                
        except Exception as e:
            print(f"通知RfHelper应用主题时出错: {e}")

    def notify_execution_tab(self, theme_id):
        """通知执行页签刷新主题"""
        try:
            from utility.PluginRepository import PluginRepository
            
            # 查找执行页签组件
            execution_tab = PluginRepository().find('EXECUTION_TAB')
            if execution_tab:
                # 强制刷新样式
                execution_tab.setStyleSheet(execution_tab.styleSheet())
                print(f"已强制刷新执行页签样式")
                
            # 查找执行状态栏组件
            execution_status = PluginRepository().find('EXECUTION_STATUS')
            if execution_status:
                # 强制刷新样式
                execution_status.setStyleSheet(execution_status.styleSheet())
                print(f"已强制刷新执行状态栏样式")
                
        except Exception as e:
            print(f"通知执行页签刷新主题时出错: {e}")

    def notify_colorization_settings(self):
        """通知表格颜色设置刷新"""
        try:
            from controller.system_plugin.edit.view.component.table.Colorizer import ColorizationSettings
            colorization_settings = ColorizationSettings()
            colorization_settings.refresh_theme_colors()
            print("已通知表格颜色设置刷新")
        except Exception as e:
            print(f"通知表格颜色设置刷新时出错: {e}")

    def _get_current_tab_name(self):
        """获取当前活动页签的名称"""
        try:
            from utility.PluginRepository import PluginRepository
            from settings.i18n.Loader import LanguageLoader

            # 获取页签对象
            edit_tab = PluginRepository().find('EDIT_TAB')
            if not edit_tab:
                return None

            # 获取当前页签索引
            current_index = edit_tab.currentIndex()
            if current_index < 0:
                return None

            # 获取页签文本
            tab_text = edit_tab.tabText(current_index)

            # 将显示文本转换为内部标识符
            language_loader = LanguageLoader()
            if tab_text == language_loader.get('EDIT'):
                return 'EDIT'
            elif tab_text == language_loader.get('TEXT_EDIT'):
                return 'TEXT_EDIT'
            elif tab_text == language_loader.get('RUN'):
                return 'RUN'
            elif tab_text == language_loader.get('BASH'):
                return 'BASH'
            elif tab_text == language_loader.get('RF_ASSISTANT'):
                return 'RF_ASSISTANT'
            else:
                return tab_text  # 返回原始文本作为备用

        except Exception as e:
            print(f"获取当前页签名称时出错: {e}")
            return None

    def apply_pending_themes_for_tab(self, tab_name):
        """为指定页签应用延迟的主题"""
        try:
            # 应用延迟的文本编辑器主题
            if tab_name == 'TEXT_EDIT' and self._pending_text_editor_theme:
                self.notify_text_editors(self._pending_text_editor_theme)
                self._pending_text_editor_theme = None
                print(f"已为文件编辑页签应用延迟的语法高亮主题")

            # 应用延迟的表格主题
            if tab_name == 'EDIT' and self._pending_table_theme:
                self.notify_colorization_settings()
                self._pending_table_theme = None
                print(f"已为编辑页签应用延迟的表格染色主题")

        except Exception as e:
            print(f"应用延迟主题时出错: {e}")

    def has_pending_themes(self):
        """检查是否有待应用的主题"""
        return self._pending_text_editor_theme is not None or self._pending_table_theme is not None

    def get_theme_colors(self, theme_id=None):
        """获取主题相关的颜色配置"""
        if theme_id is None:
            theme_id = self._current_theme

        if theme_id == 'dark':
            return {
                # 编辑器颜色
                'editor_bg': '#2B2B2B',
                'editor_text': '#F0F0F0',
                'editor_selection_bg': '#4A90E2',
                'editor_selection_text': '#FFFFFF',
                'editor_caret': '#FFFFFF',
                'editor_current_line': '#3A3A3A',

                # 行号边距颜色
                'margin_bg': '#353535',
                'margin_text': '#777777',

                # 边框和分割线
                'border_color': '#555555',
                'edge_color': '#555555',

                # 折叠区域
                'fold_margin_bg': '#353535',
                'fold_margin_fg': '#353535',

                # 弹出窗口和对话框颜色
                'dialog_bg': '#252525',
                'dialog_text': '#F5F5F5',
                'dialog_border': '#3A3A3A',
                'dialog_button_bg': '#303030',
                'dialog_button_text': '#F5F5F5',
                'dialog_button_hover_bg': '#2E5D8E',
                'dialog_input_bg': '#303030',
                'dialog_input_text': '#F5F5F5',
                'dialog_input_border': '#3A3A3A',
            }
        elif theme_id == 'light':
            return {
                # 编辑器颜色
                'editor_bg': '#FFFFFF',
                'editor_text': '#000000',
                'editor_selection_bg': '#316AC5',
                'editor_selection_text': '#FFFFFF',
                'editor_caret': '#000000',
                'editor_current_line': '#F5F5F5',

                # 行号边距颜色
                'margin_bg': '#F0F0F0',
                'margin_text': '#666666',

                # 边框和分割线
                'border_color': '#D0D0D0',
                'edge_color': '#C0C0C0',

                # 折叠区域
                'fold_margin_bg': '#F0F0F0',
                'fold_margin_fg': '#FFFFFF',

                # 弹出窗口和对话框颜色
                'dialog_bg': '#FFFFFF',
                'dialog_text': '#000000',
                'dialog_border': '#D0D0D0',
                'dialog_button_bg': '#F0F0F0',
                'dialog_button_text': '#000000',
                'dialog_button_hover_bg': '#E0E0E0',
                'dialog_input_bg': '#FFFFFF',
                'dialog_input_text': '#000000',
                'dialog_input_border': '#D0D0D0',
            }
        elif theme_id == 'cool_blue':
            return {
                # 编辑器颜色
                'editor_bg': '#E0EFFF',
                'editor_text': '#333333',
                'editor_selection_bg': '#4A90E2',
                'editor_selection_text': '#FFFFFF',
                'editor_caret': '#333333',
                'editor_current_line': '#D9EBFF',

                # 行号边距颜色
                'margin_bg': '#D9EBFF',
                'margin_text': '#333333',

                # 边框和分割线
                'border_color': '#B3D1FF',
                'edge_color': '#A0C0FF',

                # 折叠区域
                'fold_margin_bg': '#D9EBFF',
                'fold_margin_fg': '#B3D1FF',
                # 弹出窗口和对话框颜色
                'dialog_bg': '#C4DAFF',
                'dialog_text': '#333333',
                'dialog_border': '#A0C0FF',
                'dialog_button_bg': '#C4DAFF',
                'dialog_button_text': '#333333',
                'dialog_button_hover_bg': '#4A90E2',
                'dialog_input_bg': '#C4DAFF',
                'dialog_input_text': '#333333',
                'dialog_input_border': '#A0C0FF',
            }
        elif theme_id == 'eye_protect_green':
            return {
                # 编辑器颜色
                'editor_bg': '#1B2B1B',  # 深墨绿色
                'editor_text': '#C8E6C9',  # 柔和浅绿
                'editor_selection_bg': '#388E3C',  # 墨绿高亮
                'editor_selection_text': '#FFFFFF',
                'editor_caret': '#A5D6A7',
                'editor_current_line': '#223322',

                # 行号边距颜色
                'margin_bg': '#223322',
                'margin_text': '#81C784',

                # 边框和分割线
                'border_color': '#336633',
                'edge_color': '#2E7D32',

                # 折叠区域
                'fold_margin_bg': '#223322',
                'fold_margin_fg': '#388E3C',

                # 弹出窗口和对话框颜色
                'dialog_bg': '#233D23',
                'dialog_text': '#C8E6C9',
                'dialog_border': '#336633',
                'dialog_button_bg': '#2E7D32',
                'dialog_button_text': '#C8E6C9',
                'dialog_button_hover_bg': '#388E3C',
                'dialog_input_bg': '#233D23',
                'dialog_input_text': '#C8E6C9',
                'dialog_input_border': '#336633',
            }

    def apply_dialog_theme(self, dialog_widget, theme_id=None):
        """为弹出窗口应用主题样式"""
        if theme_id is None:
            theme_id = self._current_theme

        colors = self.get_theme_colors(theme_id)

        # 生成对话框样式表
        style_sheet = f"""
            QDialog {{
                background-color: {colors['dialog_bg']};
                color: {colors['dialog_text']};
                border: 1px solid {colors['dialog_border']};
                border-radius: 8px;
            }}
            
            QGroupBox {{
                background-color: {colors['dialog_bg']};
                color: {colors['dialog_text']};
                border: 1px solid {colors['dialog_border']};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 15px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
                color: {colors['dialog_text']};
            }}

            QLabel {{
                color: {colors['dialog_text']};
                background-color: transparent;
                font-size: 14px;
            }}

            QLineEdit {{
                background-color: {colors['editor_bg']};
                color: {colors['editor_text']};
                border: 1px solid {colors['dialog_input_border']};
                border-radius: 4px;
                padding: 6px;
                font-size: 14px;
            }}

            QLineEdit:focus {{
                border-color: #4A90E2;
            }}

            QTextEdit {{
                background-color: {colors['editor_bg']};
                color: {colors['editor_text']};
                border: 1px solid {colors['dialog_input_border']};
                border-radius: 4px;
                padding: 6px;
                font-size: 14px;
            }}

            QTextEdit:focus {{
                border-color: #4A90E2;
            }}

            QPushButton {{
                background-color: {colors['dialog_button_bg']};
                color: {colors['dialog_button_text']};
                border: 1px solid {colors['dialog_border']};
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }}

            QPushButton:hover {{
                background-color: {colors['dialog_button_hover_bg']};
                color: #FFFFFF;
            }}

            QPushButton:pressed {{
                background-color: #357ABD;
                color: #FFFFFF;
            }}

            QComboBox {{
                background-color: {colors['dialog_input_bg']};
                color: {colors['dialog_input_text']};
                border: 1px solid {colors['dialog_input_border']};
                border-radius: 4px;
                padding: 6px;
                font-size: 14px;
                min-width: 120px;
            }}

            QComboBox:hover {{
                border-color: #4A90E2;
            }}

            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid {colors['dialog_border']};
            }}

            QComboBox QAbstractItemView {{
                background-color: {colors['dialog_bg']};
                color: {colors['dialog_text']};
                border: 1px solid {colors['dialog_border']};
                selection-background-color: #4A90E2;
                selection-color: #FFFFFF;
            }}

            QListWidget {{
                background-color: {colors['dialog_input_bg']};
                color: {colors['dialog_input_text']};
                border: 1px solid {colors['dialog_input_border']};
                border-radius: 4px;
                selection-background-color: #4A90E2;
                selection-color: #FFFFFF;
            }}

            QTreeWidget {{
                background-color: {colors['dialog_input_bg']};
                color: {colors['dialog_input_text']};
                border: 1px solid {colors['dialog_input_border']};
                border-radius: 4px;
                selection-background-color: #4A90E2;
                selection-color: #FFFFFF;
            }}

            QCheckBox {{
                color: {colors['dialog_text']};
                spacing: 8px;
            }}

            QRadioButton {{
                color: {colors['dialog_text']};
                spacing: 8px;
            }}

            QGroupBox {{
                color: {colors['dialog_text']};
                border: 1px solid {colors['dialog_border']};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
                font-weight: bold;
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}

            QTabWidget::pane {{
                border: 1px solid {colors['dialog_border']};
                background-color: {colors['dialog_bg']};
            }}

            QTabBar::tab {{
                background-color: {colors['dialog_button_bg']};
                color: {colors['dialog_text']};
                border: 1px solid {colors['dialog_border']};
                border-bottom: none;
                padding: 8px 16px;
                margin-right: 2px;
            }}

            QTabBar::tab:selected {{
                background-color: {colors['dialog_bg']};
                border-bottom: 1px solid {colors['dialog_bg']};
            }}

            QTabBar::tab:hover:!selected {{
                background-color: {colors['dialog_button_hover_bg']};
            }}
        """

        dialog_widget.setStyleSheet(style_sheet)
        print(f"已为弹出窗口应用{theme_id}主题样式")
