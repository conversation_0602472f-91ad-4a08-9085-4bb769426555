# encoding=utf-8
'''
Create on  2019年10月14日

@author:
'''
import copy

from controller.parser.rules.CommParsedRule import is_cross_row
from controller.parser.rules.NoActionParsedRule import NoActionParsedRule
from model.data_file.Table import Table
from model.data_file.TextBlock import SuiteSetup, SuiteTeardown, \
    TestSetup, TestTeardown, ForceTags, DefaultTags, TestTimeout, Library, \
    Resource, Variables, Documentation, Metadata, TestTemplate


class Settings(Table):

    SETTINGS = {
        Documentation.NO_ACTION_HEAD: Documentation,
        SuiteSetup.NO_ACTION_HEAD: SuiteSetup,
        SuiteTeardown.NO_ACTION_HEAD: SuiteTeardown,
        TestSetup.NO_ACTION_HEAD: TestSetup,
        TestTeardown.NO_ACTION_HEAD: TestTeardown,
        ForceTags.NO_ACTION_HEAD: ForceTags,
        DefaultTags.NO_ACTION_HEAD: DefaultTags,
        TestTimeout.NO_ACTION_HEAD: TestTimeout,
        Library.NO_ACTION_HEAD: Library,
        Resource.NO_ACTION_HEAD: Resource,
        Metadata.NO_ACTION_HEAD: Metadata,
        Variables.NO_ACTION_HEAD: Variables,
        TestTemplate.NO_ACTION_HEAD: TestTemplate
    }

    __slots__ = [
        "_parse_rule",
        "_cells_list",
        "content",
        "documentation",
        "suite_setup",
        "test_setup",
        "suite_teardown",
        "test_teardown",
        "force_tags",
        "default_tags",
        "test_timeout",
        "test_template",
        "imports",
        "metadata"
    ]

    def __init__(self):
        self._parse_rule = NoActionParsedRule()
        self.content = []
        self._cells_list = []

    def parse(self):
        latest_text_block = None
        for cells in self._cells_list:
            tag, values = self._parse_rule.split(cells)
            if is_cross_row(tag):
                latest_text_block.populate(values)
                continue
            if not Settings.SETTINGS.get(tag):
                continue
            text_block = Settings.SETTINGS.get(tag)()
            text_block.populate(values)
            latest_text_block = text_block
            self.content.append(text_block)

    def add_child(self, content):
        self.content.append(eval(content.get("type"))().set_values(content.get("values")))

    def query(self):
        self._clear_attrs()
        for text_block in self.content:
            if text_block.__class__.__name__ in [Library().class_name, Resource().class_name, Variables().class_name]:
                self._update_list_attr(text_block, "imports")
            elif text_block.class_name == Metadata().class_name:
                self._update_list_attr(text_block, text_block._format_attr_name())
            else:
                setattr(self, text_block._format_attr_name(), text_block.query(self.class_name))
        return self

    def _update_list_attr(self, text_block, attr_name):
        import_row = text_block.query(self.class_name)
        import_row.insert(0, text_block.class_name)
        if not hasattr(self, attr_name):
            setattr(self, attr_name, [import_row])
        else:
            getattr(self, attr_name).append(import_row)

    def modify(self, key, content):
        key_2_method = {
            "documentation": self._modify_documentation,
            "suite_setup": self._modify_suite_setup,
            "suite_teardown": self._modify_suite_teardown,
            "test_teardown": self._modify_test_teardown,
            "force_tags": self._modify_force_tags,
            "default_tags": self._modify_default_tags,
            "test_timeout": self._modify_test_timeout,
            "imports": self._modify_imports,
            "metadata": self._modify_metadata,
            "test_setup": self._modify_test_setup
        }
        return key_2_method.get(key)(content)

    def _modify_documentation(self, content):
        if content is None:
            content = ""
        self._modify(Documentation, content)

    def _modify_suite_setup(self, content):
        self._modify(SuiteSetup, content)

    def _modify_suite_teardown(self, content):
        self._modify(SuiteTeardown, content)

    def _modify_test_teardown(self, content):
        self._modify(TestTeardown, content)

    def _modify_test_setup(self, content):
        self._modify(TestSetup, content)

    def _modify_force_tags(self, content):
        self._modify(ForceTags, content)

    def _modify_default_tags(self, content):
        self._modify(DefaultTags, content)

    def _modify_test_timeout(self, content):
        self._modify(TestTimeout, content)

    def _modify_library(self, content):
        index = content.get("index")
        clazz = self._get_target_clazz(Library, index)
        clazz.modify(content.get("content"))

    def _modify_imports(self, content):
        index, table = int(content.get("index")), content.get("table")
        if not hasattr(self, "imports"):
            self._add_import(table[index])
            return True
        if self._is_modify_import(table):
            self._modify_import(table[index], index)
            return True
        if self._is_del_import(table):
            self._del_import(index)
            return False
        if self._is_add_import(table):
            self._add_import(table[index])
            return True

    def _is_modify_import(self, table):
        return len(self.imports) == len(table)

    def _is_del_import(self, table):
        return len(self.imports) > len(table)

    def _modify_import(self, content, index):
        clazz = self._get_target_import(index)
        clazz.modify(content[1:])

    def _is_add_import(self, table):
        return len(self.imports) < len(table)

    def _add_import(self, content):
        clazz = eval(str(content[0]))()
        clazz.modify(content[1:])
        self.content.append(clazz)

    def _del_import(self, index):
        clazz = self._get_target_import(index)
        self.content.remove(clazz)

    def _get_target_import(self, index):
        for clazz in self.content:
            if isinstance(clazz, Library) or isinstance(clazz, Resource) or isinstance(clazz, Variables):
                index -= 1
            if index < 0:
                return clazz
        return None

    def _modify_metadata(self, content):
        self._clear_metadata()
        if content:
            setattr(self, "metadata", [])
        for metadata in content:
            clazz = Metadata()
            clazz.modify(metadata[1:])
            self.content.append(clazz)

    def _clear_metadata(self):
        metadatas = []
        for clazz in self.content:
            if isinstance(clazz, Metadata):
                metadatas.append(clazz)
        for metadata in metadatas:
            self.content.remove(metadata)
        if hasattr(self, "metadata"):
            del self.metadata

    def _modify(self, clazz, content):
        text_block = self._get_text_block(clazz)
        text_block = text_block if text_block else clazz()
        text_block.modify(content)
        self._clear_single_text_block(clazz)
        self.content.insert(0, text_block)

    def _get_text_block(self, clazz):
        for text_block in self.content:
            if isinstance(text_block, clazz):
                return text_block
        return None

    def _clear_single_text_block(self, clazz):
        for index in range(len(self.content)):
            action = self.content[index]
            if isinstance(action, clazz):
                self.content.remove(action)
                break

    def _get_target_clazz(self, clazz_type, index):
        i = 0
        for clazz in self.content:
            if isinstance(clazz, clazz_type):
                if i == index:
                    return clazz
                i += 1

    def _clear_attrs(self):
        if hasattr(self, "documentation"):
            del self.documentation
        if hasattr(self, "suite_setup"):
            del self.suite_setup
        if hasattr(self, "test_setup"):
            del self.test_setup
        if hasattr(self, "suite_teardown"):
            del self.suite_teardown
        if hasattr(self, "test_teardown"):
            del self.test_teardown
        if hasattr(self, "force_tags"):
            del self.force_tags
        if hasattr(self, "default_tags"):
            del self.default_tags
        if hasattr(self, "test_timeout"):
            del self.test_timeout
        if hasattr(self, "test_template"):
            del self.test_template
        if hasattr(self, "imports"):
            del self.imports
        if hasattr(self, "metadata"):
            del self.metadata
