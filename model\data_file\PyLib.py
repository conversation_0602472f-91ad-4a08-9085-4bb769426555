'''
Created on 2019年12月12日

@author: 10243352
'''
import os
import re
import time

import chardet

from model.data_file.KeyWords import KeyWord
from model.data_file.Repository import LocalKeyWordRepository, PyLibRepository
from settings.HistoryProject import HistoryProject


class PyLib(object):

    def __init__(self, relative_path, source_path="", is_import_path=False):
        self._has_class = False
        self._is_import_path = is_import_path
        if not is_import_path:
            abs_path = os.path.abspath(os.path.join(os.path.dirname(source_path), relative_path))
        else:
            root_path = os.path.abspath(HistoryProject.read('PROJECT_PATH'))
            self._import_path = relative_path
            abs_path = os.path.abspath(os.path.join(root_path, relative_path.replace(".", os.path.sep))) + ".py"
        self._path = abs_path

    def parse(self):
        if not self._validity_check():
            return
        if not self._is_need_parse():
            py_lib = PyLibRepository().find((self._path, self._is_import_path))
            for name, keyword in py_lib._keywords:
                LocalKeyWordRepository().add(name, keyword)
        else:
            self._parse()
            PyLibRepository().add((self._path, self._is_import_path), self)

    @property
    def _suffix(self):
        return os.path.splitext(self._path)[-1]

    def _validity_check(self):
        if not os.path.exists(self._path) or self._suffix != ".py":
            return False
        self.latest_modify_time = time.mktime(time.localtime(os.stat(self._path).st_mtime))
        return True

    def _parse(self):
        self._func_keywords = []
        self._class_keywords = []
        self._keywords = []
        self._update_encode_type()
        with open(self._path, "r", encoding=self._encode_type) as f:
            class_name = self._get_class_name()
            line = f.readline()
            while line:
                if not self._has_class:
                    if len(re.findall("^class *{class_name} *\(|^class *{class_name} *:".format(class_name=class_name), line)) == 0:
                        pass
                    else:
                        self._has_class = True
                    if len(re.findall("^def *(\w*) *\((.*)\):", line)) > 0:
                        keyword = self._create_new_keyword(re.findall("^def *(\w*) *\((.*)\):", line))
                        if keyword:
                            self._func_keywords.append(keyword)
                    line = f.readline()
                    continue
                else:
                    keyword = self._create_new_keyword(re.findall("\t| +def *(\w*) *\((.*)\):", line))
                    if keyword:
                        self._class_keywords.append(keyword)
                line = f.readline()
            self._update_local_repo()

    def _update_local_repo(self):
        keywords = self._class_keywords if self._has_class else self._func_keywords
        for keyword in keywords:
            LocalKeyWordRepository().add(keyword.name, keyword)
            self._keywords.append((keyword.name, keyword))
            LocalKeyWordRepository().add(".".join([self._get_class_name(), keyword.name]), keyword)
            if self._is_import_path:
                LocalKeyWordRepository().add(".".join([self._import_path, keyword.name]), keyword)
                self._keywords.append((".".join([self._import_path, keyword.name]), keyword))
            else:
                self._keywords.append((".".join([self._get_class_name(), keyword.name]), keyword))

    def _update_encode_type(self):
        with open(self._path, "rb") as f:
            content = f.read()
        self._encode_type = chardet.detect(content).get("encoding")

    def _get_class_name(self):
        file_name = os.path.split(self._path)[-1]
        return file_name[: (0 - len(self._suffix))]

    def _is_comment(self, line):
        return line.lstrip().startswith("#")

    def _is_empty_line(self, line):
        return line.strip() == ""

    def _create_new_keyword(self, func_def):
        if len(func_def) == 0:
            return
        func_name, arguments = func_def[0]
        return self._create_keyword(func_name, arguments)

    def _create_keyword(self, name, arguments):
        arguments = [argument.strip() for argument in arguments.split(",")] if arguments.strip() != "" else []
        for index in range(0, len(arguments)):
            argument = arguments[index]
            if argument.startswith("*"):
                arguments[index] = arguments[index].strip("*") + "=None"
            else:
                arguments[index] = argument.strip()
        arguments = arguments[1:] if len(arguments) > 0 and str(arguments[0]) == "self" else arguments
        keyword = KeyWord(name)
        keyword.path = os.path.abspath(self._path)
        keyword.modify("documentation", "")
        keyword._modify_arguments([" | ".join(arguments), ""], is_clear_repo=False)
        return keyword

    def _is_need_parse(self):
        py_lib = PyLibRepository().find((self._path, self._is_import_path))
        if not py_lib:
            return True
        if self.latest_modify_time > py_lib.latest_modify_time:
            PyLibRepository().delete((self._path, self._is_import_path))
            return True
        return False


if __name__ == '__main__':
    PyLib(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\testlib\app_service\basic\cell\CellService.py").parse()
    print(LocalKeyWordRepository().keys())
#     for name in LocalKeyWordRepository().keys():
#         print(LocalKeyWordRepository().query(name))
#     modifiedTime = time.mktime(time.localtime(os.stat(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\testlib5g\app_service\basic\ume\pm\PmService.py").st_mtime))
#     print("11111111111111", modifiedTime)
