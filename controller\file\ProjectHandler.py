# coding=utf-8
'''
Created on 2019年12月17日

@author: 10240349
'''
import os

from PyQt5.QtWidgets import QFileDialog

from controller.ProjectLoader import ProjectLoader
from settings.HistoryProject import HistoryProject
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from view.common.MessageBox import MessageBox
from view.common.dialog.NewProjectDialog import NewProjectDialog
from utility.PathUtils import PathUtils

# 使用跨平台的用户数据目录，避免硬编码D盘路径
TEMP_PROJECT_PATH = PathUtils.get_temp_project_path()

class ProjectHandler(object):

    INIT_THREAD = None

    @staticmethod
    def find_project():
        path = QFileDialog.getExistingDirectory()
        if not path:
            path = HistoryProject().read('PROJECT_PATH')
        else:
            if path and ProjectHandler._is_valid_project(path):
                HistoryProject().write('PROJECT_PATH', path)
                with open (TEMP_PROJECT_PATH, 'w', encoding='utf-8') as f:
                    f.write(path)
                ExecutiveTestCaseRepository().clear()
                ProjectLoader(path).load()
            else:
                MessageBox().show_critical(LanguageLoader().get('INVALID_PROJECT'))

    @staticmethod
    def _is_valid_project(path):
        file_list = os.listdir(path)
        rfcode_file_is_exist = False
        for filename in file_list:
            if filename == '.rfcode':
                rfcode_file_is_exist = True
        return rfcode_file_is_exist

    @staticmethod
    def _clear_history_project():
        HistoryProject().write('PROJECT_PATH', '')

    @staticmethod
    def create_project(this):
        this._new_project = NewProjectDialog()
        this._new_project.show()
