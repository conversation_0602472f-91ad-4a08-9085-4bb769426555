# encoding=utf-8
'''
Create on  2019年10月14日

@author:
'''
import re

from controller.parser.rules.CommParsedRule import is_cross_row
from controller.parser.rules.NoActionParsedRule import NoActionParsedRule
from model.data_file.Repository import VariableRepository, \
    LocalVariableRepository
from model.data_file.Table import Table
from model.data_file.TextBlock import TextBlock
from utility import DataHandler
from utility.ObjectRepository import ObjectRepository
from utility.Singleton import Singleton


class Variable(object):

    __slots__ = [
        "_parse_rule",
        "content",
        "_last_action",
        "name",
        "tag",
        "path"
    ]

    def __init__(self, name="${}"):
        self.name = self._get_name(name) if re.findall("{(.*)}", name) else name
        self.content = []
        self._parse_rule = NoActionParsedRule()

    def populate(self, cells):
        self.content.append(cells)

    def anti_populate(self, table_type=None):
        if len(self.content) > 0:
            first_line = [self.tag + "{%s}" % (self.name)]
            first_line.extend(self.content[0])
            content = TextBlock.TAB.join(first_line)
        for cells in self.content[1:]:
            content += TextBlock.ENTER
            content += (TextBlock.CROSS_ROW + TextBlock.TAB + TextBlock.TAB.join(cells))
        return content + TextBlock.ENTER

    def modify(self, key, content):
        key_2_method = {
            "name": self._re_name,
            "content": self._modify_content
        }
        return key_2_method.get(key)(content)

    def _get_name(self, name):
        return re.findall("{(.*)}", name)[0]

    def _re_name(self, name):
        VariableRepository().delete(self)
        LocalVariableRepository().delete(self)
        self.name = name if len(re.findall("{(.*)}", name)) == 0 else re.findall("{(.*)}", name)[0]
        VariableRepository().add(self.name, self)
        LocalVariableRepository().add(self.name, self)

    def _get_content(self):
        content = []
        for row in self.content:
            content.extend(row)
        return content

    def _split_content(self):
        content = self._get_content()
        for index in range(len(content)):
            cell = content[index]
            if cell.startswith("#"):
                return content[:index], content[index:]
        return content, []

    def _modify_content(self, content):
        name, values, comments = content
        name = self._get_name(name)
        values = self._format_values(values)
        comments = [comment.strip() for comment in comments.split("|")] if comments != "#" and comments.strip() != "" else []
        if name != self.name:
            self._re_name(name)
        values.extend(comments)
        self.content = [values]

    def _format_values(self, values):
        return [DataHandler.replace_blank_spaces(value) for value in values]


class Dict(Variable):

    def __init__(self, name):
        super().__init__(name)
        self.tag = "&"
        self.full_name = "%s{%s}" % (self.tag, self.name)

    def query(self):
        values, comments = self._split_content()
        return [self.tag + "{" + self.name + "}", values, " | ".join(comments)]

    def parse(self):
        pass


class Scalar(Variable):

    def __init__(self, name):
        super().__init__(name)
        self.tag = "$"
        self.full_name = "%s{%s}" % (self.tag, self.name)

    def query(self):
        values, comments = self._split_content()
        return [self.tag + "{" + self.name + "}", values, " | ".join(comments)]

    def parse(self):
        pass


class List(Variable):

    def __init__(self, name):
        super().__init__(name)
        self.tag = "@"
        self.full_name = "%s{%s}" % (self.tag, self.name)

    def query(self):
        values, comments = self._split_content()
        return [self.tag + "{" + self.name + "}", values, " | ".join(comments)]

    def parse(self):
        pass


class VariableFactory(object):

    @staticmethod
    def get_instance(name):
        if len(name) == 0:
            return None
        if name[0] == "@":
            return List(name)
        elif name[0] == "$":
            return Scalar(name)
        elif name[0] == "&":
            return Dict(name)
        else:
            return None


class Variables(Table):

    __slots__ = [
        "_parse_rule",
        "_cells_list",
        "content"
    ]

    def __init__(self):
        self._parse_rule = NoActionParsedRule()
        self.content = []
        self._cells_list = []

    def parse(self):
        latest_variable = None
        for cells in self._cells_list:
            tag, values = self._parse_rule.split(cells)
            if is_cross_row(tag):
                latest_variable.populate(values)
                continue
            variable = VariableFactory.get_instance(tag)
            if not variable:
                continue
            variable.populate(values)
            latest_variable = variable
            self.content.append(variable)

    def add_child(self, content):
        name = content.get("name")
        if self.has_same_variable(name):
            return "repeat"
        if content.get("type").lower() == "list":
            variable = List("@{" + name + "}")
        elif content.get("type").lower() == "scalar":
            variable = Scalar("${" + name + "}")
        elif content.get("type").lower() == "dict":
            variable = Dict("&{" + name + "}")
        variable.path = content.get("path")
        VariableRepository().add(name, variable)
        LocalVariableRepository().add(name, variable)
        variable._modify_content(content.get("value"))
        self.content.append(variable)

    def del_child(self, options):
        index = int(options.get("index"))
        VariableRepository().delete(self.content[index])
        LocalVariableRepository().delete(self.content[index])
        self.content.remove(self.content[index])

    def has_same_variable(self, name):
        for variable in self.content:
            if variable.name == name:
                return True
        return False

    def query(self):
        return [variable.query() for variable in self.content]

    def modify(self, content):
        index, table = int(content.get("index")), content.get("table")
        if self.content == []:
            self._add_child(table[index])
            self.query()
            return
        if self._is_modify(table):
            self._modify_child(table[index], index)
        if self._is_del(table):
            self._del_child(index)
        if self._is_add(table):
            self._add_child(table[index])
        self.query()

    def _is_modify(self, table):
        return len(self.content) == len(table)

    def _is_del(self, table):
        return len(self.content) > len(table)

    def _modify_child(self, content, index):
        clazz = self._get_target_variable(index)
        clazz.modify("content", content)

    def _is_add(self, table):
        return len(self.content) < len(table)

    def _del_child(self, index):
        self.content.remove(self.content[index])

    def _add_child(self, content):
        full_name = content[0]
        name = self._get_name(content[0])
        type = VariableFactory.get_instance(full_name).__class__.__name__.lower()
        self.add_child({"name": name, "type": type, "value": content})

    def _get_target_variable(self, index):
        for clazz in self.content:
            index -= 1
            if index < 0:
                return clazz
        return None
