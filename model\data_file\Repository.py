'''
Created on 2019年11月8日

@author: 10243352
'''
import copy
import re

from utility.ObjectRepository import ObjectRepository
from utility.Singleton import Singleton


@Singleton
class PyLibRepository(ObjectRepository):

    def add(self, key, obj):
        self._objDict[key] = obj
        return True

    def delete(self, key):
        if self._has_key(key):
            del self._objDict[key]

    def delete_by_regx(self, regx):
        for k, _ in self._objDict:
            self._objDict.pop(k)


@Singleton
class BuildInTagRepository(ObjectRepository):

    def query(self, key):
        if key.lower() in [": for", ":for", "in", "in range"]:
            return {key: [{
                "documentation": "",
                "arguments": [],
                "path": "",
                "is_tag": True
            }]}
        return {}


@Singleton
class ArgumentsRepository(ObjectRepository):

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]

    def query(self, key):
        if key in self._objDict.keys():
            return {key: {"path": None}}


@Singleton
class BuildInVariableRepository(ObjectRepository):

    def query(self, key):
        build_in_variables = [
            "EMPTY", "True", "False", "None", "SPACE", "TEST NAME", "TEST TAGS",
            "TEST DOCUMENTATION", "TEST STATUS", "TEST MESSAGE", "PREV TEST NAME",
            "PREV TEST STATUS", "PREV TEST MESSAGE", "SUITE NAME", "SUITE SOURCE",
            "SUITE DOCUMENTATION", "SUITE METADATA", "SUITE STATUS", "SUITE MESSAGE",
            "KEYWORD STATUS", "KEYWORD MESSAGE", "LOG LEVEL", "OUTPUT FILE", "LOG FILE",
            "REPORT FILE", "DEBUG FILE", "OUTPUT DIR"]
        format_key = BuildInVariableRepository().format_key(key)
        if format_key in build_in_variables or key in build_in_variables:
            return {key: {"path": None}}
        if key.isdigit():
            return {key: {"path": None}}
        if re.findall("^SPACE *\* *\d+$", key):
            return {key: {"path": None}}
        return {}

    @staticmethod
    def format_key(key):
        return key.replace("_", " ").upper()


@Singleton
class BuildInKeyWordRepository(ObjectRepository):

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]
            return
        if keyword not in results:
            results.append(keyword)

    def delete(self, keyword):
        results = self.find(keyword.name)
        if results is None:
            return
        if len(results) == 1:
            del self._objDict[keyword.name]
        for result in results:
            if result.name == keyword.name and result.path == keyword.path:
                results.remove(result)
                break

    def query(self, keyword_name):
        keywords = self.find(keyword_name.lower())
        if not keywords:
            return {}
        rets = {keyword_name: []}
        for keyword in keywords:
            keyword.query()
            rets[keyword_name].append({
                "documentation": keyword.documentation if hasattr(keyword, "documentation") else "",
                "arguments": [argument.strip() for argument in keyword.arguments[0].split("|")] if hasattr(keyword, "arguments") and keyword.arguments and keyword.arguments[0].strip() != "" else [],
                "path": keyword.path
            })
        return rets


@Singleton
class GlobalBuildInKeyWordRepository(ObjectRepository):

    def __init__(self):
        super().__init__()
        self.is_init = False

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]
            return
        if keyword not in results:
            results.append(keyword)

    def fuzzy_query(self, key):
        rets = {}
        keyword_names = filter(lambda keyword_name: key.lower() in keyword_name, self._objDict.keys())
        for keyword_name in keyword_names:
            rets[keyword_name] = []
            keywords = self.find(keyword_name)
            for keyword in keywords:
                keyword.query()
                rets[keyword_name].append({
                    "documentation": keyword.documentation,
                    "arguments": [argument.strip() for argument in keyword.arguments[0].split("|")] if hasattr(keyword, "arguments") else [],
                    "path": keyword.path
                })
        return rets


@Singleton
class LocalKeyWordRepository(ObjectRepository):

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]
            return
        if keyword not in results:
            results.append(keyword)

    def delete(self, keyword):
        results = self.find(keyword.name)
        if results is None:
            return
        if len(results) == 1:
            del self._objDict[keyword.name]
        for result in results:
            if result.name == keyword.name and result.path == keyword.path:
                results.remove(result)
                break

    def query(self, keyword_name):
        rets = BuildInTagRepository().query(keyword_name)
        if rets:
            return rets
        rets = BuildInKeyWordRepository().query(keyword_name)
        if rets:
            return rets
        rets = {keyword_name: []}
        keywords = self.find(keyword_name)
        if not keywords:
            return {}
        for keyword in keywords:
            keyword.query()
            if hasattr(keyword, "arguments") and keyword.arguments and keyword.arguments[0].strip() != "":
                arguments = []
                for argument in keyword.arguments[0].split("|"):
                    if argument.strip().startswith("@{"):
                        argument = argument.strip() + "=${EMPTY}"
                    arguments.append(argument.strip())
            else:
                arguments = []
            rets[keyword_name].append({
                "documentation": keyword.documentation if hasattr(keyword, "documentation") else "",
                "arguments": arguments,
                "path": keyword.path
            })
        return rets


@Singleton
class KeyWordRepository(ObjectRepository):

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]
            return
        if keyword not in results:
            results.append(keyword)

    def delete(self, keyword):
        results = self.find(keyword.name)
        if results is None:
            return
        if len(results) == 1:
            del self._objDict[keyword.name]
        for result in results:
            if result.name == keyword.name and result.path == keyword.path:
                results.remove(result)
                break

    def fuzzy_query(self, key):
        rets = GlobalBuildInKeyWordRepository().fuzzy_query(key)
        keyword_names = filter(lambda keyword_name: key in keyword_name, self._objDict.keys())
        for keyword_name in keyword_names:
            rets[keyword_name] = []
            keywords = self.find(keyword_name)
            for keyword in keywords:
                rets[keyword_name].append({
                    "documentation": keyword.documentation if hasattr(keyword, "documentation") else "",
                    "arguments": [argument.strip() for argument in keyword.arguments[0].split("|")] if hasattr(keyword, "arguments") else [],
                    "path": keyword.path
                })
        return rets

    def query(self, keyword_name):
        rets = BuildInTagRepository().query(keyword_name)
        if rets:
            return rets
        rets = BuildInKeyWordRepository().query(keyword_name)
        if rets:
            return rets
        rets = {keyword_name: []}
        keywords = self.find(keyword_name)
        if not keywords:
            return {}
        for keyword in keywords:
            keyword.query()
            rets[keyword_name].append({
                "documentation": keyword.documentation if hasattr(keyword, "documentation") else "",
                "arguments": [argument.strip() for argument in keyword.arguments[0].split("|")] if hasattr(keyword, "arguments") else [],
                "path": keyword.path
            })
        return rets


@Singleton
class VariableRepository(ObjectRepository):

    def add(self, key, variable):
        results = self.find(key)
        if not results:
            self._objDict[key] = [variable]
            return
        if variable not in results:
            results.append(variable)

    def delete(self, variable):
        results = self.find(variable.name)
        if results is None:
            return
        if len(results) == 1:
            del self._objDict[variable.name]
        for result in results:
            if result == variable:
                results.remove(result)
                break

    def fuzzy_match(self, key):
        rets = {}
        variable_names = filter(lambda variable: key in variable, self._objDict.keys())
        for variable_name in variable_names:
            rets[variable_name] = []
            variables = self.find(variable_name)
            for varaible in variables:
                rets[variable_name].append({
                    "path": varaible.path
                })
        return rets

    def query(self, key):
        if BuildInVariableRepository().query(key):
            return BuildInVariableRepository().query(key)
        rets = {key: []}
        variables = self.find(key)
        if variables is None:
            return
        for varaible in variables:
            rets[key].append({
                "path": varaible.path
            })


@Singleton
class LocalVariableRepository(ObjectRepository):

    def add(self, key, keyword):
        results = self.find(key)
        if not results:
            self._objDict[key] = [keyword]
            return
        if keyword not in results:
            results.append(keyword)

    def delete(self, keyword):
        results = self.find(keyword.name)
        if results is None:
            return
        if len(results) == 1:
            del self._objDict[keyword.name]
        for result in results:
            if result.name == keyword.name and result.path == keyword.path:
                results.remove(result)
                break

    def fuzzy_match(self, key):
        rets = {}
        variable_names = filter(lambda variable: key in variable, self._objDict.keys())
        for variable_name in variable_names:
            rets[variable_name] = []
            variables = self.find(variable_name)
            for varaible in variables:
                rets[variable_name].append({
                    "path": varaible.path
                })
        return rets

    def query(self, key):
        rets = ArgumentsRepository().query(key)
        if rets:
            return rets
        if BuildInVariableRepository().query(key):
            return BuildInVariableRepository().query(key)
        rets = {key: []}
        variables = self.find(key)
        if not variables:
            return {}
        for varaible in variables:
            rets[key].append({
                "path": getattr(varaible, "path", None)
            })
        return rets


@Singleton
class ProjectTreeRepository(ObjectRepository):

    def add(self, key, obj):
        self._objDict[key] = obj
        return True

    def delete(self, key):
        if self._has_key(key):
            del self._objDict[key]

    def delete_by_regx(self, regx):
        for k, _ in self._objDict:
            self._objDict.pop(k)


@Singleton
class DataFileRepository(ObjectRepository):

    def add(self, key, obj):
        self._objDict[key] = obj
        return True

    def delete(self, key):
        if self._has_key(key):
            del self._objDict[key]

    def delete_by_regx(self, regx):
        for k, _ in self._objDict:
            self._objDict.pop(k)


if __name__ == '__main__':
    #     GlobalBuildInKeyWordRepository().init()
    print(BuildInVariableRepository().query("False"))
