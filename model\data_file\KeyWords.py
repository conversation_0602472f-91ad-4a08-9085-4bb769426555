# encoding=utf-8
'''
Create on  2019年10月14日

@author:
'''
import re

from controller.parser.rules.ActionParsedRule import ActionParsedRule
from controller.parser.rules.CommParsedRule import is_cross_row, is_cross_for
from model.data_file.Repository import KeyWordRepository, LocalKeyWordRepository, \
    ArgumentsRepository
from model.data_file.Table import Table
from model.data_file.TextBlock import *
from model.data_file.Variables import <PERSON>alar
from utility.ObjectRepository import ObjectRepository
from utility.Singleton import Singleton


class KeyWord(object):

    SETTINGS = ['[Arguments]', '[Documentation]', '[Timeout]', '[Teardown]', '[Return]']

    __slots__ = [
        "_parse_rule",
        "content",
        "_last_action",
        "name",
        "arguments",
        "documentation",
        "timeout",
        "teardown",
        "return",
        "body",
        "path",
    ]

    def __init__(self, name):
        self.path = ""
        self.name = name
        self.content = []
        self._last_action = None
        self._parse_rule = ActionParsedRule()

    def parse(self):
        pass

    def populate(self, cells):
        key, arguments = self._parse_rule.split(cells)
        if is_cross_row(key) and isinstance(self._last_action, FOR) and self._last_action._is_only_has_head():
            self._last_action._values[0].extend(arguments)
        elif isinstance(self._last_action, FOR) and is_cross_for(key):
            self._last_action.populate(arguments)
        elif isinstance(self._last_action, FOR) and is_cross_row(key):
            self._last_action.populate(cells)
        elif is_cross_row(key):
            self._last_action.populate(arguments)
        else:
            if key.strip('[|]|:').strip().lower() == "for":
                clazz = FOR()
            elif self._is_settings(key.replace(" ", "")):
                clazz = eval(key.strip('[|]|:').strip())()
            else:
                clazz = Func()
            self._last_action = clazz
            clazz.populate(cells)
            self.content.append(clazz)

    def _is_settings(self, cell):
        return cell in KeyWord.SETTINGS

    def anti_populate(self, table_type):
        content = self.name + TextBlock.ENTER
        self.query()
        if hasattr(self, "body"):
            self.modify("body", self.body)
        for action in self.content:
            content += action.anti_populate(table_type)
        return content + TextBlock.ENTER

    def query(self):
        self._clear_attrs()
        body = []
        for text_block in self.content:
            if isinstance(text_block, Func) or isinstance(text_block, FOR):
                body.extend(text_block.query("KeyWords"))
                continue
            setattr(self, text_block.__class__.__name__.lower(), text_block.query("KeyWords"))
        setattr(self, "body", body)
        return self

    def get_doc(self):
        for action in self.content:
            if isinstance(action, Documentation):
                return action.query()
        return ""

    def modify(self, key, content):
        key_2_method = {
            "arguments": self._modify_arguments,
            "documentation": self._modify_documentation,
            "timeout": self._modify_timeout,
            "teardown": self._modify_teardown,
            "return_value": self._modify_return,
            "body": self._modify_body,
            "name": self._re_name
        }
        key_2_method.get(key)(content)

    def _re_name(self, name):
        KeyWordRepository().delete(self)
        LocalKeyWordRepository().delete(self)
        self.name = name
        KeyWordRepository().add(self.name, self)
        LocalKeyWordRepository().add(self.name, self)

    def _modify_arguments(self, content, is_clear_repo=True):
        if content is None:
            self._modify(Arguments, ["", ""])
        else:
            self._modify(Arguments, content)
        if is_clear_repo:
            ArgumentsRepository().clear()
        if content is None:
            content = ['[Arguments]', '']
        arguments = [argument.strip() for argument in content[0].split("|")]
        for argument in arguments[1:]:
            name = argument.split("=")[0]
            match_names = re.findall("{(.*)}", name)
            name = name if match_names == [] else match_names[0]
            ArgumentsRepository().add(name, Scalar("${%s}" % (name)))

    def _modify_documentation(self, content):
        self._modify(Documentation, content)

    def _modify_timeout(self, content):
        self._modify(Timeout, content)

    def _modify_teardown(self, content):
        self._modify(Teardown, content)

    def _modify_return(self, content):
        self._modify(Return, content)

    def _modify(self, clazz, content):
        text_block = self._get_text_block(clazz)
        text_block = text_block if text_block else clazz()
        if isinstance(content, list):
            content[0] = clazz.ACTION_HEAD + "|" + content[0]
        elif isinstance(content, str):
            content = clazz.ACTION_HEAD + "|" + content
        elif content is None and isinstance(text_block, Documentation):
            content = clazz.ACTION_HEAD
        elif content is None and not isinstance(text_block, Documentation):
            content = [clazz.ACTION_HEAD, ""]
        text_block.modify(content)
        self._clear_single_text_block(clazz)
        self.content.insert(0, text_block)

    def _modify_body(self, cells_list):
        steps, self._last_action = [], Func()
        for cells in cells_list:
            key, arguments = self._parse_rule.split(cells)
            if is_cross_row(key):
                self._last_action.populate(arguments)
            elif (key == "\\" or key == "") and isinstance(self._last_action, FOR):
                self._last_action.populate(arguments)
            else:
                if key.strip('[|]|:').strip().lower() == "for":
                    clazz = FOR()
                else:
                    clazz = Func()
                self._last_action = clazz
                clazz.populate(cells)
                steps.append(clazz)
        self._clear_steps()
        self.content.extend(steps)

    def _clear_steps(self):
        actions = []
        for index in range(len(self.content)):
            action = self.content[index]
            if not self._is_step(action):
                continue
            actions.append(action)
        for action in actions:
            self.content.remove(action)

    def _clear_single_text_block(self, clazz):
        for index in range(len(self.content)):
            action = self.content[index]
            if isinstance(action, clazz):
                self.content.remove(action)
                break

    def _is_step(self, action):
        return isinstance(action, Func) or isinstance(action, FOR)

    def _get_text_block(self, clazz):
        for text_block in self.content:
            if isinstance(text_block, clazz):
                return text_block
        return None

    def _clear_attrs(self):
        if hasattr(self, "arguments"):
            del self.arguments
        if hasattr(self, "documentation"):
            del self.documentation
        if hasattr(self, "timeout"):
            del self.timeout
        if hasattr(self, "teardown"):
            del self.teardown
        if hasattr(self, "return"):
            delattr(self, "return")
        if hasattr(self, "body"):
            del self.body


class KeyWords(Table):
    __slots__ = [
        "content",
        "_cells_list"
    ]

    def __init__(self):
        self.content = []
        self._cells_list = []

    def parse(self):
        latest_keyword = None
        for cells in self._cells_list:
            if self._is_new_keyword(cells[0]):
                keyword = KeyWord(cells[0])
                latest_keyword = keyword
                self.content.append(latest_keyword)
            if len(cells) != 1 and latest_keyword:
                latest_keyword.populate(cells[1:])
        return self.content

    def _is_new_keyword(self, cell):
        return cell.strip() != ""

    def add_child(self, content):
        name = content.get("name")
        if self._has_same_key_word(name):
            return "repeat"
        keyword = KeyWord(name)
        arguments = [argument.strip() for argument in content.get("arguments").split("|")]
        if not self._has_no_arguments(arguments):
            arguments.insert(0, Arguments.ACTION_HEAD)
            keyword.populate(arguments)
        keyword.path = content.get("path")
        self.content.append(keyword)
        KeyWordRepository().add(name, keyword)
        LocalKeyWordRepository().add(name, keyword)

    def _has_same_key_word(self, name):
        for key_word in self.content:
            if key_word.name == name:
                return True
        return False

    def _has_no_arguments(self, arguments):
        return len(arguments) == 1 and arguments[0] == ""

    def del_child(self, options):
        index = int(options.get("index"))
        KeyWordRepository().delete(self.content[index])
        LocalKeyWordRepository().delete(self.content[index])
        self.content.remove(self.content[index])


if __name__ == '__main__':
    print("[aswdadas]".strip('[|]'))
