# coding=utf8
'''
Created on 2019年10月18日

@author: 10140129
'''
import os

from iplatform.PluginRegistry import PluginRegistry
from iplatform.SandBox import SandBox
# from plugins.help.src.Main import Main
from settings.SystemSettings import SystemSettings, SYSTEM_ROOT_DIR


class Activator:

    @staticmethod
    def activate(plugin_id):
        plugin = Activator._get_plugin_obj(plugin_id)
        if not Activator._check_plugin_main(plugin_id,plugin.main):
            raise Exception("plugin main error")
        main = '.'.join(plugin.main.replace('/', '.').split('.')[:-1])
        cmdStr = 'from plugins.{}.{} import Main\nMain().activate()'.format(plugin_id, main)
        result = SandBox.execute(cmdStr, plugin_id)
        print(result)

    @staticmethod
    def _get_plugin_obj(plugin_id):
        return PluginRegistry().find(plugin_id)

    @staticmethod
    def _check_plugin_main(plugin_id,plugin_main):
        return True if os.path.exists("{}/plugins/{}/{}".format(SYSTEM_ROOT_DIR, plugin_id, plugin_main)) else False


if __name__ == "__main__":
    from iplatform.Core import Core
    Core()
    Activator.activate("demo")
#     str = "from plugins.demo.src.main import activate\nactivate()"
#     print(SandBox.execute(str, ''))
