# -*- coding: utf-8 -*-
"""
测试RobotHighlighter的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.Qsci import QsciScintilla
from iplatform.highlight.RobotHighlighter import RobotHighlighter


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RobotFramework语法高亮测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建编辑器
        self.editor = QsciScintilla()
        self.setCentralWidget(self.editor)
        
        # 设置语法高亮器
        self.highlighter = RobotHighlighter(self.editor)
        self.editor.setLexer(self.highlighter)
        
        # 设置编辑器属性
        self.editor.setUtf8(True)  # 支持UTF-8编码
        self.editor.setIndentationsUseTabs(False)
        self.editor.setIndentationWidth(4)
        self.editor.setAutoIndent(True)
        
        # 设置测试内容
        test_content = """*** Settings ***
Documentation    这是一个测试套件的说明文档
Library          SeleniumLibrary
Resource         ../resources/common.robot
Variables        ../variables/config.py

*** Variables ***
${用户名}        测试用户
${密码}          123456
@{列表变量}      项目1    项目2    项目3
&{字典变量}      key1=值1    key2=值2

*** Test Cases ***
测试用例_登录系统
    [Documentation]    测试登录功能
    [Tags]    冒烟测试    登录    中文标签
    #打开浏览器    https://example.com    chrome
    输入用户名    ${用户名}
    输入密码      ${密码}
    点击登录按钮
    验证登录成功
    [Teardown]    关闭浏览器

测试用例_数据验证
    [Tags]    数据测试
    ${结果}    执行查询    SELECT * FROM users WHERE id = 100
    log    查询结果：${结果}
    should be equal    ${结果}[0]    预期值
    ${数字}    set variable    12345
    ${文本}    set variable    "这是一个字符串"
    Comment    这是一个注释行
    
*** Keywords ***
打开浏览器
    [Arguments]    ${网址}    ${浏览器类型}=chrome
    [Documentation]    打开指定的浏览器并访问网址
    Open Browser    ${网址}    ${浏览器类型}
    Maximize Browser Window
    
输入用户名
    [Arguments]    ${用户名}
    Input Text    id=username    ${用户名}
    
输入密码
    [Arguments]    ${密码}
    Input Password    id=password    ${密码}
    
点击登录按钮
    Click Button    xpath=//button[@type='submit']
    
验证登录成功
    Wait Until Page Contains    欢迎您
    Page Should Contain    ${用户名}
    
关闭浏览器
    Close All Browsers
    
执行查询
    [Arguments]    ${查询语句}
    [Return]    ${查询结果}
    ${查询结果}    Query    ${查询语句}
"""
        
        self.editor.setText(test_content)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())
