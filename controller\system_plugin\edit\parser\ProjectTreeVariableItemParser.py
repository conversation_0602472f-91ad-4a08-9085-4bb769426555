# encoding=utf-8
'''
Created on 2019年11月25日

@author: 10247557
'''
INIT_FILE = 'init_file'
SETTINGS = 'settings'
VARIABLES = 'variables'

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from model.CurrentItem import CurrentItem
from utility.Singleton import Singleton


@Singleton
class ProjectTreeVariableItemParser(ItemParser):

    def query(self, item):
        self._item = item
        self._set_default()
        self._content = self._item._data_file.init_file if hasattr(self._item._data_file, INIT_FILE) else None
        self._get_variables()
        if self._content and hasattr(self._content, SETTINGS):
            self._settings = self._content.settings.query()
            self._get_documentation()
            self._get_suite_setup()
            self._get_suite_teardown()
            self._get_test_setup()
            self._get_test_teardomn()
            self._get_force_tags()
            self._get_imports()
            self._get_metadata()

    def get_cur_data_file(self, item):
        return item._data_file.init_file if hasattr(item._data_file, INIT_FILE) else None

    def _get_variables(self):
        self.variable_table = self._content.variables.query() if hasattr(self._content, VARIABLES) else None

    def modify(self, area_type, content):
        if self._content:
            self._content.modify_settings(area_type, content)
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), {'type': 'other', 'operator': 'modify'})
