# coding=utf-8
'''
Created on 2019年12月17日

@author: 10240349
'''
import sys

from PyQt5.Qt import Qt, QHBoxLayout, pyqtSignal, QCursor
from PyQt5.QtWidgets import QWidget, QLabel, QPushButton, \
    QVBoxLayout, QTableWidget, QHeaderView, QTableWidgetItem, \
    QApplication

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader


WIDTH = 600
HEIGHT = 310


class ConfigLibDialog(QWidget):

    ok_pressed = pyqtSignal(dict)

    def __init__(self, name):
        super().__init__()
        self._source_lib_name = name
        self._data = self._get_data_from_systemconfig()
        self._set_ui()
        self._init_ui()

    def _set_ui(self):
        self.setWindowTitle(LanguageLoader().get('CONFIG_LIB_PATH'))
        self.setWindowModality(Qt.ApplicationModal)
        self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

    def _init_ui(self):
        self.resize(WIDTH, HEIGHT)
        vbox = QVBoxLayout()
        for box in (self._set_label_area(), self._set_list_area(),
                    self._set_btn_area()):
            vbox.addLayout(box)
        self.setLayout(vbox)

    def _set_label_area(self):
        label = QLabel(LanguageLoader().get('ADD_SOURCE_LABEL'))
        hbox = QHBoxLayout()
        hbox.addWidget(label)
        return hbox

    def _set_list_area(self):
        v_layout = QVBoxLayout()
        self._table = QTableWidget()
        self._table.itemChanged.connect(self._change_rows)
        self._set_table_style(self._data)
        self._set_data_in_table(self._data)
        v_layout.addWidget(self._table)
        return v_layout

    def _set_table_style(self, data):
        self._table.setColumnCount(2)
        self._table.setRowCount(len(data) + 1)
        self._table.verticalHeader().setHidden(True)
        self._table.setHorizontalHeaderLabels(['库名', '路径'])
        self._table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    def _set_data_in_table(self, data):
        for row in range(len(data)):
            for column in range(len(data[row])):
                self._table.setItem(row, column, QTableWidgetItem(data[row][column]))

    def _set_btn_area(self, ok_btn_name = 'OK', cancel_btn_name = 'Cancel'):
        ok_btn = QPushButton(ok_btn_name, self)
        ok_btn.setCursor(QCursor(Qt.PointingHandCursor))
        cancel_btn = QPushButton(cancel_btn_name)
        cancel_btn.setCursor(QCursor(Qt.PointingHandCursor))
        ok_btn.setFixedSize(80, 25)
        ok_btn.setCheckable(True)
        ok_btn.toggle()
        cancel_btn.setFixedSize(80, 25)
        hbox = QHBoxLayout()
        hbox.addWidget(ok_btn, 0, Qt.AlignRight)
        hbox.addWidget(cancel_btn, 0, Qt.AlignLeft)
        ok_btn.clicked.connect(self._set_source_lib)
        cancel_btn.clicked.connect(self.close)
        return hbox

    def _set_source_lib(self):
        all_row = self._table.rowCount()
        source_lib = {}
        for row in range(all_row):
            if self._table.item(row, 0) and self._table.item(row, 1) and \
                    self._table.item(row, 0).text() and self._table.item(row, 1).text():
                source_lib[self._table.item(row, 0).text()] = self._table.item(row, 1).text()
        SystemSettings().write(self._source_lib_name, source_lib)
        self.ok_pressed.emit(source_lib)
        self.close()

    def _change_rows(self):
        select_value = self._table.selectedItems()
        if select_value:
            value = select_value[0].text()
            row = self._table.selectedIndexes()[0].row()
            all_row = self._table.rowCount()
            if value and row + 1 == all_row:
                self._table.insertRow(all_row)

    def _get_data_from_systemconfig(self):
        _list = []
        _dict = SystemSettings().read(self._source_lib_name)
        if _dict:
            for k, v in _dict.items():
                _list.append([k, v])
        return _list


def except_hook(cls, exception, traceback):
    sys.__excepthook__(cls, exception, traceback)


if __name__ == '__main__':
    import cgitb
    cgitb.enable(format = 'text')
    sys.excepthook = except_hook
    app = QApplication(sys.argv)
    ui = ConfigLibDialog('dialog')
    ui.show()
    sys.exit(app.exec_())
