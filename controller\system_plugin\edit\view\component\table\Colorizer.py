import re

from PyQt5.Qt import Qt, QFont
from PyQt5.QtGui import Q<PERSON>rush, QColor
from PyQt5.QtWidgets import QTableWidgetItem

from model.data_file.Repository import LocalKeyWordRepository, LocalVariableRepository
from model.data_file.Variables import Variable
from settings.UserSettings import UserSettings
from utility.Singleton import Singleton


@Singleton
class Colorizer(object):

    def __init__(self):
        self.init_settings()

    def _apply_cool_blue_theme_colors(self):
        """应用淡蓝色主题颜色配置"""
        return {
            'BACKGROUND': [255, 255, 255],   # 白色背景
            'TEXT': [51, 51, 51],           # 深灰色文字
            'KEYWORD': [0, 102, 204],       # 深蓝色关键字
            'VARIABLE': [0, 153, 51],       # 绿色变量
            'ERROR': [204, 0, 0],           # 红色错误
            'COMMENT': [153, 153, 153],     # 灰色注释
            'WEI_COMMENT': [153, 153, 153], # 灰色注释
            'ATTENTION': [255, 204, 0],     # 黄色注意
            'WARN': [255, 153, 0],          # 橙色警告
            'UNAVILIBLE': [153, 153, 153]   # 灰色不可用
        }

    def init_settings(self):
        # 确保ColorizationSettings使用最新的主题颜色
        colorization_settings = ColorizationSettings()
        colorization_settings.refresh_theme_colors()

        self._backgroundColor = colorization_settings.get_background_color()
        self._argsColor = colorization_settings.get_variable_color()
        self._errColor = colorization_settings.get_error_color()
        self._font = colorization_settings.get_font()
        self._size = colorization_settings.get_font_size()
        self._textColor = colorization_settings.get_text_color()
        self._highlightColor = colorization_settings.get_keyword_color()

        # 缓存其他颜色
        self._commentColor = colorization_settings.get_comment_color()
        self._weiCommentColor = colorization_settings.get_wei_comment_color()
        self._warnColor = colorization_settings.get_warn_color()
        self._attentionColor = colorization_settings.get_attention_color()

    def colorize(self, table, row, line):
        # 每次染色前刷新颜色设置，确保使用最新主题
        self.init_settings()

        # print(f"Colorizer.colorize被调用: row={row}, line={line}")  # 减少调试输出

        column = -1
        self._find_keyword(line)
        comment = False
        wei_comment = False
        line = self._format_line_content(line)
        for index in range(0, len(line)):
            column = column + 1
            item = QTableWidgetItem(line[index])
            if comment:
                item.setForeground(self._get_color("comment"))
            elif wei_comment:
                item.setForeground(self._get_color("wei_comment"))
            else:
                result = self._colorize(item, index)
                if result == "comment":
                    comment = True
                elif result == "wei_comment":
                    wei_comment = True
            item.setFont(QFont(self._font, int(self._size), QFont.Black))
            all_row = table._table.rowCount()
            all_column = table._table.columnCount()
            if row + 1 == all_row:
                table._table.insertRow(all_row)
            if column + 1 >= all_column:
                table._table.insertColumn(all_column-1)
            table._table.setItem(int(row), int(column), item)

    def _format_line_content(self, line):
        for index in self._keyword_args_indexs:
            if index > len(line) - 1:
                line.insert(index, '')
        return line

    def _check_unfilled_cell(self, table, row):
        for i in self._keyword_args_indexs:
            if table.item(row, i) and table.item(row, i).text():
                continue
            item = QTableWidgetItem("")
            item.setBackground(self._get_color("attention"))
            item.setFont(QFont(self._font, int(self._size), QFont.Black))
            all_row = table.rowCount()
            if i + 1 >= all_row:
                table.insertRow(all_row)
            table.setItem(int(row), int(i), item)

    def _colorize(self, item, index):
        text = item.text()
        # print(f"  _colorize: text='{text}', index={index}")  # 减少调试输出

        if text == "" and index in self._keyword_args_indexs:
            item.setBackground(self._get_color("attention"))
            item.setForeground(self._get_color("text"))
            # print(f"    -> 空白关键字参数: attention背景, text前景")
        elif text.startswith('#'):
            item.setForeground(self._get_color("wei_comment"))
            # print(f"    -> 注释: wei_comment前景")
            return 'wei_comment'
        elif text == "\\":
            item.setBackground(self._get_color("comment"))
            item.setForeground(self._get_color("comment"))
            # print(f"    -> 续行符: comment背景和前景")
        elif self._is_variable(text):
            if index < self._last_keyword_index:
                if not self._is_local_variable(text):
                    vaiable_name = re.match("^\$\{(.*)\}=?$", text).group(1)
                    LocalVariableRepository().add(vaiable_name, Variable(vaiable_name))
                item.setForeground(self._get_color("variable"))
                item.setBackground(self._get_color("background"))
                # print(f"    -> 变量: variable前景")
            elif index > self._last_keyword_index and self._is_local_variable(text):
                item.setForeground(self._get_color("variable"))
                item.setBackground(self._get_color("background"))
                # print(f"    -> 本地变量: variable前景")
                if index in self._keyword_args_indexs and text:
                    self._keyword_args_indexs.remove(index)
            else:
                item.setForeground(self._get_color("error"))
                item.setBackground(self._get_color("background"))
                # print(f"    -> 错误变量: error前景")
        elif self._is_keyword(text) or text.upper() == "END":
            # 将END关键字与其他关键字显示为相同的蓝色
            item.setForeground(self._get_color("keyword"))
            item.setBackground(self._get_color("background"))
            # print(f"    -> 关键字: keyword前景")
            if text.upper() == "COMMENT":
                return "comment"
        else:
            item.setForeground(self._get_color("text"))
            item.setBackground(self._get_color("background"))
            # print(f"    -> 普通文字: text前景")
        return True

    def _get_color(self, type):
        # 使用实例变量中缓存的颜色值，而不是每次都创建新的ColorizationSettings
        if type == "variable":
            colorTuple = self._argsColor
        elif type == "keyword":
            colorTuple = self._highlightColor
        elif type == "error":
            colorTuple = self._errColor
        elif type == "warn":
            colorTuple = self._warnColor
        elif type == "background":
            colorTuple = self._backgroundColor
        elif type == "comment":
            colorTuple = self._commentColor
        elif type == "wei_comment":
            colorTuple = self._weiCommentColor
        elif type == "attention":
            colorTuple = self._attentionColor
        else:
            colorTuple = self._textColor

        # print(f"    获取颜色 {type}: {colorTuple}")  # 减少调试输出
        return QColor(int(colorTuple[0]), int(colorTuple[1]), int(colorTuple[2]))

    def _find_keyword(self, line):
        self._last_keyword_index = -1
        self._keyword_args_indexs = []
        for i in range(len(line) - 1, -1, -1):
            if not line[i]:
                continue
            keyword_dicts = LocalKeyWordRepository().query(line[i])
            if keyword_dicts and keyword_dicts.get(line[i]) and self._last_keyword_index < 0:
                self._last_keyword_index = i
                self._apend_keyword_args_indexs(i, keyword_dicts.get(line[i])[-1].get("arguments"))
            elif keyword_dicts and keyword_dicts.get(line[i]):
                self._apend_keyword_args_indexs(i, keyword_dicts.get(line[i])[-1].get("arguments"))

    def _apend_keyword_args_indexs(self, index, args):
        args_count = 0
        for arg in args:
            if "=" in arg:
                break
            args_count += 1
        for j in range(args_count):
            self._keyword_args_indexs.append(index + j + 1)

    def _is_variable(self, cell):
        match = re.match("^\$\{(.*)\}=?$", cell)
        return True if match and not match.group(1).isdigit() and "${"  not in match.group(1) else False

    def _is_local_variable(self, cell):
        variable = re.match("^\$\{(.*)\}=?$", cell).group(1)
        local_variables = LocalVariableRepository().query(variable).get(variable)
        return True if local_variables else False

    def _is_keyword(self, cell):
        # 检查是否是本地关键字
        if LocalKeyWordRepository().query(cell).get(cell):
            return True

        # 检查是否是内置关键字
        from model.data_file.Repository import BuildInKeyWordRepository
        if BuildInKeyWordRepository().query(cell):
            return True

        # 检查是否是保留关键字（如FOR、END等）
        reserved_keywords = ['FOR', ':FOR', ': FOR','WHILE', 'BREAK', 'CONTINUE', 'END',
                            'IF', 'ELSE', 'ELIF', 'ELSE IF', 'RETURN', 'IN', 'IN RANGE',
                            'LOG', 'SLEEP', 'SHOULD BE EQUAL', 'GET TEXT', 'SET VARIABLE',
                            'COMMENT', 'ARGUMENTS', 'DOCUMENTATION', 'TAGS', 'SETUP', 'TEARDOWN']

        # 检查常见的RobotFramework关键字
        common_keywords = ['Log', 'Sleep', 'Should Be Equal', 'Get Text', 'Set Variable',
                          'Comment', '[Arguments]', '[Documentation]', '[Tags]', '[Setup]', '[Teardown]',
                          'Run Keyword', 'Run Keywords', 'Wait Until', 'Click Element', 'Input Text']

        return (cell.upper() in reserved_keywords or
                cell in common_keywords or
                cell.startswith('[') and cell.endswith(']'))

    def _coloring_task(self, selection_content, row=0, col=0):
        if self._grid:  # For example move from RIDE Log tab to Grid
            if row >= self._grid.NumberRows:
                self._grid.ForceRefresh()
            elif col < self._grid.NumberCols:
                self._colorize_cell(row, col, selection_content)
                self._coloring_task(selection_content, row, col + 1)
            else:
                self._coloring_task(selection_content, row + 1, 0)

    def _colorize_cell(self, row, col, selection_content):
        cell_info = self._controller.get_cell_info(row, col)
        if cell_info is None:
            self._set_default_colors(row, col)
            return
        self._grid.SetCellTextColour(row, col, self._get_text_color(cell_info))
        self._grid.SetCellBackgroundColour(row, col, self._get_background_color(cell_info, selection_content))
        self._grid.SetCellFont(row, col, self._get_cell_font(row, col, cell_info))

    def _set_default_colors(self, row, col):
        self._grid.SetCellTextColour(row, col, self._colors.get_text_color)
        self._grid.SetCellBackgroundColour(row, col, self._colors.get_background_color)

    def _get_text_color(self, cell_info):
        return self._colors.get_text_color(cell_info.content_type)

    def _get_background_color(self, cell_info, selection_content):
        if cell_info.matches(selection_content):
            return self._colors.get_highlight_color()
        if cell_info.has_error():
            return self._colors.get_error_color()
        return self._colors.get_background_color(cell_info.cell_type)

    def _get_cell_font(self, row, col, cell_info):
        font = self._grid.GetCellFont(row, col)
        font.SetWeight(self._get_weight(cell_info))
        return font


@Singleton
class ColorizationSettings(object):

    def __init__(self):
        self._theme_colors = self._get_theme_colors()

    def _get_theme_colors(self):
        """根据当前主题获取颜色配置"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()

            if current_theme == 'cool_blue':
                # 淡蓝色主题颜色配置
                return {
                    'BACKGROUND': [255, 255, 255],   # 白色背景
                    'TEXT': [51, 51, 51],           # 深灰色文字
                    'KEYWORD': [0, 102, 204],        # 深蓝色关键字
                    'VARIABLE': [0, 153, 51],        # 绿色变量
                    'ERROR': [204, 0, 0],            # 红色错误
                    'COMMENT': [153, 153, 153],      # 灰色注释
                    'WEI_COMMENT': [153, 153, 153],  # 灰色注释
                    'ATTENTION': [255, 204, 0],      # 黄色注意
                    'WARN': [255, 153, 0],           # 橙色警告
                    'UNAVILIBLE': [153, 153, 153]    # 灰色不可用
                }
            elif current_theme == 'eye_protect_green':
                # 墨绿色护眼主题颜色配置
                return {
                    'BACKGROUND': [27, 43, 27],      # 深墨绿色背景 #1B2B1B
                    'TEXT': [232, 245, 232],         # 浅绿白色文字 #E8F5E8
                    'KEYWORD': [129, 212, 250],      # 浅蓝色关键字 #81D4FA
                    'VARIABLE': [255, 171, 145],     # 浅橙色变量 #FFAB91
                    'ERROR': [255, 138, 101],        # 橙色错误 #FF8A65
                    'COMMENT': [165, 214, 167],      # 中等绿色注释 #A5D6A7
                    'WEI_COMMENT': [165, 214, 167],  # 中等绿色注释 #A5D6A7
                    'ATTENTION': [255, 213, 79],     # 金黄色注意 #FFD54F
                    'WARN': [255, 171, 145],         # 浅橙色警告 #FFAB91
                    'UNAVILIBLE': [129, 199, 132]    # 中绿色不可用 #81C784
                }
            elif theme_manager.is_dark_theme(current_theme):
                # 其他深色主题颜色配置
                return {
                    'BACKGROUND': [64, 64, 64],      # 深灰色背景
                    'TEXT': [224, 224, 224],         # 浅灰色文字
                    'KEYWORD': [74, 144, 226],       # 蓝色关键字 (库关键字)
                    'VARIABLE': [152, 195, 121],     # 绿色变量
                    'ERROR': [240, 113, 120],        # 红色错误
                    'COMMENT': [128, 128, 128],      # 灰色注释
                    'WEI_COMMENT': [128, 128, 128],  # 灰色注释
                    'ATTENTION': [255, 204, 102],    # 黄色注意
                    'WARN': [229, 192, 123],         # 黄色警告
                    'UNAVILIBLE': [128, 128, 128]    # 灰色不可用
                }
            else:
                # 浅色主题使用原有配置
                return {
                    'BACKGROUND': [255, 255, 255],   # 白色背景
                    'TEXT': [0, 0, 0],               # 黑色文字
                    'KEYWORD': [25, 105, 225],       # 蓝色关键字
                    'VARIABLE': [34, 139, 34],       # 绿色变量
                    'ERROR': [255, 0, 0],            # 红色错误
                    'COMMENT': [192, 192, 192],      # 灰色注释
                    'WEI_COMMENT': [192, 192, 192],  # 灰色注释
                    'ATTENTION': [255, 255, 0],      # 黄色注意
                    'WARN': [255, 165, 0],           # 橙色警告
                    'UNAVILIBLE': [128, 128, 128]    # 灰色不可用
                }
        except Exception as e:
            print(f"获取主题颜色失败，使用默认配置: {e}")
            # 出错时使用浅色主题配置
            return {
                'BACKGROUND': [255, 255, 255],
                'TEXT': [0, 0, 0],
                'KEYWORD': [25, 105, 225],
                'VARIABLE': [34, 139, 34],
                'ERROR': [255, 0, 0],
                'COMMENT': [192, 192, 192],
                'WEI_COMMENT': [192, 192, 192],
                'ATTENTION': [255, 255, 0],
                'WARN': [255, 165, 0],
                'UNAVILIBLE': [128, 128, 128]
            }

    def refresh_theme_colors(self):
        """刷新主题颜色（主题切换时调用）"""
        self._theme_colors = self._get_theme_colors()

    def get_background_color(self):
        # 每次获取颜色时都刷新主题颜色，确保使用最新主题
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('BACKGROUND', UserSettings().get_value("BACKGROUND"))

    def get_attention_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('ATTENTION', UserSettings().get_value("ATTENTION"))

    def get_unavailible_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('UNAVILIBLE', UserSettings().get_value("UNAVILIBLE"))

    def get_text_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('TEXT', UserSettings().get_value("TEXT"))

    def get_keyword_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('KEYWORD', UserSettings().get_value("KEYWORD"))

    def get_warn_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('WARN', UserSettings().get_value("WARN"))

    def get_error_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('ERROR', UserSettings().get_value("ERROR"))

    def get_variable_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('VARIABLE', UserSettings().get_value("VARIABLE"))

    def get_comment_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('COMMENT', UserSettings().get_value("COMMENT"))

    def get_wei_comment_color(self):
        self._theme_colors = self._get_theme_colors()
        return self._theme_colors.get('WEI_COMMENT', UserSettings().get_value("WEI_COMMENT"))

    def get_font(self):
        return UserSettings().get_value("FONT")

    def get_font_size(self):
        return UserSettings().get_value("TABLE_FONT_SIZE")


if __name__ == "__main__":
    print(ColorizationSettings().get_warn_color())
