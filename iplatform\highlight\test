import sys
from PyQt5.QtWidgets import (Q<PERSON><PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QFileDialog, QLabel, 
                             QStatusBar)
from PyQt5.Qsci import QsciScintilla
from PyQt5.QtCore import Qt
from PyQt5.Qsci import QsciLexerCustom
from PyQt5.QtGui import QColor, QFont

class RobotFrameworkViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("Robot Framework File Viewer")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create toolbar
        toolbar_layout = QHBoxLayout()
        
        self.open_button = QPushButton("Open File")
        self.open_button.clicked.connect(self.open_file)
        toolbar_layout.addWidget(self.open_button)
        
        self.file_label = QLabel("No file opened")
        self.file_label.setAlignment(Qt.AlignCenter)
        toolbar_layout.addWidget(self.file_label)
        
        layout.addLayout(toolbar_layout)
        
        # Create editor
        self.editor = QsciScintilla()
        self.setup_editor()
        layout.addWidget(self.editor)
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
    def setup_editor(self):
        """Configure the QScintilla editor with Robot Framework settings"""
        # Set lexer
        from RobotHighlighter import RobotHighlighter
        self.lexer = RobotHighlighter(self.editor)
        self.editor.setLexer(self.lexer)
        
        # Basic editor settings
        self.editor.setUtf8(True)
        self.editor.setAutoIndent(True)
        self.editor.setIndentationGuides(True)
        self.editor.setIndentationsUseTabs(False)
        self.editor.setIndentationWidth(4)
        self.editor.setTabWidth(4)
        self.editor.setEdgeMode(QsciScintilla.EdgeLine)
        self.editor.setEdgeColumn(120)
        self.editor.setEdgeColor(QColor(236, 236, 236))
        
        # Margin settings
        self.editor.setMarginType(0, QsciScintilla.NumberMargin)
        self.editor.setMarginWidth(0, "0000")
        self.editor.setMarginsForegroundColor(QColor(128, 128, 128))
        self.editor.setMarginsBackgroundColor(QColor(240, 240, 240))
        
        # Brace matching
        self.editor.setBraceMatching(QsciScintilla.SloppyBraceMatch)
        
        # Current line highlighting
        self.editor.setCaretLineVisible(True)
        self.editor.setCaretLineBackgroundColor(QColor(232, 242, 254))
        
    def open_file(self):
        """Open a file dialog and load the selected file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Robot Framework File",
            "",
            "Robot Framework Files (*.robot *.tsv);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.editor.setText(content)
                self.file_label.setText(file_path)
                self.status_bar.showMessage(f"Loaded: {file_path}", 3000)
                
                # Adjust lexer based on file extension
                if file_path.endswith('.tsv'):
                    self.lexer.set_tab_based(True)
                else:
                    self.lexer.set_tab_based(False)
                    
            except Exception as e:
                self.status_bar.showMessage(f"Error: {str(e)}", 5000)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application style (optional)
    app.setStyle('Fusion')
    
    # Create and show the main window
    viewer = RobotFrameworkViewer()
    viewer.show()
    
    sys.exit(app.exec_())