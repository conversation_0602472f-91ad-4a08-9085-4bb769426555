# coding=utf-8
'''
Created on 2019年11月8日

@author: 10247557
'''
import gc
import os
import webbrowser

from PyQt5.Qt import QIcon, QThread, pyqtSignal

from controller.test_runner.LogEditParser import WorkThread
from model.data_file.DataFile import SaveSequence
from resources.Loader import Loader
from robot.rebot import rebot
from settings.LogProject import LogProject
from settings.i18n.Loader import LanguageLoader
from utility.LogPathRepository import LogPathRepository
from utility.PluginRepository import PluginRepository
from utility.UIRepository import UIRepository
from utility.timer import get_date_time
from view.common.MessageBox import MessageBox


def check_and_fix_output_dir():
    """检查并修复输出目录问题"""
    output_dir = LogPathRepository().find('output_dir')

    if not output_dir:
        return False, "输出目录未设置"

    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir, exist_ok=True)
            return True, f"已重新创建输出目录: {output_dir}"
        except Exception as e:
            return False, f"无法创建输出目录: {str(e)}"

    # 检查写权限
    try:
        test_file = os.path.join(output_dir, 'test_write.tmp')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        return True, "输出目录正常"
    except Exception as e:
        return False, f"输出目录没有写权限: {str(e)}"


class BtnAction(object):

    def __init__(self):
        self._stop_pressed_count = 0

    def run(self, cmds, btn_list):
        self._stop_pressed_count = 0
        BtnAction._clear_log()
        self._wt = WorkThread()
        self._wt.set_cmds(cmds)
        self._wt.set_btn(btn_list)
        self._append_running_log('Testcases is running...\n')
        self._wt.new_summary_log.connect(BtnAction._append_summary_log)
        self._wt.new_trace_log.connect(BtnAction._append_trace_log)
        self._wt.new_progress_log.connect(BtnAction._update_progress_messge)
        self._wt.icon_status.connect(BtnAction._set_testcase_icon)
        self._wt.start()
        self.log_thread_stoped = True

    def pause(self, parent):
        self._set_btn_status_in_pause(parent)
        self._wt.new_summary_log.emit('[ SENDING PAUSE SIGNAL ]\n')
        self._wt._plugin.on_pause()

    def continue_(self, parent):
        parent._pause_btn.setEnabled(True)
        parent._stop_btn.setEnabled(True)
        parent._continue_btn.setEnabled(False)
        self._wt.new_summary_log.emit('[ SENDING CONTINUE SIGNAL ]\n')
        self._wt._plugin.on_continue()

    def stop(self, parent):
        self._wt.new_summary_log.emit('[ SENDING STOP SIGNAL ]\n')
        self._wt._plugin.on_stop()
        self._stop_pressed_count += 1
        if self._stop_pressed_count >= 2:
            if not PluginRepository().find('SHOW_REAL_TIME_LOG'):
                log_btn_gray = True
                self._wt.set_log_btn_status(log_btn_gray)
                log_btn_gray = False
            self._set_btn_status_in_two_stop(parent)
            self._write_file(need_open_log=False)

    def next(self):
        self._wt.new_summary_log.emit('[ SENDING STEP NEXT SIGNAL ]\n')
        self._wt._plugin.on_stop()

    def over(self):
        self._wt.new_summary_log.emit('[ SENDING STEP OVER SIGNAL ]\n')
        self._wt._plugin.on_stop()

    def get_log(self):
        if self._wt.isRunning():
            self._write_file(need_open_log=True)
        else:
            # 检查并修复输出目录问题
            is_ok, message = check_and_fix_output_dir()
            if not is_ok:
                MessageBox().show_information(f"输出目录问题：{message}\n请重新运行用例")
                return

            output_dir = LogPathRepository().find('output_dir')
            log_file_path = os.path.join(output_dir, 'log.html')

            # 检查日志文件是否存在
            if os.path.exists(log_file_path):
                try:
                    webbrowser.open(log_file_path)
                except Exception as e:
                    MessageBox().show_information(f"无法打开日志文件：{str(e)}")
            else:
                # 尝试查找其他可能的日志文件
                alternative_files = []
                try:
                    for file in os.listdir(output_dir):
                        if file.endswith('.html') and ('log' in file.lower() or 'report' in file.lower()):
                            alternative_files.append(file)
                except OSError:
                    pass

                if alternative_files:
                    # 如果找到其他HTML文件，尝试打开第一个
                    try:
                        webbrowser.open(os.path.join(output_dir, alternative_files[0]))
                        return
                    except Exception:
                        pass

                # 提供详细的错误信息和诊断
                error_msg = "日志文件生成失败，请重跑用例\n\n"
                error_msg += "=== 问题诊断 ===\n"
                error_msg += f"📁 输出目录: {output_dir}\n"
                error_msg += f"🎯 期望日志文件: {log_file_path}\n"

                # 检查目录状态
                if os.path.exists(output_dir):
                    error_msg += "✅ 输出目录: 存在\n"

                    # 检查目录中的文件
                    try:
                        files_in_dir = os.listdir(output_dir)
                        if files_in_dir:
                            html_files = [f for f in files_in_dir if f.endswith('.html')]
                            xml_files = [f for f in files_in_dir if f.endswith('.xml')]

                            error_msg += f"📄 目录文件数: {len(files_in_dir)}\n"
                            if html_files:
                                error_msg += f"🌐 HTML文件: {', '.join(html_files)}\n"
                            else:
                                error_msg += "❌ HTML文件: 无\n"
                            if xml_files:
                                error_msg += f"📋 XML文件: {', '.join(xml_files)}\n"
                        else:
                            error_msg += "❌ 输出目录: 为空\n"
                    except OSError as e:
                        error_msg += f"❌ 目录读取失败: {str(e)}\n"
                else:
                    error_msg += "❌ 输出目录: 不存在\n"

                # 检查Robot Framework
                try:
                    from robot.rebot import rebot  # noqa: F401
                    error_msg += "✅ Robot Framework: 可用\n"
                except ImportError:
                    error_msg += "❌ Robot Framework: 导入失败\n"

                error_msg += "\n=== 解决建议 ===\n"
                error_msg += "1. 重新运行用例（最常见解决方案）\n"
                error_msg += "2. 确保用例完全执行结束后再点击日志\n"
                error_msg += "3. 检查磁盘空间是否充足\n"
                error_msg += "4. 检查防病毒软件是否阻止文件生成\n"
                error_msg += "5. 尝试以管理员身份运行RFCode\n"

                # 尝试保存诊断信息到文件
                try:
                    diagnostic_file = os.path.join(output_dir, 'log_generation_diagnostic.txt')
                    with open(diagnostic_file, 'w', encoding='utf-8') as f:
                        f.write(error_msg)
                        f.write(f"\n\n诊断时间: {get_date_time()}")
                    error_msg += f"\n\n📋 诊断信息已保存到: {diagnostic_file}"
                except Exception:
                    pass

                MessageBox().show_information(error_msg)

    def _write_file(self, need_open_log=True):
        if PluginRepository().find('SHOW_REAL_TIME_LOG') and self.log_thread_stoped:
            self.log_thread_stoped = False
            self._realtime_log_thread = RealtimeLogThread(self, need_open_log)
            self._realtime_log_thread.start()
            log_label = PluginRepository().find('KEYWORD_PROGRESS')
            log_label.setText('实时日志生成中，请稍等...')
            self._realtime_log_thread.log_finished.connect(self._set_log_label)
            status_bar = PluginRepository().find('STATUS_BAR')
            status_bar.addPermanentWidget(log_label)

    def _set_log_label(self, text):
        log_label = PluginRepository().find('KEYWORD_PROGRESS')
        log_label.setText(text)

    @staticmethod
    def _clear_log():
        log_trace_editor = UIRepository().find('log_trace_editor')
        summary_log_editor = UIRepository().find('log_summary_editor')
        progress_editor = UIRepository().find('progress_editor')
        progress_editor.clear()
        summary_log_editor.clear()
        log_trace_editor.clear()

        # 根据主题设置progress_editor样式
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if theme_manager.is_dark_theme():
                progress_editor.setStyleSheet("background-color: #404040; color: #E0E0E0; border-width:0; border-style:outset")
            else:
                progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")
        except Exception as e:
            print(f"设置progress_editor主题样式失败: {e}")
            progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")

    @staticmethod
    def _update_progress_messge(message, plugin):
        progress_editor = UIRepository().find('progress_editor')
        progress_editor.setText(message)
        pass_num = plugin.progress_bar._pass
        fail_num = plugin.progress_bar._fail

        # 根据主题和执行状态设置样式
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()

            if pass_num > 0 and fail_num == 0:
                # 成功状态 - 绿色背景
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #2E7D32; color: #E8F5E8; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(155,213,139)")
            elif fail_num > 0:
                # 失败状态 - 红色背景
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #C62828; color: #FFEBEE; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(242,125,124)")
            else:
                # 默认状态
                if theme_manager.is_dark_theme():
                    progress_editor.setStyleSheet("background-color: #404040; color: #E0E0E0; border-width:0; border-style:outset")
                else:
                    progress_editor.setStyleSheet("background:rgb(225,225,225);border-width:0;border-style:outset")
        except Exception as e:
            print(f"设置progress_editor执行状态样式失败: {e}")
            # 回退到原始样式
            if pass_num > 0 and fail_num == 0:
                progress_editor.setStyleSheet("background:rgb(155,213,139)")
            elif fail_num > 0:
                progress_editor.setStyleSheet("background:rgb(242,125,124)")

    @staticmethod
    def _append_trace_log(log):
        log_trace_editor = UIRepository().find('log_trace_editor')
        log_trace_editor.append(log[:1000000])

    def _append_running_log(self, log):
        summary_log_editor = UIRepository().find('log_summary_editor')
        summary_log_editor.append(log)

    @staticmethod
    def _append_summary_log(log):
        summary_log_editor = UIRepository().find('log_summary_editor')
        summary_log_editor.append(log)

    @staticmethod
    def _set_testcase_icon(testcase, status):
        if status == 'Running':
            testcase.setIcon(0, QIcon(Loader().get_path('RUNNING')))
        elif status == 'Failed':
            testcase.setIcon(0, QIcon(Loader().get_path('FAILED')))
        elif status == 'Passed':
            testcase.setIcon(0, QIcon(Loader().get_path('PASSED')))

    def _set_btn_status_in_two_stop(self, parent):
        parent._start_btn.setEnabled(True)
        parent._pause_btn.setEnabled(False)
        parent._stop_btn.setEnabled(False)
        parent._continue_btn.setEnabled(False)
        parent._log_btn.setEnabled(True)
        PluginRepository().update('TESTCASE_RUNNING', False)
        SaveSequence.save_all()

    def _set_btn_status_in_pause(self, parent):
        parent._start_btn.setEnabled(False)
        parent._pause_btn.setEnabled(False)
        parent._stop_btn.setEnabled(True)
        parent._continue_btn.setEnabled(True)
        parent._log_btn.setEnabled(True)


class RealtimeLogThread(QThread):

    log_finished = pyqtSignal(str)

    def __init__(self, action, need_open_log):
        super().__init__()
        self._action = action
        self._need_open_log = need_open_log

    def run(self):
        path = LogPathRepository().find('output_dir')

        try:
            # 检查输出目录是否存在
            if not path or not os.path.exists(path):
                self.log_finished.emit('输出目录不存在，日志生成失败')
                self._action.log_thread_stoped = True
                return

            # 生成XML文件
            try:
                self._action._wt.write_xml()
            except Exception as e:
                self.log_finished.emit(f'XML文件生成失败：{str(e)}')
                self._action.log_thread_stoped = True
                return

            xml_file = os.path.join(path, 'out.xml')
            log_file = os.path.join(path, 'log.html')

            # 检查XML文件是否生成成功
            if not os.path.exists(xml_file):
                self.log_finished.emit('XML文件未生成，日志生成失败')
                self._action.log_thread_stoped = True
                return

            # 检查XML文件是否为空或过小
            try:
                xml_size = os.path.getsize(xml_file)
                if xml_size < 100:  # XML文件太小，可能不完整
                    self.log_finished.emit(f'XML文件过小({xml_size}字节)，可能不完整')
                    self._action.log_thread_stoped = True
                    return
            except OSError:
                self.log_finished.emit('无法读取XML文件大小')
                self._action.log_thread_stoped = True
                return

            # 使用rebot生成日志
            try:
                rebot(xml_file, outputdir=path, logtitle='实时日志%s' % get_date_time()[:-4])
            except Exception as e:
                self.log_finished.emit(f'rebot执行失败：{str(e)}')
                self._action.log_thread_stoped = True
                return

            # 检查日志文件是否生成成功
            if os.path.exists(log_file):
                self.log_finished.emit('实时日志生成完成')
                if self._need_open_log:
                    try:
                        webbrowser.open(log_file)
                    except Exception as e:
                        self.log_finished.emit(f'日志文件打开失败：{str(e)}')
            else:
                # 检查是否有其他HTML文件生成
                html_files = []
                try:
                    for file in os.listdir(path):
                        if file.endswith('.html'):
                            html_files.append(file)
                except OSError:
                    pass

                if html_files:
                    self.log_finished.emit(f'找到HTML文件：{", ".join(html_files)}')
                    if self._need_open_log:
                        try:
                            webbrowser.open(os.path.join(path, html_files[0]))
                        except Exception:
                            pass
                else:
                    self.log_finished.emit('日志HTML文件未生成')

        except Exception as e:
            self.log_finished.emit(f'日志生成过程出现异常：{str(e)}')
        finally:
            self._action.log_thread_stoped = True
            # 清理临时XML文件
            try:
                xml_file = os.path.join(path, 'out.xml')
                if os.path.exists(xml_file):
                    os.remove(xml_file)
            except Exception:
                pass
            gc.collect()
