# coding=utf-8
'''
Created on 2019年11月25日

@author: 10240349
'''
# coding=utf-8
'''
Created on 2019年10月23日

@author: 10240349
'''

from _functools import partial
import codecs
import os

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QAction, QActionGroup

from settings.i18n.Loader import LanguageLoader
from controller.system_plugin.style.ThemeManager import ThemeManager
from utility.PluginRepository import PluginRepository


class StylePlugin(object):

    def __init__(self, parent):
        self._parent_window = parent
        self._theme_actions = {}
        self._action_group = None
        self._theme_manager = ThemeManager()

    def load(self):
        style_menu = self._parent_window.addMenu('&' + LanguageLoader().get('STYLE'))
        self._create_theme_menu(style_menu)
        self._load_saved_theme()

    def _create_theme_menu(self, style_menu):
        """创建主题菜单"""
        # 创建动作组，确保只能选择一个主题
        self._action_group = QActionGroup(self._parent_window)
        self._action_group.setExclusive(True)

        # 获取主题列表
        themes = self._theme_manager.get_themes()

        for theme_id, theme_info in themes.items():
            action = QAction(QIcon(''), theme_info['name'], self._parent_window)
            action.setCheckable(True)
            action.setToolTip(theme_info['description'])
            action.triggered.connect(partial(self._set_theme, theme_id))

            self._action_group.addAction(action)
            style_menu.addAction(action)
            self._theme_actions[theme_id] = action

    def _set_theme(self, theme_id):
        """设置主题"""
        # 使用主题管理器设置主题
        if self._theme_manager.set_theme(theme_id):
            # 更新菜单选中状态
            if theme_id in self._theme_actions:
                self._theme_actions[theme_id].setChecked(True)

    def _load_saved_theme(self):
        """加载保存的主题设置"""
        try:
            # 获取当前主题
            current_theme = self._theme_manager.get_current_theme()

            # 设置对应的菜单项为选中状态
            if current_theme in self._theme_actions:
                self._theme_actions[current_theme].setChecked(True)

            # 应用保存的主题
            self._theme_manager.apply_saved_theme()

        except Exception as e:
            print(f"加载主题设置时出错: {e}")
            # 出错时使用默认主题
            if 'light' in self._theme_actions:
                self._theme_actions['light'].setChecked(True)
            self._theme_manager.set_theme('light')

    def get_current_theme(self):
        """获取当前主题"""
        return self._theme_manager.get_current_theme()

    def get_theme_manager(self):
        """获取主题管理器实例"""
        return self._theme_manager
