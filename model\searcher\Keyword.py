'''
Created on 2019年12月18日

@author: 10243352
'''
import os

from robot.libraries import STDLIBS

from controller.parser.reader.ReaderFactory import ReaderFactory
from model.data_file.Repository import GlobalBuildInKeyWordRepository
from model.data_file.RfStdLibs import RfStdLibs
from model.data_file.env import SUPPORT_SUITE_FORMAT
from model.searcher.TestCase import TestCase
from model.searcher.repositories.KeywordsRepository import KeywordsRepository
from utility.log.SystemLogger import logger


class Keyword(TestCase):
    CACHE = {}

    def __init__(self, path, file_counter):
        self._clear()
        self._init_global_build_in_repo()
        self._path = os.path.abspath(path.rstrip(os.path.sep)) if path != "" else ""
        if self._verify_path():
            return
        for root, _, files in os.walk(path):
            for file in files:
                file_counter.add_number_of_analyzed_files(1)
                file_path = os.path.abspath(os.path.join(root, file))
                if file_path in Keyword.CACHE:
                    if self._get_record_modify_time(file_path) == self._get_latest_modify_time(file_path):
                        continue
                Keyword.CACHE[file_path] = self._get_latest_modify_time(file_path)
                KeywordsRepository().delete(file_path)
                if os.path.splitext(file_path)[-1] not in SUPPORT_SUITE_FORMAT:
                    continue
                self._store_keywords(file_path)
        file_counter.set_analyzed_finished()

    def search(self, key):
        results = []
        if self._verify_path() and self._path != "":
            return results
        if self._path != "":
            for path in KeywordsRepository().keys():
                names = KeywordsRepository().find(path)
                match_names = filter(lambda testcase_name: key in testcase_name and path.startswith(os.path.abspath(self._path + os.path.sep)), names)
                sub_results = [[name, path, ""] for name in match_names]
                results.extend(sub_results)
        else:
            results = GlobalBuildInKeyWordRepository().fuzzy_query(key)
            results = [[name, results[name][-1]["path"], results[name][-1]["documentation"]] for name in results]
        return results

    def _init_global_build_in_repo(self):
        if GlobalBuildInKeyWordRepository().is_init:
            return
        for build_in_lib in STDLIBS:
            RfStdLibs(GlobalBuildInKeyWordRepository()).parse(build_in_lib)
        GlobalBuildInKeyWordRepository().is_init = True

    def _store_keywords(self, file_path):
        with open(file_path, "rb") as file_handler:
            begin_record = False
            line = file_handler.readline()
            encode_type = None
            while(line):
                line = file_handler.readline()
                try:
                    encode_type = self._get_encode_type(line, encode_type)
                    line = line.decode(encoding=encode_type)
                except Exception as _:
                    logger.info("decode fail\ndecode file is: %s.\ndecode line is: %s." % (file_path, line))
                    continue
                tag = line.rstrip().strip("*").lower()
                if not begin_record and line.startswith('*') and tag.strip() in ["keywords", "keyword"]:
                    begin_record = True
                    continue
                if begin_record and line.startswith('*') and tag.strip() in ["settings", "variables", "test case", "testcase", "testcases", "test cases"]:
                    break
                if not begin_record:
                    continue
                self._store_keywrod(file_path, line)

    def _store_keywrod(self, file_path, line_content):
        cells = line_content.split(ReaderFactory.get_instance(file_path).CELL_SEPERATOR)
        first_cell = cells[0].strip()
        if first_cell != "" and not first_cell.startswith("#") and first_cell.lower() != "comment":
            KeywordsRepository().add(file_path, cells[0])

    def _get_record_modify_time(self, path):
        return Keyword.CACHE[path]

if __name__ == '__main__':
    import time
    print(Keyword(r"", "").search("comment"))
#     print(Keyword(r"").search("slEEp"))
    time01 = time.time()
    print(Keyword(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test", "").search("小区"))
    time02 = time.time()
    print(time02 - time01)
    print(Keyword(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test", "").search("小区"))
    time03 = time.time()
    print(time03 - time02)
