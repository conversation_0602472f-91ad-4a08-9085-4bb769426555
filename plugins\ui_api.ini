[General]
MENU_BAR="\x7cfb\x7edf\x4e3b\x7a97\x53e3\x7684\x83dc\x5355\x680f\xff0c\x652f\x6301\x5bf9\x83dc\x5355\x680f\x7684\x5b9a\x5236:DrawChildren,DrawWindowBackground,IgnoreMask,PaintDeviceMetric,PdmDepth,PdmDevicePixelRatio,PdmDevicePixelRatioScaled,PdmDpiX,PdmDpiY,PdmHeight,PdmHeightMM,PdmNumColors,PdmPhysicalDpiX,PdmPhysicalDpiY,PdmWidth,PdmWidthMM,RenderFlag,RenderFlags,acceptDrops,accessibleDescription,accessibleName,actionAt,actionEvent,actionGeometry,actions,activateWindow,activeAction,addAction,addActions,addMenu,addSeparator,adjustSize,autoFillBackground,backgroundRole,baseSize,blockSignals,changeEvent,childAt,childEvent,children,childrenRect,childrenRegion,clear,clearFocus,clearMask,close,closeEvent,colorCount,connectNotify,contentsMargins,contentsRect,contextMenuEvent,contextMenuPolicy,cornerWidget,create,createWindowContainer,cursor,customContextMenuRequested,customEvent,deleteLater,depth,destroy,destroyed,devType,devicePixelRatio,devicePixelRatioF,devicePixelRatioFScale,disconnect,disconnectNotify,dragEnterEvent,dragLeaveEvent,dragMoveEvent,dropEvent,dumpObjectInfo,dumpObjectTree,dynamicPropertyNames,effectiveWinId,ensurePolished,enterEvent,event,eventFilter,find,findChild,findChildren,focusInEvent,focusNextChild,focusNextPrevChild,focusOutEvent,focusPolicy,focusPreviousChild,focusProxy,focusWidget,font,fontInfo,fontMetrics,foregroundRole,frameGeometry,frameSize,geometry,getContentsMargins,grab,grabGesture,grabKeyboard,grabMouse,grabShortcut,graphicsEffect,graphicsProxyWidget,hasFocus,hasHeightForWidth,hasMouseTracking,hasTabletTracking,height,heightForWidth,heightMM,hide,hideEvent,hovered,inherits,initPainter,initStyleOption,inputMethodEvent,inputMethodHints,inputMethodQuery,insertAction,insertActions,insertMenu,insertSeparator,installEventFilter,isActiveWindow,isAncestorOf,isDefaultUp,isEnabled,isEnabledTo,isFullScreen,isHidden,isLeftToRight,isMaximized,isMinimized,isModal,isNativeMenuBar,isRightToLeft,isSignalConnected,isVisible,isVisibleTo,isWidgetType,isWindow,isWindowModified,isWindowType,keyPressEvent,keyReleaseEvent,keyboardGrabber,killTimer,layout,layoutDirection,leaveEvent,locale,logicalDpiX,logicalDpiY,lower,mapFrom,mapFromGlobal,mapFromParent,mapTo,mapToGlobal,mapToParent,mask,maximumHeight,maximumSize,maximumWidth,metaObject,metric,minimumHeight,minimumSize,minimumSizeHint,minimumWidth,mouseDoubleClickEvent,mouseGrabber,mouseMoveEvent,mousePressEvent,mouseReleaseEvent,move,moveEvent,moveToThread,nativeEvent,nativeParentWidget,nextInFocusChain,normalGeometry,objectName,objectNameChanged,overrideWindowFlags,overrideWindowState,paintEngine,paintEvent,paintingActive,palette,parent,parentWidget,physicalDpiX,physicalDpiY,pos,previousInFocusChain,property,pyqtConfigure,raise_,receivers,rect,releaseKeyboard,releaseMouse,releaseShortcut,removeAction,removeEventFilter,render,repaint,resize,resizeEvent,restoreGeometry,saveGeometry,screen,scroll,sender,senderSignalIndex,setAcceptDrops,setAccessibleDescription,setAccessibleName,setActiveAction,setAttribute,setAutoFillBackground,setBackgroundRole,setBaseSize,setContentsMargins,setContextMenuPolicy,setCornerWidget,setCursor,setDefaultUp,setDisabled,setEnabled,setFixedHeight,setFixedSize,setFixedWidth,setFocus,setFocusPolicy,setFocusProxy,setFont,setForegroundRole,setGeometry,setGraphicsEffect,setHidden,setInputMethodHints,setLayout,setLayoutDirection,setLocale,setMask,setMaximumHeight,setMaximumSize,setMaximumWidth,setMinimumHeight,setMinimumSize,setMinimumWidth,setMouseTracking,setNativeMenuBar,setObjectName,setPalette,setParent,setProperty,setShortcutAutoRepeat,setShortcutEnabled,setSizeIncrement,setSizePolicy,setStatusTip,setStyle,setStyleSheet,setTabOrder,setTabletTracking,setToolTip,setToolTipDuration,setUpdatesEnabled,setVisible,setWhatsThis,setWindowFilePath,setWindowFlag,setWindowFlags,setWindowIcon,setWindowIconText,setWindowModality,setWindowModified,setWindowOpacity,setWindowRole,setWindowState,setWindowTitle,sharedPainter,show,showEvent,showFullScreen,showMaximized,showMinimized,showNormal,signalsBlocked,size,sizeHint,sizeIncrement,sizePolicy,stackUnder,startTimer,staticMetaObject,statusTip,style,styleSheet,tabletEvent,testAttribute,thread,timerEvent,toolTip,toolTipDuration,tr,triggered,underMouse,ungrabGesture,unsetCursor,unsetLayoutDirection,unsetLocale,update,updateGeometry,updateMicroFocus,updatesEnabled,visibleRegion,whatsThis,wheelEvent,width,widthMM,winId,window,windowFilePath,windowFlags,windowHandle,windowIcon,windowIconChanged,windowIconText,windowIconTextChanged,windowModality,windowOpacity,windowRole,windowState,windowTitle,windowTitleChanged,windowType"
