'''
Created on 2019年10月21日

@author: 10243352
'''


class Tables(object):

    def __init__(self):
        self._tables = []

    def parse(self):
        for table in self._tables:
            table.parse()
            table.clear()
        return self._tables

    def append(self, table):
        self._tables.append(table)


class Table(object):

    def __init__(self):
        self._cells_list = []

    @property
    def class_name(self):
        return self.__class__.__name__

    def add(self, cells):
        self._cells_list.append(cells)

    def parse(self):
        raise NotImplementedError()

    def clear(self):
        self._cells_list = []

    def del_child(self, options):
        index = int(options.get("index"))
        self.content.remove(self.content[index])

    def add_child(self, content):
        raise NotImplementedError()