# coding=utf-8
'''
Created on 2019年12月17日

@author: 10240349
'''
import os
import sys

from PyQt5.Qt import Qt, QIcon, QThread, pyqtSignal, QCursor
from PyQt5.QtWidgets import QH<PERSON><PERSON><PERSON><PERSON><PERSON>, QLabel, QPushButton, \
    QApplication, QVBoxLayout, QTextEdit, QWidget, QComboBox, \
    QTableWidget, QTableWidgetItem, QAbstractItemView, \
    QHeaderView

from controller.system_plugin.tools.search_dialog.ConfigLibDialog import ConfigLibDialog
from controller.system_plugin.tools.search_dialog.LoadingBar import KeywordsLoadingBar
from controller.system_plugin.tools.search_dialog.MyLineEdit import MyLineEdit
from model.searcher.Keyword import Keyword
from model.searcher.SearchRepoInitor import SearchRepoInitThread
from resources.Loader import Loader
from settings.HistoryProject import HistoryProject
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from utility.FileHandler import Keywords<PERSON>ile<PERSON>ounter
from view.common.MessageBox import <PERSON>Box
from view.explorer.tree_item.SpecifiedKeywordJumper import SpecifiedKeywordJumper


SEARCH_LINE_WIDTH = 300
SEARCH_BTN = 100
TESTCASE_HEADER_WIDTH = 300


class Keywords(QWidget):
    thread_obj = None
    loading_bar_obj = None

    def __init__(self, title, logo_path):
        super().__init__()
        self._set_ui(title, logo_path)
        self._init_ui()
        self._search_repo_init_thread = SearchRepoInitThread(None)

    def _set_ui(self, title, logo_path):
        self._first_column_width = TESTCASE_HEADER_WIDTH
        self.setWindowTitle(title)
        self.setWindowIcon(QIcon(logo_path))
#         self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

    def _init_ui(self):
        self.resize(SystemSettings().get_value('SEARCH_KEYWORDS_DIALOG_WIDTH'),
                    SystemSettings().get_value('SEARCH_KEYWORDS_DIALOG_HEIGHT'))
        vbox = QVBoxLayout()
        for box in (self._set_line_area(),
                    self._set_list_area(), self._set_text_area()):
            vbox.addLayout(box)
        self.setLayout(vbox)
        self._focus_cursor()

    def _focus_cursor(self):
        self._search_line.setCursorPosition(2)
        self._search_line.setFocus()

    def _set_line_area(self):
        hbox = QHBoxLayout()
        hbox = self._set_widget(hbox)
        hbox.setSpacing(10)
        hbox.addStretch() # 最后一个控件添加伸缩，就会左对齐
        return hbox

    def _set_widget(self, hbox):
        self._search_line = self._set_search_line()
        self._search_btn = self._set_search_btn()
        _label = self._set_source_label()
        self._keyword_lib = self._set_combox()
        config_btn = self._set_config_btn()
        for c in (self._search_line, self._search_btn, _label, self._keyword_lib, config_btn):
            hbox.addWidget(c)
        return hbox

    def _set_search_btn_status(self, flag):
        self._search_btn.setEnabled(flag)

    def _set_search_line(self):
        line = MyLineEdit()
        line.enter_pressed.connect(self._search_keywords)
        line.setMaximumWidth(SEARCH_LINE_WIDTH)
        return line

    def _set_search_btn(self):
        search_btn = QPushButton('Search')
        search_btn.setCursor(QCursor(Qt.PointingHandCursor))
        search_btn.setMaximumWidth(SEARCH_BTN)
        search_btn.clicked.connect(self._search_keywords)
        return search_btn

    def _set_source_label(self):
        label = QLabel('Source: ')
        return label

    def _set_combox(self):
        self._combox = QComboBox(self)
        if HistoryProject.read('PROJECT_PATH'):
            self._source_lib = ['Userkeywords', 'Builtin']
        else:
            self._source_lib = ['Builtin']
        _dict = SystemSettings().read("SOURCE_KEYWORDS_LIB")
        if _dict:
            self._source_lib += _dict.keys()
        self._combox.addItems(self._source_lib)
        return self._combox

    def _set_config_btn(self):
        config_btn = QPushButton('Configure')
        config_btn.setCursor(QCursor(Qt.PointingHandCursor))
        config_btn.clicked.connect(self._open_config_dialog)
        return config_btn

    def _set_list_area(self):
        v_layout = QVBoxLayout()
        self._table = QTableWidget()
        self._keywords_data = []
        self._set_table_style(len(self._keywords_data))
        self._set_data_in_table(self._keywords_data)
        self._table.itemDoubleClicked.connect(self._check_item)
        v_layout.addWidget(self._table)
        return v_layout

    def _set_table_style(self, length):
        self._table.setColumnCount(2)
        self._table.setRowCount(length)
        self._table.verticalHeader().setHidden(True)
        self._table.setColumnWidth(0, TESTCASE_HEADER_WIDTH)
        self._table.setHorizontalHeaderLabels([LanguageLoader().get('KEYWORD_NAME'),
                                               LanguageLoader().get('LIB_PATH')])
        self._table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self._table.setSelectionMode(QAbstractItemView.SingleSelection)
        self._table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self._table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Interactive)
        self._table.setColumnWidth(0, TESTCASE_HEADER_WIDTH)
        self._table.setEditTriggers(QAbstractItemView.NoEditTriggers)

    def _set_data_in_table(self, data):
        for row in range(len(data)):
            for column in range(len(data[row])):
                self._table.setItem(row, column, QTableWidgetItem(data[row][column]))

    def _set_text_area(self):
        v_layout = QVBoxLayout()
        self._keyword_des = QTextEdit()
        v_layout.addWidget(self._keyword_des)
        return v_layout

    def _set_style(self, text_des):
        text_des.setLineWrapMode(QTextEdit.NoWrap)
        text_des.setStyleSheet("background:transparent;border-width:0;border-style:outset")
        text_des.setFontFamily(SystemSettings().get_value('FONT'))

    def _search_keywords(self):
        self._keyword_des.setText('')
        self._first_column_width = self._table.columnWidth(0)
        _dict = SystemSettings().read("SOURCE_KEYWORDS_LIB")
        if self._combox.currentText() == 'Userkeywords' and not self._check_rfcode_file('USERKEYWORDS'):
            MessageBox().show_warning(LanguageLoader().get('SYSTEM_LIB_TIP'))
        else:
            self._kill_old_thread()
            self._dt = SearchThread(_dict, self._combox.currentText(), self._search_line.text())
            Keywords.thread_obj = self._dt
            self._kill_old_loading_bar()
            self.loading_bar = KeywordsLoadingBar(self._dt.get_file_counter())
            Keywords.loading_bar_obj = self.loading_bar
            self._dt.search_finished.connect(self._reload_keywords)
            self._dt.start()
            self.loading_bar.run()

    def _kill_old_thread(self):
        if Keywords.thread_obj:
            Keywords.thread_obj.terminate()

    def _kill_old_loading_bar(self):
        if Keywords.loading_bar_obj:
            Keywords.loading_bar_obj = None

    def _reload_keywords(self, _list):
        self.loading_bar.destroy()
        if not _list:
            MessageBox().show_warning(LanguageLoader().get('SEARCH_RESULT'))
        self._keywords_data = _list
        self._set_table_style(len(self._keywords_data))
        self._table.clearContents()
        self._table.setColumnWidth(0, self._first_column_width)
        self._set_data_in_table(self._keywords_data)

    def _check_item(self):
        name = self._table.selectedItems()[0].text()
        path = self._table.selectedItems()[1].text()
        if self._combox.currentText().lower() == 'Builtin'.lower():
            text = self._assemble_content(self._keywords_data, name, path)
            self._keyword_des.setText(text)
        else:
            SpecifiedKeywordJumper().get_keyword_item(path, name)

    def _assemble_content(self, _list, name, path):
        text = ''
        for row in _list:
            if row[0] == name and row[1] == path:
                text = '【Source】: ' + row[1] + '\n\n' + '【功能说明】: ' + row[2]
        return text

    def _open_config_dialog(self):
        self._config_dialog = ConfigLibDialog('SOURCE_KEYWORDS_LIB')
        self._config_dialog.show()
        self._config_dialog.ok_pressed.connect(self._update_source_lib)

    def _update_source_lib(self):
        _dict = SystemSettings().read("SOURCE_KEYWORDS_LIB")
        if _dict:
            self._source_lib = list(_dict.keys())
            self._source_lib.append('Userkeywords')
            self._combox.clear()
            self._combox.addItems(self._source_lib)

    def _check_rfcode_file(self, key):
        config_file = HistoryProject().read('PROJECT_PATH') + os.path.sep + '.rfcode'
        with open(config_file, 'r', encoding = 'utf8') as f:
            content = f.read()
            if content:
                _dict = eval(content)
                if key in _dict:
                    return True
                else:
                    return False

#     def _stop_search(self):
#         label = PluginRepository().find('KEYWORD_PROGRESS')
#         status_bar = PluginRepository().find('STATUS_BAR')
#         label.setText('关键字搜索终止 ')
#         status_bar.addPermanentWidget(label)

#     def closeEvent(self, event):
#         self._kill_old_loading_bar()
#         self._kill_old_thread()
#         self._stop_search()


class SearchThread(QThread):

    search_finished = pyqtSignal(list)

    def __init__(self, _dict, combox_text, search_line_text):
        super().__init__()
        self._dict = _dict
        self._combox_text = combox_text
        self._search_line_text = search_line_text
        self._path = self._get_path()
        self._fc = KeywordsFileCounter(self._path)

    def _get_path(self):
        path = ''
        if self._combox_text == 'Userkeywords':
            self._project_path = HistoryProject().read('PROJECT_PATH')
            if self._project_path and self._get_system_lib('USERKEYWORDS'):
                path = self._project_path + os.path.sep + self._get_system_lib('USERKEYWORDS')
        elif self._combox_text == 'Builtin':
            path = ''
        else:
            path = self._dict[self._combox_text]
        return path

    def run(self):
        _list = Keyword(self._path, self._fc).search(self._search_line_text)
        self.search_finished.emit(_list)

    def get_file_counter(self):
        return self._fc

    def _get_system_lib(self, key):
        config_file = self._project_path + os.path.sep + '.rfcode'
        with open(config_file, 'r', encoding = 'utf8') as f:
            content = f.read()
            if content:
                _dict = eval(content)
                if key in _dict:
                    return _dict[key]


def except_hook(cls, exception, traceback):
    sys.__excepthook__(cls, exception, traceback)


if __name__ == '__main__':
    import cgitb
    cgitb.enable(format = 'text')
    sys.excepthook = except_hook
    app = QApplication(sys.argv)
    ui = Keywords('dialog', Loader().get_path('SEARCH'))
    ui.show()
#     ui.ok_pressed.connect(test_fun)
    sys.exit(app.exec_())
