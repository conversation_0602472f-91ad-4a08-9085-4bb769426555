'''
Created on 2019年11月27日

@author: 10243352
'''
import os
import re

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from model.data_file.KeyWords import KeyWord
from model.data_file.Repository import ArgumentsRepository, \
    ProjectTreeRepository, DataFileRepository
from model.data_file.Variables import <PERSON>alar
from settings.HistoryProject import HistoryProject
from view.explorer.tree_item.TestcaseItem import TestcaseItem
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem


class LocalRepoUpdater(object):

    PARENT_TAGS = []

    def __init__(self, current_item):
        self._current_item = current_item
        self._parsed_item = None

    def update(self):
        LocalRepoUpdater.PARENT_TAGS = []
        self._parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
        self._update_arguments_repo()
        self._update_local_repo()
        self._update_parent_tags()

    def _update_local_repo(self):
        data_file = self._parsed_item.get_cur_data_file(self._current_item)
        if data_file and hasattr(data_file, 'open'):
            data_file.open()

    def _update_arguments_repo(self):
        ArgumentsRepository().clear()
        cur_obj = self._parsed_item.get_cur_obj(self._current_item)
        if isinstance(cur_obj, KeyWord):
            keyword = cur_obj.query()
            if hasattr(keyword, "arguments") and keyword.arguments:
                arguments = [argument.strip() for argument in keyword.arguments[0].split("|")]
                for argument in arguments:
                    full_names = re.findall("{.*?}", argument.split("=")[0])
                    if len(full_names) > 0:
                        name = full_names[0][1:-1]
                        ArgumentsRepository().add(name, Scalar(name))

    def _update_parent_tags(self):
        cur_data_file = self._parsed_item.get_cur_data_file(self._current_item)
        if not cur_data_file or not hasattr(cur_data_file, 'path'):
            return
        root_path = os.path.abspath(HistoryProject.read('PROJECT_PATH'))
        end_dir = os.path.dirname(cur_data_file.path).strip(os.path.sep)
        if root_path == end_dir and "__init__" == os.path.split(os.path.splitext(cur_data_file.path)[0])[-1]:
            return
        self._add_init_file_tags(root_path)
        cur_dir = root_path
        for dir_name in end_dir[len(root_path):].strip(os.path.sep).split(os.path.sep)[:-1]:
            cur_dir = os.path.abspath(os.path.join(cur_dir, dir_name))
            self._add_init_file_tags(cur_dir)
        if isinstance(self._current_item, TestcaseItem):
            self._add_init_file_tags(end_dir)
            self._add_cur_file_tags(cur_data_file.path)
        elif not (isinstance(self._current_item, ProjectTreeItem) or "variable" in self._current_item.__class__.__name__.lower()):
            self._add_init_file_tags(end_dir)
        LocalRepoUpdater.PARENT_TAGS = list(set(LocalRepoUpdater.PARENT_TAGS))

    def _add_init_file_tags(self, path):
        node = ProjectTreeRepository().find(os.path.abspath(path))
        if not node:
            return
        if node.init_file and hasattr(node.init_file, "settings"):
            node.init_file.settings.query()
            if hasattr(node.init_file.settings, "force_tags") and node.init_file.settings.force_tags:
                LocalRepoUpdater.PARENT_TAGS.extend([tag.strip() for tag in node.init_file.settings.force_tags[0].split("|")])
            if hasattr(node.init_file.settings, "default_tags") and node.init_file.settings.default_tags:
                LocalRepoUpdater.PARENT_TAGS.extend([tag.strip() for tag in node.init_file.settings.default_tags[0].split("|")])

    def _add_cur_file_tags(self, path):
        data_file = DataFileRepository().find(os.path.abspath(path))
        if data_file and hasattr(data_file, "settings"):
            data_file.settings.query()
            if hasattr(data_file.settings, "force_tags") and data_file.settings.force_tags:
                LocalRepoUpdater.PARENT_TAGS.extend([tag.strip() for tag in data_file.settings.force_tags[0].split("|")])
            if hasattr(data_file.settings, "default_tags") and data_file.settings.default_tags:
                LocalRepoUpdater.PARENT_TAGS.extend([tag.strip() for tag in data_file.settings.default_tags[0].split("|")])
