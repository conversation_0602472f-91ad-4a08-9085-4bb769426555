import sys
import math
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


class StarWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(35, 35)
        self.fill_color = Qt.gray  # 默认灰色
        self.border_color = Qt.darkGray
        self.is_hovered = False

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制五角星
        path = QPainterPath()
        size = min(self.width(), self.height())
        center = QPointF(size/2, size/2)
        radius = size/2 * 0.8

        # 五角星顶点计算
        angles = [math.radians(90 + 72*i) for i in range(5)]
        outer_points = [QPointF(
            center.x() + radius * math.cos(angle),
            center.y() - radius * math.sin(angle)
        ) for angle in angles]

        inner_radius = radius * 0.4
        inner_angles = [math.radians(90 + 72*i + 36) for i in range(5)]
        inner_points = [QPointF(
            center.x() + inner_radius * math.cos(angle),
            center.y() - inner_radius * math.sin(angle)
        ) for angle in inner_angles]

        # 构建路径
        path.moveTo(outer_points[0])
        for i in range(5):
            path.lineTo(inner_points[i])
            path.lineTo(outer_points[(i+1)%5])
        path.closeSubpath()

        # 绘制填充
        painter.fillPath(path, self.fill_color)
        
        # 绘制边框
        pen = QPen(self.border_color, 2)
        painter.setPen(pen)
        painter.drawPath(path)

class StarRatingWidget(QWidget):
    rating_changed = pyqtSignal(int, int)  # 修改信号，发送 rating_id 和 star_value

    def __init__(self):
        super().__init__()
        self.stars = {}  # 改为字典存储 {rating_id: [star1, star2...]}
        self.ratings = {}  # 存储每行的评分 {rating_id: star_value}
        self.current_hover = (-1, -1)  # (rating_id, star_index)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # 评分项列表
        rating_items = [
            ("相似用例查找", 1),
            ("关键字推荐", 2), 
            ("AI脚本生成", 3)
        ]

        for label_text, rating_id in rating_items:
            # 创建评分行
            row = QHBoxLayout()
            row.setSpacing(10)
            
            # 添加标签
            label = QLabel(f"{label_text}:")
            label.setFixedWidth(100)
            row.addWidget(label)
            
            # 添加5颗星
            star_list = []
            for i in range(5):
                star = StarWidget()
                star.installEventFilter(self)
                star.setProperty("rating_id", rating_id)
                star.setProperty("star_value", i+1)
                star_list.append(star)
                row.addWidget(star)
            
            self.stars[rating_id] = star_list
            # 添加弹性空间
            row.addStretch()
            main_layout.addLayout(row)

        self.setLayout(main_layout)
        self.update_stars()

    def eventFilter(self, obj, event):
        rating_id = obj.property("rating_id")
        star_value = obj.property("star_value")
        
        
        if event.type() == QEvent.Enter:
            star_index = self.stars[rating_id].index(obj)
            self.current_hover = (rating_id, star_index)
            self.update_stars()
        elif event.type() == QEvent.Leave:
            self.current_hover = (-1, -1)
            self.update_stars()
        elif event.type() == QEvent.MouseButtonPress:
            if event.button() == Qt.LeftButton:
                self.ratings[rating_id] = star_value
                self.rating_changed.emit(rating_id, star_value)
                self.update_stars()
        return super().eventFilter(obj, event)

    def update_stars(self):
        for rating_id, star_list in self.stars.items():
            for i, star in enumerate(star_list):
                star_value = star.property("star_value")
                
                # 判断是否悬停或已评分
                is_hover = (rating_id == self.current_hover[0] and 
                           i <= self.current_hover[1])
                is_rated = (star_value <= self.ratings.get(rating_id, 0))
                
                if is_hover or is_rated:
                    star.fill_color = QColor(255, 215, 0)  # 金色
                    star.border_color = QColor(184, 134, 11)
                else:
                    star.fill_color = QColor(211, 211, 211)  # 灰色
                    star.border_color = QColor(169, 169, 169)
                
                star.update()

class FeedbackDialog(QDialog):
    feedbackDialogReady = pyqtSignal(dict)

    def __init__(self, x, y, para=None):
        super().__init__()
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setGeometry(x, y, 300, 180)
        self.para = para
        self.setWindowTitle("用户反馈")
        self.setGeometry(x, y, 400, 300)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 评分组件
        self.rating_widget = StarRatingWidget()
        self.rating_label = QLabel("请点击星星进行评分")
        self.rating_widget.rating_changed.connect(self.update_rating_label)

        # 反馈输入
        self.feedback_input = QTextEdit()
        self.feedback_input.setPlaceholderText("请输入您的意见和建议...")
        self.feedback_input.setMinimumHeight(100)

        # 提交按钮
        submit_btn = QPushButton("提交反馈")
        submit_btn.setFixedHeight(35)
        submit_btn.clicked.connect(self.submit_feedback)

        # 布局
        layout.addWidget(QLabel("服务评价:"))
        layout.addWidget(self.rating_widget)
        layout.addWidget(self.rating_label)
        layout.addSpacing(10)
        layout.addWidget(QLabel("意见反馈:"))
        layout.addWidget(self.feedback_input)
        layout.addWidget(submit_btn)

        self.setLayout(layout)

    def update_rating_label(self, rating_id, star_value):
        rating_texts = {
            1: "相似用例查找",
            2: "关键字推荐", 
            3: "AI脚本生成"
        }
        ratings = ["很差", "较差", "一般", "较好", "很好"]
        if 1 <= star_value <= 5:
            self.rating_label.setText(
                f"{rating_texts[rating_id]}: {star_value}星 ({ratings[star_value-1]})"
            )
            self.rating_label.setStyleSheet("color: blue;")
        else:
            self.rating_label.setText("请点击星星进行评分")
            self.rating_label.setStyleSheet("color: black;")

    def submit_feedback(self):
        if not self.rating_widget.ratings:
            QMessageBox.warning(self, "提示", "请先进行星级评分!")
            return

        # 此处可以添加数据库存储或网络请求
        feedback = self.feedback_input.toPlainText()
        print(f"提交成功!\n评分: {self.rating_widget.ratings}\n反馈内容: {feedback}")
        QMessageBox.information(self, "成功", "感谢您的反馈!")
        self.feedbackDialogReady.emit({'score': self.rating_widget.ratings, 'content': feedback})
        self.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FeedbackDialog()
    window.show()
    sys.exit(app.exec_())
