'''
Created on 2019年12月18日

@author: 10243352
'''
import os
import time

import chardet

from controller.parser.reader.ReaderFactory import ReaderFactory
from model.data_file.env import SUPPORT_SUITE_FORMAT
from model.searcher.repositories.TestCasesRepository import TestCasesRepository
from utility.log.SystemLogger import logger
from utility.EncodingHandler import EncodingHandler


class TestCase(object):
    CACHE = {}

    def __init__(self, path, file_counter):
        self._clear()
        self._path = path
        if self._verify_path():
            return
        for root, _, files in os.walk(path):
            for file in files:
                file_counter.add_number_of_analyzed_files(1)
                file_path = os.path.abspath(os.path.join(root, file))
                if file_path in TestCase.CACHE:
                    if self._get_record_modify_time(file_path) == self._get_latest_modify_time(file_path):
                        continue
                TestCase.CACHE[file_path] = self._get_latest_modify_time(file_path)
                TestCasesRepository().delete(file_path)
                if os.path.splitext(file_path)[-1] not in SUPPORT_SUITE_FORMAT:
                    continue
                self._store_testcases(file_path)
        file_counter.set_analyzed_finished()

    def search(self, key):
        results = []
        if self._verify_path():
            return results
        for path in TestCasesRepository().keys():
            names = TestCasesRepository().find(path)
            match_names = filter(lambda testcase_name: key in testcase_name and path.startswith(os.path.abspath(self._path + os.path.sep)), names)
            sub_results = [[name, path] for name in match_names]
            results.extend(sub_results)
        return results

    def _get_latest_modify_time(self, path):
        return time.mktime(time.localtime(os.stat(path).st_mtime))

    def _get_record_modify_time(self, path):
        return TestCase.CACHE[path]

    def _clear(self):
        self._clear_repo()
        self._clear_cache()

    def _clear_cache(self):
        cache_keys = [path for path in TestCase.CACHE.keys()]
        for path in cache_keys:
            if not os.path.exists(path):
                del TestCase.CACHE[path]

    def _clear_repo(self):
        repo_keys = [path for path in TestCasesRepository().keys()]
        for path in repo_keys:
            if not os.path.exists(path):
                TestCasesRepository().delete(path)

    def _store_testcases(self, file_path):
        """存储测试用例，使用改进的编码处理"""
        try:
            # 检查文件是否存在且不为空
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                logger.debug(f"文件不存在或为空: {file_path}")
                return

            begin_record = False
            line_number = 0

            # 使用EncodingHandler按行读取文件
            for line in EncodingHandler.read_file_lines_with_encoding(file_path):
                line_number += 1

                # 跳过空行
                if not line.strip():
                    continue

                # 处理行内容
                tag = line.rstrip().strip("*").lower()
                if not begin_record and line.startswith('*') and tag.strip() in ["test case", "testcase", "testcases", "test cases"]:
                    begin_record = True
                    continue
                if begin_record and line.startswith('*') and tag.strip() in ["settings", "variables", "keywords", "keyword"]:
                    break
                if not begin_record:
                    continue

                self._store_testcase(file_path, line)

        except Exception as e:
            logger.error(f"处理文件时出错: {file_path}, 错误: {e}")
            import traceback
            traceback.print_exc()

    def _store_testcase(self, file_path, line_content):
        cells = line_content.split(ReaderFactory.get_instance(file_path).CELL_SEPERATOR)
        first_cell = cells[0].strip()
        if first_cell != "" and not first_cell.startswith("#"):
            TestCasesRepository().add(file_path, cells[0])

    def _get_encode_type(self, line, encode_type):
        """改进的编码检测方法"""
        if encode_type is None:
            try:
                # 使用chardet检测编码
                detection_result = chardet.detect(line)
                detected_encoding = detection_result.get("encoding")
                confidence = detection_result.get("confidence", 0)

                # 如果检测结果不可靠或为ascii，使用utf-8
                if not detected_encoding or confidence < 0.7 or detected_encoding.lower() == "ascii":
                    encode_type = "utf-8"
                else:
                    encode_type = detected_encoding

                # 处理一些常见的编码别名
                if encode_type.lower() in ['gb2312', 'gbk']:
                    encode_type = 'gbk'
                elif encode_type.lower() in ['utf-8-sig']:
                    encode_type = 'utf-8'

            except Exception as e:
                logger.warn(f"编码检测失败: {e}, 使用默认UTF-8编码")
                encode_type = "utf-8"

        return encode_type

    def _verify_path(self):
        return not os.path.exists(self._path)

if __name__ == '__main__':
    time_01 = time.time()
    results = TestCase(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test\testcases").search("小区")
    time_02 = time.time()
    print("11111111111", time_02 - time_01)
    print(TestCase(r"D:\Demo\5g_nr_v3\1207\script_v3\5GNR\test\testcases").search("小区"))
    time_03 = time.time()
    print("11111111111", time_03 - time_02)
