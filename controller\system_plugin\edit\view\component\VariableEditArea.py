# encoding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
import re

from PyQt5.Qt import Qt, QItemSelectionModel, QCursor
from PyQt5.QtWidgets import QHBoxLayout, QLabel, QPushButton, QLineEdit, \
    QVBoxLayout, QAction, QTableWidget, QAbstractItemView, QTableWidgetItem, \
    QMenu

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.view.component.Formator import Comment
from controller.system_plugin.edit.view.component.VariableTableMenu import VariableTableMenu
from model.CurrentItem import CurrentItem
from view.common.MessageBox import MessageBox
from view.common.dialog.DictVariableDialog import DictVariableDialog
from view.common.dialog.ListVariableDialog import ListVariableDialog
from view.common.dialog.ScalarDialog import ScalarDialog


class VariableEditArea(object):

    def __init__(self, parent):
        self._parent = parent
        self._layout = None
        self._current_row = None

    def get_layout(self):
        return self._layout

    def load(self):
        if self._layout:
            return self._layout
        self._set_table()
        self._set_add_scalar_btn()
        self._set_add_list_btn()
        self._set_add_dict_btn()
        self._set_layout()
        return self._layout

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._table)
        inner_layout = QVBoxLayout()
        inner_layout.addWidget(self._add_scalar_btn, 0, Qt.AlignTop)
        inner_layout.addWidget(self._add_list_btn, 1, Qt.AlignTop)
        inner_layout.addWidget(self._add_dict_btn, 2, Qt.AlignTop)
        self._layout.addLayout(inner_layout)

    def _set_table(self):
        self._table = QTableWidget(self._parent)
        self._table.setColumnCount(3)
        self._table.setRowCount(0)
        self._table.verticalHeader().setVisible(False)
        self._table.horizontalHeader().setStretchLastSection(True)
        self._table.setHorizontalHeaderLabels(['Variable', 'Value', 'Comment'])
        self._table.setShowGrid(False)
        self._table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft)
        self._table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self._table.setSelectionMode(QAbstractItemView.SingleSelection)
        self._table.setStyleSheet("border:none;")
        self._table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self._table.doubleClicked.connect(self._show_dialog)
        self.table_menu = VariableTableMenu(self)
        self.table_menu.load_shortcut()
        self._table.setContextMenuPolicy(Qt.CustomContextMenu)
        self._table.customContextMenuRequested.connect(self.generate_menu)
        SignalDistributor().locate_varible.connect(self._locate_varible)

    def _set_add_scalar_btn(self):
        self._add_scalar_btn = QPushButton('    Add Scalar    ', self._parent)
        self._add_scalar_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._add_scalar_btn.clicked.connect(self._show_scalar)

    def _show_scalar(self):
        self._scalar_dialog = ScalarDialog('Scalar Variable')
        self._scalar_dialog.show()
        self._scalar_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _fill_one_row(self, info_list):
        if not self._check_name_is_repeated(info_list[0][0]):
            info_list = self._formate_variable(info_list)
            operator = None
            rows = self._table.rowCount()
            if self._table.item(0, 0) and self._select_row < 0 or rows == 0:
                self._table.insertRow(rows)
            for i in range(len(info_list)):
                rows = self._table.rowCount()
                if self._select_row > -1:
                    self._table.setItem(self._select_row, i, QTableWidgetItem(info_list[i]))
                    self._current_row = self._select_row
                    operator = 'modify'
                else:
                    self._table.setItem(rows - 1, i, QTableWidgetItem(info_list[i]))
                    self._current_row = rows - 1
                    operator = 'new'
            self._modify_data({'operator': operator})

    def _formate_variable(self, info_list):
        if len(info_list[2]) == 1:
            info_list[2] = ''
        info_list[2] = Comment(info_list[2]).get_comment()
        if not info_list[0][1].startswith('$'):
            info_list[1] = ' | '.join(info_list[1])
        else:
            info_list[1] = info_list[1][0]
        info_list = [info_list[0][1]] + info_list[1:]
        return info_list

    def _check_name_is_repeated(self, name):
        all_row = self._table.rowCount()
        for r in range(all_row):
            text = self._table.item(r, 0).text()
            if name == text[2:-1] and r != self._select_row:
                MessageBox().show_critical('Variable with this name already exists.')
                return True
        return False

    def _set_add_list_btn(self):
        self._add_list_btn = QPushButton('Add List', self._parent)
        self._add_list_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._add_list_btn.clicked.connect(self._show_list)

    def _set_add_dict_btn(self):
        self._add_dict_btn = QPushButton('Add Dict', self._parent)
        self._add_dict_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._add_dict_btn.clicked.connect(self._show_dict)

    def _show_list(self):
        self._list_dialog = ListVariableDialog('List Variable')
        self._list_dialog.show()
        self._list_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _show_dict(self):
        self._dict_dialog = DictVariableDialog('Dictionary Variable')
        self._dict_dialog.show()
        self._dict_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def reset_table(self):
        self._table.clearContents()
        self._table.setColumnCount(3)
        self._table.setRowCount(0)

    def fill_data(self, content):
        self.reset_table()
        if content:
            for row in range(len(content)):
                all_row = self._table.rowCount()
                if row == all_row:
                    self._table.insertRow(all_row)
                for column in range(len(content[row])):
                    if isinstance(content[row][column], list):
                        item = QTableWidgetItem(' | '.join(content[row][column]))
                    else:
                        item = QTableWidgetItem(content[row][column])
                    self._table.setItem(row, column, item)
#         self._set_name_without_star()

    def _set_name_without_star(self):
        suite = CurrentItem().get()['suite']
        if suite:
            suite.set_name_without_star()

    def _show_dialog(self):
        row = self._table.selectedIndexes()[0].row()
        if self._table.item(row, 0):
            text = self._table.item(row, 0).text()[0]
            types = {'$':'scalar', '@':'list', '&':'dict'}
            eval('self._show_' + types.get(text) + '()')
            infos = [self._table.item(row, i).text() if self._table.item(row, i) else '' for i in range(3)]
            if not text.startswith('$'):
                infos[1] = infos[1].split(' | ')
            eval('self._' + types.get(text) + '_dialog.fill_data(%s)' % infos)
            self._select_row = row

    def generate_menu(self, pos):
        if self._table.rowCount() > 0:
            self._menu = QMenu()
            self.table_menu.load_menu()
            self._menu.exec_(self._table.mapToGlobal(pos))

#     def get_data(self):
#         all_row = self._table.rowCount()
#         all_column = self._table.columnCount()
#         result = []
#         for row in range(all_row):
#             line = []
#             for column in range(all_column):
#                 if self._table.item(row, column):
#                     line.append(self._table.item(row, column).text())
#                 else:
#                     line.append('')
#             result.append(line)
#         return result

    def get_line_data(self,):
        all_column = self._table.columnCount()
        line = []
        for column in range(all_column):
            if self._table.item(self._current_row, column):
                line.append(self._table.item(self._current_row, column).text())
            else:
                line.append('')
        return line

    def _format_value(self, line):
        if line[0].startswith('@{'):
            result = line[1].split(' | ')
            for index in range(len(result)):
                if result[index] == '':
                    result[index] = '${EMPTY}'
            line[1] = result
        return line

    def _modify_data(self, oprator_dict):
        result = {'type': 'variables', 'value': self.get_line_data(), 'index': self._current_row}
        result.update(oprator_dict)
        if self._current_row is not None:
            SignalDistributor().editor_modify(CurrentItem().get_current_item(), result)

    def _locate_varible(self, index):
        self._table.setCurrentCell(index, 0, QItemSelectionModel.Select)
        self._table.selectRow(index)
        self._table.setFocus()
