# -*- coding:utf-8 -*-
'''
Created on 2019年10月17日

@author: 10247557
'''
import logging
import sys
import inspect
import ctypes
import os
import re

from iplatform.ThreadManager import ThreadManager
from settings.UserSettings import UserSettings
from utility.timer import set_timeout, after_timeout


class SandBox:

    @set_timeout(60, after_timeout)
    @staticmethod
    def execute(cmdStr, plugin_id):
        if plugin_id in UserSettings().get_plugins():
            cmd = compile(cmdStr, '', 'exec')
            try:
                exec(cmd)
            except Exception as e:
                logging.error("load plugin {} fail".format(plugin_id))
                logging.error(e)
        else:
            ThreadManager.start(cmdStr, plugin_id)


    @staticmethod
    def force_interrupt(plugin_id):
        ThreadManager.force_interrupt(plugin_id)


if __name__ == "__main__":
    import time
#     cmdStr = "time.sleep(5)\n1/0"
    cmdStr = "from plugins.demo.src.Main import Main\nMain().activate()"
    thread = SandBox.execute(cmdStr, 10)
    time.sleep(12)
#     thread = SandBox.execute(cmdStr, 10)
#     from utility.log import Logger
#     logger = Logger.get_plugin_logger('demo')
#     print (logger)
#     logger.info('22222')


