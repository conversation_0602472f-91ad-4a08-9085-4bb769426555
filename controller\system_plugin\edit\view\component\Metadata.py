# encoding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
from PyQt5.Qt import Qt, QCursor
from PyQt5.QtWidgets import QHBoxLayout, QLabel, QPushButton, QLineEdit, \
    QVBoxLayout, QAction, QTableWidget, QAbstractItemView, QTableWidgetItem, \
    QMenu

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserF<PERSON>ry
from controller.system_plugin.edit.view.component.TableMenu import TableMenu
from model.CurrentItem import CurrentItem
from view.common.dialog.MetadataDialog import MetadataDialog


class MetadataEditArea(object):

    def __init__(self, parent):
        self._parent = parent
        self._current_row = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._set_table()
        self._set_add_metadata_btn()
        self._set_layout()

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._table)
        inner_layout = QVBoxLayout()
        inner_layout.addWidget(self._add_metadata_btn, 0, Qt.AlignTop)
        self._layout.addLayout(inner_layout)

    def _set_table(self):
        self._table = QTableWidget(self._parent)
        self._table.setColumnCount(3)
        self._table.setRowCount(0)
        self._table.verticalHeader().setVisible(False)
        self._table.horizontalHeader().setStretchLastSection(True)
        self._table.setHorizontalHeaderLabels(['Metadata', 'Value', 'Comment'])
        self._table.setShowGrid(False)
        self._table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft)
        self._table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self._table.setSelectionMode(QAbstractItemView.SingleSelection)
        self._table.setStyleSheet("border:none;")
        self._table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table_menu = TableMenu(self)
        self.table_menu.load_shortcut()
        self._table.setContextMenuPolicy(Qt.CustomContextMenu)
        self._table.customContextMenuRequested.connect(self.generate_menu)
        self._table.doubleClicked.connect(self._show_dialog)
        self._select_row = -1

    def _set_add_metadata_btn(self):
        self._add_metadata_btn = QPushButton('   Add Metadata   ', self._parent)
        self._add_metadata_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._add_metadata_btn.clicked.connect(self._show_metadata)

    def _show_metadata(self):
        self._metadata_dialog = MetadataDialog('Metadata')
        self._metadata_dialog.show()
        self._metadata_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _show_dialog(self):
        row = self._table.selectedIndexes()[0].row()
        if self._table.item(row, 0):
            self._show_metadata()
            infos = [self._table.item(row, i).text() if self._table.item(row, i) else '' for i in range(3)]
            self._metadata_dialog.fill_data(infos)
            self._select_row = row

    def _fill_one_row(self, info_list):
        rows = self._table.rowCount()
        if self._table.item(0, 0) and self._select_row < 0 or rows == 0:
            self._table.insertRow(rows)
        for i in range(len(info_list)):
            rows = self._table.rowCount()
            if self._select_row > -1:
                self._table.setItem(self._select_row, i, QTableWidgetItem(info_list[i]))
                self._current_row = self._select_row
            else:
                self._table.setItem(rows - 1, i, QTableWidgetItem(info_list[i]))
                self._current_row = rows - 1
        self._modify_data()

    def reset_table(self):
        self._table.clearContents()
        self._table.setColumnCount(3)
        self._table.setRowCount(0)

    def fill_data(self, content):
        self.reset_table()
        if content:
            for row in range(len(content)):
                all_row = self._table.rowCount()
                if row == all_row:
                    self._table.insertRow(all_row)
                for column in range(len(content[row]) - 1):
                    item = QTableWidgetItem(content[row][column + 1])
                    self._table.setItem(row, column, item)

    def get_data(self):
        all_row = self._table.rowCount()
        all_column = self._table.columnCount()
        result = []
        for row in range(all_row):
            line = ['metadata']
            for column in range(all_column):
                if self._table.item(row, column):
                    line.append(self._table.item(row, column).text())
                else:
                    line.append('')
            result.append(line)
        return result

    def _modify_data(self):
        result = self.get_data()
        if self._current_row is not None:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify('metadata', result)

    def generate_menu(self, pos):
        if self._table.rowCount() > 0:
            self._menu = QMenu()
            self.table_menu.load_menu()
            self._menu.exec_(self._table.mapToGlobal(pos))
