'''
Created on 2019年10月22日

@author: 10243352
'''
import copy
import re

from controller.parser.rules.ActionParsedRule import ActionParsedRule
from controller.parser.rules.CommParsedRule import is_cross_row
from settings.SystemSettings import SystemSettings
ROBOT_VERSION = SystemSettings().read('ROBOT_VERSION')


class TextBlock(object):
    __slot__ = ["_values"]
    NO_ACTION_TABLE = ["Settings", "Varibales"]
    TAB = "\t"
    ENTER = "\n"
    CROSS_ROW = "..."
    ROW_LIMIT = 7

    def __init__(self):
        self._values = []

    @property
    def class_name(self):
        return self.__class__.__name__

    def populate(self, values):
        self._values.append(values)

    def anti_populate(self, table_type="Settings"):
        if table_type in TextBlock.NO_ACTION_TABLE:
            return self._anti_populate_of_no_action()
        return self._anti_populate_of_action()

    def set_values(self, values):
        self._values = values

    def query(self, table_type):
        if table_type in TextBlock.NO_ACTION_TABLE:
            return self._query_of_no_action()
        return self._query_of_action()

    def modify(self, content):
        if content is None:
            values, comments = [], []
        else:
            values, comments = content
            values = [value.strip() for value in values.split("|")] if values != "" else []
            comments = [comment.strip() for comment in comments.split("|")] if comments != "" else []
            if len(comments) == 1 and comments[0] == "":
                comments = []
        values.extend(comments)
        self.content = [values]
        self.set_values(self.content)

    def _anti_populate_of_no_action(self):
        if len(self._values) > 0:
            first_line = [self.NO_ACTION_HEAD]
            first_line.extend(self._values[0])
            content = TextBlock.TAB.join(first_line)
        for cells in self._values[1:]:
            content += TextBlock.ENTER
            content += (TextBlock.CROSS_ROW + TextBlock.TAB + TextBlock.TAB.join(cells))
        return content + TextBlock.ENTER

    def _anti_populate_of_action(self):
        content = ""
        if len(self._values) > 0:
            first_line = [content + TextBlock.TAB + self.ACTION_HEAD]
            first_line.extend(self._values[0][1:])
            content = TextBlock.TAB.join(first_line)
        for cells in self._values[1:]:
            content += TextBlock.ENTER
            content += (TextBlock.TAB + TextBlock.CROSS_ROW + TextBlock.TAB + TextBlock.TAB.join(cells))
        return content + TextBlock.ENTER

    def _format_attr_name(self):
        return "".join([char if char.islower() else "_" + char.lower() for char in self.__class__.__name__]).strip("_")

    def _get_content(self):
        content = []
        for row in self._values:
            content.extend(row)
        return content

    def _split_content(self):
        content = self._get_content()
        for index in range(len(content)):
            cell = content[index]
            if cell.startswith("#"):
                return content[:index], content[index:]
        return content, []

    def _query_of_action(self):
        values, comments = self._split_content()
        return [" | ".join(values[1:]), " | ".join(comments)] if (len(values) != 1 or len(comments) != 0) else None

    def _query_of_no_action(self):
        values, comments = self._split_content()
        return [" | ".join(values).rstrip(" \|| "), " | ".join(comments)] if (
                values != [] or len(comments) != 0) else None


class SuiteSetup(TextBlock):
    NO_ACTION_HEAD = "Suite Setup"

    pass


class SuiteTeardown(TextBlock):
    NO_ACTION_HEAD = "Suite Teardown"

    pass


class TestSetup(TextBlock):
    NO_ACTION_HEAD = "Test Setup"
    pass


class TestTeardown(TextBlock):
    NO_ACTION_HEAD = "Test Teardown"
    pass


class Tags(TextBlock):
    ACTION_HEAD = "[Tags]"

    def _query_of_action(self):
        content = []
        [content.extend(row) for row in self._values]
        rets = [" | ".join(content[1:]).rstrip(" \|| ").strip("|")] if len(content) > 1 else None
        return rets if rets and rets[0] != "" else None

    def modify(self, content):
        if content is None:
            return
        values = [value.strip() for value in content.split("|")]
        self.content = [values]
        self.set_values(self.content)


#     def _query_of_action(self):
#         values, comments = self._split_content()
#         return [" | ".join(values[1:]).strip("\|| "), " | ".join(comments)]


class ForceTags(TextBlock):
    NO_ACTION_HEAD = "Force Tags"

    def _query_of_no_action(self):
        content = []
        [content.extend(row) for row in self._values]
        rets = [" | ".join(content).rstrip(" \|| ")] if len(content) > 0 else None
        return rets if rets and rets[0] != "" else None

    def modify(self, content):
        if content is None:
            return
        values = [value.strip() for value in content.split("|")]
        self.content = [values]
        self.set_values(self.content)


class DefaultTags(ForceTags):
    NO_ACTION_HEAD = "Default Tags"


class TestTimeout(TextBlock):
    NO_ACTION_HEAD = "Test Timeout"
    pass


class Library(TextBlock):
    NO_ACTION_HEAD = "Library"

    def get_relative_path(self):
        return self._values[0][0] if len(self._values[0]) > 0 else None

    def _has_alias(self, values):
        return "WITH NAME" in values

    def _has_args(self, values):
        if len(values) > 1:
            if self._has_alias(values) and values.index("WITH NAME") > 1:
                return True
            elif not self._has_alias(values):
                return True
        return False

    def _get_alias(self, values):
        return values[values.index("WITH NAME") + 1:]

    def _get_args(self, values):
        if "WITH NAME" in values:
            return values[1: values.index("WITH NAME")]
        else:
            return values[1:]

    def _query_of_no_action(self):
        values, comments = self._split_content()
        if len(values) == 0:
            return None
        if len(values) <= 1:
            return [" | ".join(values), "", "", " | ".join(comments)]
        if self._has_alias(values) and self._has_args(values):
            return [values[0], " | ".join(self._get_args(values)), " | ".join(self._get_alias(values)),
                    " | ".join(comments)]
        elif not self._has_alias(values) and self._has_args(values):
            return [values[0], " | ".join(self._get_args(values)), "", " | ".join(comments)]
        elif not self._has_alias(values) and not self._has_args(values):
            return [values[0], "", "", " | ".join(comments)]
        elif self._has_alias(values) and not self._has_args(values):
            return [values[0], "", " | ".join(self._get_alias(values)), " | ".join(comments)]

    def modify(self, content):
        if content is None:
            return
        values, args, alias, comments = content
        values = [value.strip() for value in values.split("|")]
        args = [arg.strip() for arg in args.split("|")]
        alias = ["WITH NAME", alias] if alias != "" else []
        comments = [comment.strip() for comment in comments.split("|")] if comments != "" else []
        values.extend(args)
        values.extend(alias)
        values.extend(comments)
        self.content = [values]
        self.set_values(self.content)


class Resource(TextBlock):
    NO_ACTION_HEAD = "Resource"

    def get_relative_path(self):
        return self._values[0][0] if len(self._values[0]) > 0 else None

    def _query_of_no_action(self):
        values, comments = self._split_content()
        return [" | ".join(values).rstrip(" \|| "), "", "", " | ".join(comments)] if len(values) > 0 else None

    def modify(self, content):
        if content is None:
            return
        values, comments = content[0], content[-1]
        values = [value.strip() for value in values.split("|")]
        comments = [comment.strip() for comment in comments.split("|")]
        values.extend(comments)
        self.content = [values]
        self.set_values(self.content)


class Metadata(TextBlock):
    NO_ACTION_HEAD = "Metadata"

    def _query_of_no_action(self):
        values, comments = self._split_content()
        return [values[0], values[1], " | ".join(comments)] if len(values) > 0 else None

    def modify(self, content):
        if content is None:
            return
        name, value, comment = content
        name = name if name else "\\"
        value = value if value else "${EMPTY}"
        comments = [comment] if comment != "" else []
        metadata = [name, value]
        metadata.extend(comments)
        self.content = [metadata]
        self.set_values(self.content)


class Variables(TextBlock):
    NO_ACTION_HEAD = "Variables"

    def _has_args(self, values):
        return len(values) > 1

    def _query_of_no_action(self):
        values, comments = self._split_content()
        if len(values) == 0:
            return None
        if self._has_args(values):
            return [values[0], " | ".join(values[1:]), "", " | ".join(comments)]
        else:
            return [" | ".join(values), "", "", " | ".join(comments)]

    def modify(self, content):
        if content is None:
            return
        values, args, comments = content[0], content[1], content[-1]
        values = [value.strip() for value in values.split("|")]
        args = [args.strip() for arg in args.split("|")] if args != "" else []
        comments = [comment.strip() for comment in comments.split("|")] if comments != "" else []
        values.extend(args)
        values.extend(comments)
        self.content = [values]
        self.set_values(self.content)


class Arguments(TextBlock):
    ACTION_HEAD = "[Arguments]"

    pass


class Documentation(TextBlock):
    ACTION_HEAD = "[Documentation]"
    NO_ACTION_HEAD = "Documentation"

    def modify(self, content):
        if content is None:
            return
        cells_list = [[cell.strip()] for cell in content.split(TextBlock.ENTER)]
        cells_list[0] = cells_list[0][0].split("|")
        self.set_values(cells_list)

    def _query_of_action(self):
        first_line = self._values[0][1:]
        content = " | ".join(first_line) + TextBlock.ENTER
        for cell_list in self._values[1:]:
            content += " | ".join(cell_list) + TextBlock.ENTER
        return content.rstrip(TextBlock.ENTER)

    def _query_of_no_action(self):
        content = ""
        for cell_list in self._values:
            content += " | ".join(cell_list) + TextBlock.ENTER
        return content.rstrip(TextBlock.ENTER)


class Timeout(TextBlock):
    ACTION_HEAD = "[Timeout]"

    pass


class Teardown(TextBlock):
    ACTION_HEAD = "[Teardown]"

    pass


class Return(TextBlock):
    ACTION_HEAD = "[Return]"

    pass


class Func(TextBlock):

    def _anti_populate_of_action(self):
        cells, content = [], ""
        for value in self._values:
            cells.extend(value)
        cells = self._delete_empty_cell(cells)
        self._deal_comment_for_body(cells)
        valid_cells, comments = self._split_valid_cells_and_comments(cells)
        if len(valid_cells) == 0:
            return TextBlock.TAB + TextBlock.TAB.join(self._replace_empty_cells(comments)) + TextBlock.ENTER
        rows = len(valid_cells) // TextBlock.ROW_LIMIT + 1 if len(valid_cells) % TextBlock.ROW_LIMIT != 0 else len(
            valid_cells) // TextBlock.ROW_LIMIT
        for row_num in range(0, rows):
            tail = min(len(valid_cells), (row_num + 1) * TextBlock.ROW_LIMIT)
            cur_row_cells = cells[row_num * TextBlock.ROW_LIMIT: tail]
            if row_num == 0:
                content = TextBlock.TAB + TextBlock.TAB.join(self._replace_empty_cells(cur_row_cells))
            else:
                content += TextBlock.ENTER
                content += (TextBlock.TAB + TextBlock.CROSS_ROW + TextBlock.TAB + TextBlock.TAB.join(
                    self._replace_empty_cells(cur_row_cells)))
        if len(comments) != 0:
            content += TextBlock.TAB + TextBlock.TAB.join(self._replace_empty_cells(comments))
        return content + TextBlock.ENTER

    def _delete_empty_cell(self, row):
        for index in range(0, len(row)):
            cell = row[index]
            if cell != "":
                return row[index:]
        return []

    def _deal_comment_for_body(self, row):
        if len(row) >= 2 and row[0] == "\\" and row[1] == "Comment":
            row[0], row[1] = "Comment", "\\"

    def _split_valid_cells_and_comments(self, cells):
        for index in range(len(cells)):
            if cells[index].lower() == "comment" or cells[index].startswith("#"):
                return cells[:index], cells[index:]
        return cells, []

    def _query_of_action(self):
        content = []
        for cell_list in self._values:
            content.extend(cell_list)
        return [content]

    def _replace_empty_cells(self, cells):
        if len(cells) == 0:
            return cells
        for index in range(0, len(cells)):
            if cells[index] == "":
                cells[index] = "${EMPTY}"
        return cells


class FOR(Func):
    if ROBOT_VERSION == 'robot2':
        ACTION_HEAD = ": FOR"
    else:
        ACTION_HEAD = "FOR"
    ROW_LIMIT = 6

    def _is_cross_row(self, key):
        return key == "..."

    def populate(self, cells):
        if not cells:
            self._values.append([])
            return
        self._values.append(cells)

    def _anti_populate_of_action(self):
        self._format_head()
        if len(self._values) > 0:
            content = TextBlock.TAB + TextBlock.TAB.join(self._values[0])
        latest_line = []
        for index in range(1, len(self._values)):
            cells = self._values[index]
            if len(cells) > 0 and cells[0] == TextBlock.CROSS_ROW and latest_line != []:
                latest_line.extend(cells[1:])
                continue
            content += TextBlock.ENTER
            latest_line = copy.deepcopy(cells)
            content += self._format_row(latest_line)
        return content + TextBlock.ENTER

    def _query_of_action(self):
        self._format_head()
        content = [self._values[0]]
        for cell_list in copy.deepcopy(self._values[1:]):
            if len(content) > 1 and len(cell_list) > 0 and is_cross_row(cell_list[0]):
                content[-1].extend(cell_list[1:])
                continue
            if ROBOT_VERSION == 'robot2':
                cell_list.insert(0, "\\")
            else:
                cell_list.insert(0, "")
            content.append(cell_list)
        return content

    def _is_only_has_head(self):
        return len(self._values) == 1

    def _format_head(self):
        if len(self._values) > 0:
            head = self._values[0]
            if len(self._values[0]) > 0:
                if ROBOT_VERSION == 'robot2':
                    head[0] = ": FOR"
                else:
                    head[0] = "FOR"
            else:
                return
            for index in range(1, len(head)):
                value = head[index]
                tmp_variables = re.findall("{(.*)}", value)
                if len(tmp_variables) == 0:
                    format_str = " ".join(re.findall("\w+", head[index].upper()))
                    if format_str in ["IN", "IN RANGE"]:
                        head[index] = format_str

    def _format_row(self, cells):
        if ROBOT_VERSION == 'robot2':
            surfix = "\\"
        else:
            surfix = ""
        cells = self._delete_empty_cell(cells)
        valid_cells, comments = self._split_valid_cells_and_comments(cells)
        if len(valid_cells) == 0:
            return TextBlock.TAB + "" + TextBlock.TAB + TextBlock.TAB.join(self._replace_empty_cells(comments))
        content = ""
        rows = len(valid_cells) // FOR.ROW_LIMIT + 1 if len(valid_cells) % FOR.ROW_LIMIT != 0 else len(
            valid_cells) // FOR.ROW_LIMIT
        for row_num in range(0, rows):
            tail = min(len(valid_cells), (row_num + 1) * FOR.ROW_LIMIT)
            cur_row_cells = valid_cells[row_num * FOR.ROW_LIMIT: tail]
            if row_num == 0:
                content += (TextBlock.TAB + surfix + TextBlock.TAB + TextBlock.TAB.join(
                    self._replace_empty_cells(cur_row_cells)))
            else:
                content += TextBlock.ENTER
                content += (TextBlock.TAB + surfix + TextBlock.TAB + TextBlock.CROSS_ROW + TextBlock.TAB + TextBlock.TAB.join(
                    self._replace_empty_cells(cur_row_cells)))
        if len(comments) != 0:
            content += TextBlock.TAB + TextBlock.TAB.join(self._replace_empty_cells(comments))
        return content


class Setup(TextBlock):
    ACTION_HEAD = "[Setup]"

    pass


class Template(TextBlock):
    ACTION_HEAD = "[Template]"

    pass


class TestTemplate(TextBlock):
    NO_ACTION_HEAD = "Test Template"

    pass


class Comment(TextBlock):
    NO_ACTION_HEAD = "Comment"

    pass


class END(Func):
    ACTION_HEAD = "END"
    ROW_LIMIT = 1

    def _is_cross_row(self, key):
        return False

    def populate(self, cells):
        if not cells:
            self._values.append([])
            return
        self._values.append(cells)

    def _anti_populate_of_action(self):
        return "END" + TextBlock.ENTER

    def _query_of_action(self):
        return [["END"]]

    def _is_only_has_head(self):
        return True


if __name__ == "__main__":
    print(Setup()._format_attr_name())
    print(Setup().class_name)
