# encoding=utf-8
'''
Created on 2019年11月8日

@author: 10247557
'''
from utility.Reflection import Reflection
from utility.UIRepository import UIRepository

class EditCreator(object):

    @staticmethod
    def create(editor_class, *args):
        obj = UIRepository().find(editor_class)
        if obj:
            return obj
        else:
            obj =  Reflection().create_obj('controller.system_plugin.edit.view.' + editor_class, editor_class, *args)
            UIRepository().add(editor_class, obj)
            return obj

if __name__ == '__main__':
    a = EditCreator.create('EditPlugin', 'parent')
    print (a)