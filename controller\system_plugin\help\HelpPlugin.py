# coding=utf-8
'''
Created on 2024年12月19日

@author: AI Assistant
'''

from functools import partial
import webbrowser, os
import platform
import json
import requests
from threading import Thread

from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import <PERSON>A<PERSON>, Q<PERSON>ialog, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import QThread, pyqtSignal, Qt

from resources.Loader import Loader
from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader
from view.common.dialog.IntroductionDialog import IntroductionDialog
from plugins.SystemLibrary import SystemLibrary


class HelpPlugin(object):

    def __init__(self, parent):
        self._parent_window = parent

    def load(self):
        help_menu = self._parent_window.addMenu('&帮助')
        self._add_item_about_rfcode(help_menu)
        self._add_item_software_instruction(help_menu)
        self._add_item_check_update(help_menu)

    def _add_item_about_rfcode(self, help_menu):
        about_action = QAction(QIcon(''), LanguageLoader().get('ABOUT_RFCODE'), self._parent_window)
        about_action.triggered.connect(partial(self.open_dialog, self))
        help_menu.addAction(about_action)

    def _add_item_software_instruction(self, help_menu):
        instruction_action = QAction(QIcon(''), LanguageLoader().get('PRODUCTION_INSTRUCTION'), self._parent_window)
        instruction_action.triggered.connect(self.open_instruction)
        help_menu.addAction(instruction_action)

    def _add_item_check_update(self, help_menu):
        check_update_action = QAction(QIcon(''), '检查更新', self._parent_window)
        check_update_action.triggered.connect(self.show_update_dialog)
        help_menu.addAction(check_update_action)

    def get_icon(self):
        return Loader().get_relative_path('FILE')

    def _get_shortcut_key(self, key):
        return SystemSettings().get_value(key)

    def show_dialog(self):
        self._dialog.show()

    @staticmethod
    def open_dialog(this):
        message = LanguageLoader().get('ABOUT_RFCODE_DES')
        this._dialog = IntroductionDialog(LanguageLoader().get('ABOUT_RFCODE'), message)
        this._dialog.show()

    @staticmethod
    def open_instruction():
        webbrowser.open(Loader().get_path('PRODUCTION_INSTRUCTION'))

    def show_update_dialog(self):
        """显示检查更新对话框"""
        try:
            # 创建更新检查对话框
            self._update_dialog = UpdateDialog(self._parent_window)
            self._update_dialog.show()
        except Exception as e:
            print(f"显示更新对话框失败: {e}")
            import traceback
            traceback.print_exc()


# 版本检查URL
VERSION_URL = "http://10.2.71.213:31381/version?operatingSystem={}"

class UpdateDialog(QDialog):
    """更新检查对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("检查更新")
        self.setModal(True)
        self.resize(400, 200)
        self._init_ui()
        self._start_check()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        self._status_label = QLabel("正在检查更新...")
        layout.addWidget(self._status_label)

        self._update_button = QPushButton("立即更新")
        self._update_button.setVisible(False)
        self._update_button.clicked.connect(self._start_update)
        layout.addWidget(self._update_button)

        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

    def _start_check(self):
        """开始检查更新"""
        self._check_thread = CheckUpdateThread()
        self._check_thread.update_checked.connect(self._on_update_checked)
        self._check_thread.start()

    def _on_update_checked(self, updateDic):
        """更新检查完成回调"""
        if updateDic:
            print(updateDic)
            self._update_version = updateDic.get('version', '')
            self._update_info = updateDic.get('update_info', '')
            self._status_label.setText(f"发现新版本，可以更新：　{self._update_version}\n\n{self._update_info}")
            self._update_button.setVisible(True)
            self._download_url = updateDic.get('path', '')

        else:
            self._status_label.setText("当前已经是最新版本")

    def _start_update(self):
        """开始更新"""
        if hasattr(self, '_download_url'):
            self._status_label.setText("正在下载更新...")
            self._update_button.setVisible(False)

            # 启动更新线程
            update_thread = UpdateThread(self._download_url)
            update_thread.start()


class CheckUpdateThread(QThread):
    """检查更新线程"""
    update_checked = pyqtSignal(dict)

    def run(self):
        try:
            operating_system = platform.architecture()[0]
            response = requests.get(VERSION_URL.format(operating_system))
            content = json.loads(response.text)

            print("本地当前版本:", SystemLibrary.get_current_version())
            print("服务器最新版本:", content.get("version"))

            if content and content.get("error_info"):
                self.update_checked.emit("")
            elif content.get("version") > SystemLibrary.get_current_version():
                print("下载路径:", content.get("path"))
                self.update_checked.emit(content)
            else:
                self.update_checked.emit({})
        except Exception as e:
            print(f"检查更新失败: {e}")
            self.update_checked.emit({})

class UpdateThread(Thread):
    def __init__(self, down_load_path):
        super().__init__()
        self.down_load_path = down_load_path

    def run(self):
        resp = requests.get(self.down_load_path)
        name = self.down_load_path.split('/')[-1]
        tools_dir = os.path.join(os.path.dirname(__file__), "tools")
        os.makedirs(tools_dir, exist_ok=True)
        file_path = os.path.join(tools_dir, name)
        print("down_load_path", file_path)
        if resp.status_code == 200:
            with open(file_path, "wb") as file:
                file.write(resp.content)
            print("update file is ready")
            operatingType = platform.system()
            operatingSystem = platform.architecture()[0]
            print('operatingType is {}'.format(operatingType))
            print('operatingSystem is {}'.format(operatingSystem))
            if operatingType == "Windows":
                self.update_windows(file_path, operatingSystem)
            elif operatingType == "Linux":
                self.update_linux(file_path)
            else:
                print("not support")

    def update_windows(self, file_path, operatingSystem):
        tools_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools'))
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Update package not found at {file_path}")
        update_cmd = f'start "" "{file_path}"'
        print(f'start update:{update_cmd}')
        os.system(update_cmd)

    def update_linux(self, file_path):
        pass

    def _update_linux(self, file_path):
        """Linux更新"""
        print("Linux更新功能待实现")
