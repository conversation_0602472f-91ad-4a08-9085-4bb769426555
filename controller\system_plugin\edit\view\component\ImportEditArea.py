# encoding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''
import os

from PyQt5.Qt import <PERSON><PERSON>, <PERSON>Font, QCursor
from PyQt5.QtGui import QBrush, QColor
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QLabel, QPushButton, QVBoxLayout, \
    QTableWidget, QAbstractItemView, QTableWidgetItem, QMenu, QApplication

from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserF<PERSON>ry
from controller.system_plugin.edit.view.component.TableMenu import TableMenu
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
from controller.system_plugin.edit.view.component.table.TableItem import TableItem
from model.CurrentItem import CurrentItem
from model.data_file.env import VALID_LIB_NAMES
from settings.HistoryProject import HistoryProject
from view.common.dialog.ImportFailedHelp import Import<PERSON>ailedHelp
from view.common.dialog.LibraryDialog import Library<PERSON>ialog
from view.common.dialog.ResourceDialog import Resource<PERSON>ialog
from view.common.dialog.VariablesDialog import VariablesDialog
from view.explorer.tree_item.SpecifiedResourceJumper import SpecifiedResourceJumper


class _table(QTableWidget):

    def __init__(self, parent):
        super(_table, self).__init__(parent)

    def mousePressEvent(self, event):
        self._resource_jumper = SpecifiedResourceJumper()
        super().mousePressEvent(event)
        if event.button() == Qt.LeftButton:
            if QApplication.keyboardModifiers() == Qt.ControlModifier:
                row = self.currentItem().row()
                name = self.item(row, 1).text()
                path = self._resource_jumper.get_path(name).strip()
                self._resource_jumper.get_resource_item(path)


class ImportEditArea(object):

    def __init__(self, parent):
        self._parent = parent
        self._current_row = None

    def get_layout(self):
        return self._layout

    def load(self):
        self._set_table()
        self._label = QLabel('Add Import')
        self._set_library_btn()
        self._set_resource_btn()
        self._set_variable_btn()
        self._set_resource_btn()
        self._set_import_failed_help_btn()
        self._set_layout()
        self._select_row = -1

    def _set_layout(self):
        self._layout = QHBoxLayout()
        self._layout.addWidget(self._table)
        inner_layout = QVBoxLayout()
        inner_layout.addWidget(self._label, 0, Qt.AlignCenter)
        inner_layout.addWidget(self._library_btn, 0, Qt.AlignTop)
        inner_layout.addWidget(self._resource_btn, 0, Qt.AlignTop)
        inner_layout.addWidget(self._variable_btn, 0, Qt.AlignTop)
        inner_layout.addWidget(self._import_failed_help_btn, 1, Qt.AlignTop)
        self._layout.addLayout(inner_layout)

    def _set_table(self):
        self._table = _table(self._parent)
        self._table.setItemDelegate(TableItem())
#         self._table = QTableWidget(self._parent)
        self._table.setColumnCount(5)
        self._table.setRowCount(0)
        self._table.verticalHeader().setVisible(False)
        self._table.horizontalHeader().setStretchLastSection(True)
        self._table.setHorizontalHeaderLabels(['Import', 'Name/Path', 'Arguements', 'Alias', 'Comment'])
        self._table.setShowGrid(False)
        self._table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft)
        self._table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self._table.setSelectionMode(QAbstractItemView.SingleSelection)
        self._table.setStyleSheet("border:none;")
        self._table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self._table.doubleClicked.connect(self._show_dialog)
        self.table_menu = TableMenu(self)
        self.table_menu.load_shortcut()
        self._table.setContextMenuPolicy(Qt.CustomContextMenu)
        self._table.customContextMenuRequested.connect(self.generate_menu)

    def _set_library_btn(self):
        self._library_btn = QPushButton('Library', self._parent)
        self._library_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._library_btn.clicked.connect(self._show_library)

    def _show_library(self):
        self._library_dialog = LibraryDialog('Library')
        self._library_dialog.show()
        self._library_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _fill_one_row(self, info_list):
        if not info_list[1]:
            return
        rows = self._table.rowCount()
        if self._table.item(0, 0) and self._select_row < 0 or rows == 0:
            self._table.insertRow(rows)
        for i in range(len(info_list)):
            rows = self._table.rowCount()
            if self._select_row > -1:
                self._table.setItem(self._select_row, i, QTableWidgetItem(info_list[i]))
                self._current_row = self._select_row
            else:
                self._table.setItem(rows - 1, i, QTableWidgetItem(info_list[i]))
                self._current_row = rows - 1
        if info_list[0] == 'Resource':
            self._set_color(self._current_row, self._check_resource_is_useful(info_list[1]))
        else:
            if not self._check_library_is_useful(info_list[1]):
                self._set_red_color(self._current_row)
        self._modify_data()

    def _check_library_is_useful(self, name):
        path = SpecifiedResourceJumper().get_path(name)
        if path.endswith('.py'):
            if path[0] == CurrentItem().get()['path'][0] and os.path.exists(path):
                return True
            return False
        else:
            if name in VALID_LIB_NAMES:
                return True
            if os.path.exists(HistoryProject().read('PROJECT_PATH') +
                              os.path.sep + name.replace('.', os.path.sep) + '.py'):
                return True
            try:
                __import__(name)
                return True
            except:
                return False

    def _check_resource_is_useful(self, name):
        path = SpecifiedResourceJumper().get_path(name)
        if path.endswith('.tsv') or path.endswith('.txt') or path.endswith('.robot'):
            if path[0] == CurrentItem().get()['path'][0] and os.path.exists(path):
                return True
            return False
        else:
            return False

    def _set_color(self, row, is_useful):
        cols = self._table.columnCount()
        color = Colorizer()
        if is_useful:
            for c in range(cols):
                item = self._table.item(row, c)
                item.setForeground(color._get_color('keyword'))
                underline = QFont()
                underline.setUnderline(True)
                item.setFont(underline)
        else:
            self._set_red_color(row)

    def _set_red_color(self, row):
        cols = self._table.columnCount()
        for c in range(cols):
            self._table.item(row, c).setForeground(QBrush(QColor(255, 0, 0)))

    def _set_resource_btn(self):
        self._resource_btn = QPushButton('Resource', self._parent)
        self._resource_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._resource_btn.clicked.connect(self._show_resource)

    def _show_resource(self):
        self._resource_dialog = ResourceDialog('Resource')
        self._resource_dialog.show()
        self._resource_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _set_variable_btn(self):
        self._variable_btn = QPushButton('Variables', self._parent)
        self._variable_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._variable_btn.clicked.connect(self._show_variables)

    def _show_variables(self):
        self._variables_dialog = VariablesDialog('Variables')
        self._variables_dialog.show()
        self._variables_dialog.ok_pressed.connect(self._fill_one_row)
        self._select_row = -1

    def _set_import_failed_help_btn(self):
        self._import_failed_help_btn = QPushButton('Import Failed Help', self._parent)
        self._import_failed_help_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._import_failed_help_btn.clicked.connect(self._show_import_failed_help)

    def _show_import_failed_help(self):
        ImportFailedHelp().show()

    def _show_dialog(self):
        row = self._table.selectedIndexes()[0].row()
        if self._table.item(row, 0):
            text = self._table.item(row, 0).text()
            eval('self._show_' + text.lower() + '()')
            infos = [self._table.item(row, i).text() if self._table.item(row, i) else '' for i in range(1, 5)]
            eval('self._' + text.lower() + '_dialog.fill_data(%s)' % infos)
            self._select_row = row

    def reset_table(self):
        self._table.clearContents()
        self._table.setColumnCount(5)
        self._table.setRowCount(0)

    def fill_data(self, content):
        self.reset_table()
        if content:
            for row in range(len(content)):
                all_row = self._table.rowCount()
                if row == all_row:
                    self._table.insertRow(all_row)
                for column in range(len(content[row])):
                    item = QTableWidgetItem(content[row][column])
                    self._table.setItem(row, column, item)
                if content[row][0] == 'Resource':
                    self._set_color(row, self._check_resource_is_useful(content[row][1]))
                else:
                    if not self._check_library_is_useful(content[row][1]):
                        self._set_red_color(row)

    def get_data(self):
        all_row = self._table.rowCount()
        all_column = self._table.columnCount()
        result = []
        for row in range(all_row):
            line = []
            for column in range(all_column):
                if self._table.item(row, column):
                    line.append(self._table.item(row, column).text())
                else:
                    line.append('')
            result.append(line)
        return result

    def _modify_data(self):
        result = self.get_data()
        if self._current_row is not None:
            parsed_item = ItemParserFacory().create(CurrentItem().get()['type'] + 'Parser')
            parsed_item.modify('imports', {'table':result, 'index': self._current_row})

    def generate_menu(self, pos):
        if self._table.rowCount() > 0:
            self._menu = QMenu()
            self.table_menu.load_menu()
            self._menu.exec_(self._table.mapToGlobal(pos))
