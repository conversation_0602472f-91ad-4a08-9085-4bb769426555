# -*- coding: utf-8 -*-
# python
from openai import OpenAI
import httpx
import types
import collections
import types

class NebulacoderAPI:
    def __init__(self, api_key, api_base, model="nebulacoder-v5.2"):
        self.api_key = api_key
        self.api_base = api_base
        self.model = model
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.api_base,
            http_client=httpx.Client(
                trust_env=False,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            )
        )

    def create_chat_completion(self, messages, stream=True, **kwargs):
        return self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=stream,
            **kwargs
        )

    def handle_stream_response(self, response, callback=None):
        res = ''
        docId = ''
        try:
            for chunk in response:
                if not docId and chunk.id:
                    docId = chunk.id
                res += chunk.choices[0].delta.content
                if callback:
                    callback(chunk.choices[0].delta.content)
            return {'bo': {'docId': docId, 'result': res}}
        except Exception as err:
            print(f"Error: {err}")

    def handle_non_stream_response(self, response):
        try:
            print(response)
            print("\n\n")
            output = response.choices[0].message.content
            print(f"[Output]\n{output}")
        except Exception as err:
            print(f"Error: {err}")
    
    @staticmethod
    def get_llm_result(user, systemPrompt, prompt):
        api = NebulacoderAPI(
        api_key=user,
        api_base="http://nebulacoder.dev.zte.com.cn:40081/v1/"
        )
        response = api.create_chat_completion(
            messages=[{"role": "system", "content": systemPrompt}, {"role": "user", "content": prompt}],
            stream=True,
            max_tokens=10000,
            temperature=0.1
        )
        if isinstance(response, (types.GeneratorType, collections.Iterable)):
            return api.handle_stream_response(response)
        else:
            return api.handle_non_stream_response(response)

# 使用示例
if __name__ == "__main__":

    prompt_rule = """" 
<新用例开发规范>
你是一个经验丰富的软件测试专家和服务器测试专家，具备丰富的Robotframework自动化开发经验，能够参考已经实现用例操作步骤代码块，准确的开发出新用例。

为了开发出准确的用例脚本，Claude在开发用例时必须时刻优先考虑**诚实、准确、专业、严格**，同时在输出代码前能够进行自我检验准确性，确保输出的准确性和可用性，思考过程遵循<anthropic_thinking_protocol>。

如下是你开发新用例自动化的总体要求，需要严格遵守：
- 你分析用例开发过程时，需要用专业的软件测试知识分析测试用例，设计合理的Robotframework自动化结构；
- 你开发新用例所使用的关键字必须是已经在自动化框架中定义好的，或者实例脚本中已经使用过的关键字；
- 你不可以新增加或新创造关键字，新用例所使用的关键字都应该是框架已定义或内建关键字；

新用例开发请你按照**新用例分析**、**用例实现**、**结果检查**三个步骤及子步骤进行开发，输出格式为Robotframework代码。
## 1、新用例分析：
### 用例分析
> 为了更好的输出代码，优先对测试用例进行分析
- 使用5GNR和多模基站专业领域知识分析新用例测试操作步骤和预期结果的测试意图；
- 分析用例中的[操作][预期结果]，设计合理的测试自动化用例实现结构；
- 被测环境有设置或破坏的操作需要增加恢复；
- 注意识别用例中预置条件中的测试步骤；

### 代码块复用分析
> 为了确保代码输出引用的准确性，对每个测试步骤识别已有不是不可以  触发方式 填写方代码的利用情况
- 输出引用代码块的分析过程，查找时关注测试步骤意图，操作方法相似不是不可以  触发方式 填写方的地方；
- 复用分析输出格式:
```markdown
步骤x分析:
[操作]：%Documentation中操作%
[预期结果]：%Documentation中预期结果%
[分析结果]：%代码复用的原因%
[操作拆解]：%操作拆解步骤%
[可用关键字]:

## 2、用例实现
> 根据上述用例和代码块分析结果，按要求生成测试用例
### 自动化开发
- 按照用例分析结果，参考已有用例的风格设计合理的Robotframework代码结构；
- 按照代码块复用分析结果，引用准确的代码块和已定义的关键字完成脚本开发
- 新用例中[Documentation]中用例信息不需要修改和替换，保持原状；
- 使用LogStep在代码中标记处测试步骤|操作，格式为：LogStep    N <步骤|操作>，注意空格不要错了，与[Documentation]中操作描述保持一致；
- 根据<新用例>测试场景，添加合适的[Setup]和[Teardown]，不要设置[Timeout]；

### 结果输出要求
- 脚本符合robotframework3语法规范，如：FOR循环不能嵌套等；
- 输出格式为Robotframework代码，不能包含其他任何不符合语法规范的信息信息；
- 输出格式为Robotframework格式，兼容Python3.7；


## 3、结果检查
> 对已输出的新用例测试脚本进行自检，输出检查结果
### 基础检查
- [过程检查]回顾整个用例实现过程，排查是否按照用例分析、代码复用分析后，再生成代码，如有遗漏请说明并重新生成；
输出：
> ```markdown
过程检查       - ✔/✘
```
### **关键字检查**
> Claude在思考和仿写自动化用例时，有可能出现所谓的大模型幻觉，Claude应该避免这种情况，如Claude可能会自主设计开发新的关键字并在开发中引用，这是我不想要的结果，望Claude理解，希望通过如下方式回顾检查。
- 严格检查新用例引用的关键字在[case_step_x.tsv]、[keyword_des_x.tsv]文件中有定义或使用；
- 希望Claude检查时先读取项目里已有的关键字，找到文件中定义的关键字起始行，检查时不能为了追求返回速度而忽略了准确性
</新用例开发规范>
    """
    prompt = '''
__DX__:
## 角色扮演：
- 你是一名Robot Framework开发新手。

'''

    api = NebulacoderAPI(
        api_key="10124054",
        api_base="http://nebulacoder.dev.zte.com.cn:40081/v1/"
    )
    response = api.create_chat_completion(
        messages=[{"role": "system", "content": prompt_rule}, {"role": "user", "content": prompt}],
        stream=True,
        max_tokens=10000,
        temperature=0.1
    )
    if isinstance(response, (types.GeneratorType, collections.Iterable)):
        print(api.handle_stream_response(response))
    else:
        api.handle_non_stream_response(response)
