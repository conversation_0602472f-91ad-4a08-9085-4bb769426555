<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RF Helper项目开发时间线</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .timeline-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .timeline-header {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .timeline {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .timeline-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, #4CAF50, #2196F3, #FF9800, #E91E63, #9C27B0);
            border-radius: 2px;
            z-index: 1;
        }
        
        .milestone {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }
        
        .milestone-point {
            width: 20px;
            height: 20px;
            background: white;
            border: 4px solid;
            border-radius: 50%;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .milestone:nth-child(1) .milestone-point { border-color: #4CAF50; }
        .milestone:nth-child(2) .milestone-point { border-color: #2196F3; }
        .milestone:nth-child(3) .milestone-point { border-color: #FF9800; }
        .milestone:nth-child(4) .milestone-point { border-color: #E91E63; }
        .milestone:nth-child(5) .milestone-point { border-color: #9C27B0; }
        
        .milestone-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 180px;
            text-align: center;
            border-top: 4px solid;
        }
        
        .milestone:nth-child(1) .milestone-content { border-top-color: #4CAF50; }
        .milestone:nth-child(2) .milestone-content { border-top-color: #2196F3; }
        .milestone:nth-child(3) .milestone-content { border-top-color: #FF9800; }
        .milestone:nth-child(4) .milestone-content { border-top-color: #E91E63; }
        .milestone:nth-child(5) .milestone-content { border-top-color: #9C27B0; }
        
        .milestone-date {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .milestone-title {
            font-size: 14px;
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .milestone-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .arrow {
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid #9C27B0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            z-index: 3;
        }
        
        @media (max-width: 768px) {
            .timeline {
                flex-direction: column;
                gap: 30px;
            }
            
            .timeline-line {
                display: none;
            }
            
            .milestone-content {
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="timeline-container">
        <div class="timeline-header">
            智能录测技术落地规划
        </div>
        
        <div class="timeline">
            <div class="timeline-line"></div>
            
            <div class="milestone">
                <div class="milestone-point"></div>
                <div class="milestone-content">
                    <div class="milestone-date">2024.9</div>
                    <div class="milestone-title">项目启动</div>
                    <div class="milestone-desc">
                        需求收集<br>
                        领域目录收集<br>
                        项目启动
                    </div>
                </div>
            </div>
            
            <div class="milestone">
                <div class="milestone-point"></div>
                <div class="milestone-content">
                    <div class="milestone-date">2024.10</div>
                    <div class="milestone-title">基础开发</div>
                    <div class="milestone-desc">
                        RF助手优化<br>
                        AI开发规范制定<br>
                        星云6.0对接
                    </div>
                </div>
            </div>
            
            <div class="milestone">
                <div class="milestone-point"></div>
                <div class="milestone-content">
                    <div class="milestone-date">2025.2</div>
                    <div class="milestone-title">系统集成</div>
                    <div class="milestone-desc">
                        RF助手集成到<br>
                        RFCode中
                    </div>
                </div>
            </div>
            
            <div class="milestone">
                <div class="milestone-point"></div>
                <div class="milestone-content">
                    <div class="milestone-date">2025.3</div>
                    <div class="milestone-title">需求开发</div>
                    <div class="milestone-desc">
                        RFCode需求开发<br>
                        故障解决
                    </div>
                </div>
            </div>
            
            <div class="milestone">
                <div class="milestone-point"></div>
                <div class="milestone-content">
                    <div class="milestone-date">2025.5</div>
                    <div class="milestone-title">推广优化</div>
                    <div class="milestone-desc">
                        项目推广<br>
                        功能优化
                    </div>
                </div>
            </div>
            
            <div class="arrow"></div>
        </div>
    </div>
</body>
</html>