# encoding=utf-8
'''
Create on  2019年10月14日

@author:10240349
'''
import os

from utility.Singleton import Singleton

RESOURCE_CFG = \
    {
        'LOGO': 'images/rfcode.png',
        'FILE': 'images/folder.png',
        'OPEN_FILE': 'images/open_file.png',
        'FOLDER': 'images/folder.png',
        'TESTSUITE': 'images/testsuite.png',
        'PYFILE': 'images/pyfile.png',
        'RESOURCE': 'images/resource.png',
        'TESTCASE': 'images/testcase.png',
        'USERKEYWORD': 'images/keyword.png',
        'VARIABLE': 'images/variable.png',
        'START_BTN': 'images/start_btn.png',
        'STOP_BTN': 'images/stop_btn.png',
        'PAUSE_BTN': 'images/pause_btn.png',
        'CONTINUE_BTN': 'images/continue.png',
        'LOG_BTN': 'images/log.png',
        'PREPARE': 'images/preparing.png',
        'RUNNING': 'images/testcase_exec.png',
        'FAILED': 'images/testcase_failed.png',
        'PASSED': 'images/testcase_pass.png',
        'SAVE': 'images/save.png',
        'SAVE_ALL': 'images/save_all.png',
        'GO_BACK': 'images/back.png',
        'GO_FORWARD': 'images/forward.png',
        'HOME': 'images/home.png',
        'SEARCH': 'images/search.png',
        'SEARCH_LOADING': 'images/waiting.gif',
        'DETAILS': 'images/testcase.png',
        'ENVIROMENT_VARIABLE': 'images/env_variable.png',
        'GIT_BASH': 'images/git_bash.png',
        'START_GIT_SUCCESS': 'images/start_git_success.png',
        'UNTICKED': 'images/unticked.png',

        "DEFAULT_QSS": 'qss/default.qss',
        'PRODUCTION_INSTRUCTION': 'doc/RFCode使用说明书.docx',


    }


@Singleton
class Loader(object):

    @staticmethod
    def get_path(key):
        return os.path.abspath(os.path.dirname(__file__)) + '/' + RESOURCE_CFG[key]
