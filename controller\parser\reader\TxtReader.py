# encoding=utf-8
'''
Create on  2019年10月14日

@author: 10244931, 102443352, 10154402
'''
from codecs import BOM_UTF8
import sys
import traceback

from settings.SystemSettings import SystemSettings
ROBOT_VERSION = SystemSettings().read('ROBOT_VERSION')
import importlib
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing.tsvreader')
RobotReader = getattr(robot_module, 'RobotReader')
from robot.utils.utf8reader import Utf8Reader


from controller.parser.reader.Reader import Reader
from controller.parser.rules.CommParsedRule import is_blank_line
from model.data_file.Table import Table
from model.data_file.Table import Tables
from model.data_file.TableFactory import TableFactory
from model.data_file.TestCases import TestCases
from model.data_file.KeyWords import KeyWords
sys.setrecursionlimit(100000)


NBSP = u'\xA0'


class TxtReader(RobotReader):

    CELL_SEPERATOR = "    "

    def __init__(self, path):
        super().__init__()
        self._path = path

    def parse(self):
        try:
            return self._parse()
        except Exception as e:
            traceback.print_exc()
            self.format_2_utf8()
            return self._parse()

    def read(self, file):
        tables, table, path = Tables(), Table(), getattr(file, 'name', '<file-like object>')
        for lineno, line in enumerate(Utf8Reader(file).readlines(), start=1):
            cells = self.split_row(line.rstrip()) if not (isinstance(table, TestCases) or isinstance(table, KeyWords)) else self._split_row(line.rstrip())
            cells = list(self._check_deprecations(cells, path, lineno))
            if is_blank_line(cells):
                continue
            name = cells and cells[0].strip() or ''
            if name.startswith('*') and len(cells) == 1:
                block_type = name.replace("*", "").strip()
                table = TableFactory.get_instance(block_type)
                tables.append(table)
                continue
            table.add(cells)
        return tables.parse()

    def _parse(self):
        with open(self._path, "rb") as tsv_file:
            tables = self.read(tsv_file)
        return tables

    def _split_row(self, line):
        return line.split(TxtReader.CELL_SEPERATOR)

if __name__ == '__main__':
    #     import time
    # sleep(100)
    #     t1 = time.time()
    reader = TxtReader(r"D:\Demo\rf-ide\rf-ide\testcases\ut\test_file\t1.tsv")
    reader.parse()
#     table_list = []
#     for i in range(10000):
#         reader = TsvReader(r"D:\Demo\rf-ide\rf-ide\testcases\ut\test_file\t1.tsv")
#         table_list.append(reader.parse())
#     t2 = time.time()
#     print(t2 - t1)
#     time.sleep(100)
