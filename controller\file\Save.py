'''
Created on 2019年12月17日

@author: 10240349
'''
from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.parser.ItemParserFactory import ItemParserFacory
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from utility.ModifiedItemRepository import ModifiedItemRepository
from utility.PluginRepository import PluginRepository
from utility.ProjectTreeRepository import ProjectTreeRepository
from view.explorer.tree_item.ProjectTreeItem import ProjectTreeItem
from view.explorer.tree_item.PyItem import PyItem
from view.explorer.tree_item.ResourceItem import ResourceItem
from view.explorer.tree_item.SuiteItem import SuiteItem
from view.explorer.tree_item.TestcaseItem import TestcaseItem
from view.explorer.tree_item.UserKeywordItem import UserKeywordItem
from view.explorer.tree_item.VariableItem import VariableItem
from view.common.MessageBox import MessageBox
from settings.SystemSettings import SystemSettings
from robot.parsing import disable_curdir_processing
ROBOT_VERSION = SystemSettings().read('ROBOT_VERSION')
import importlib
robot_module = importlib.import_module(f'{ROBOT_VERSION}.parsing')
disable_curdir_processing = getattr(robot_module, 'disable_curdir_processing')


EDIT = LanguageLoader().get('EDIT')
TEXT_EDIT = LanguageLoader().get('TEXT_EDIT')
RUN = LanguageLoader().get('RUN')


class ScrollPositionManager:
    """滚动位置管理器，用于保存和恢复界面滚动位置"""

    def __init__(self):
        self.tree_scroll_position = None
        self.editor_scroll_position = None
        self.editor_cursor_position = None
        self.current_tab_index = None

    def save_positions(self):
        """保存当前的滚动位置"""
        try:
            # 保存项目树滚动位置
            self._save_tree_scroll_position()

            # 保存编辑器滚动位置
            self._save_editor_scroll_position()

            # 保存当前页签索引
            self._save_current_tab_index()

        except Exception as e:
            print(f"保存滚动位置时出错: {e}")

    def restore_positions(self):
        """恢复滚动位置"""
        try:
            # 恢复项目树滚动位置
            self._restore_tree_scroll_position()

            # 恢复编辑器滚动位置
            self._restore_editor_scroll_position()

            # 恢复页签索引
            self._restore_current_tab_index()

        except Exception as e:
            print(f"恢复滚动位置时出错: {e}")

    def _save_tree_scroll_position(self):
        """保存项目树滚动位置"""
        try:
            tree = ProjectTreeRepository().find("PROJECT_TREE")
            if tree:
                # 保存垂直滚动条位置
                v_scrollbar = tree.verticalScrollBar()
                if v_scrollbar:
                    self.tree_scroll_position = v_scrollbar.value()
                    print(f"已保存项目树滚动位置: {self.tree_scroll_position}")
        except Exception as e:
            print(f"保存项目树滚动位置时出错: {e}")

    def _restore_tree_scroll_position(self):
        """恢复项目树滚动位置"""
        try:
            if self.tree_scroll_position is not None:
                tree = ProjectTreeRepository().find("PROJECT_TREE")
                if tree:
                    # 使用QTimer延迟恢复，确保界面已更新
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(50, lambda: self._do_restore_tree_scroll())
        except Exception as e:
            print(f"恢复项目树滚动位置时出错: {e}")

    def _do_restore_tree_scroll(self):
        """实际执行项目树滚动位置恢复"""
        try:
            tree = ProjectTreeRepository().find("PROJECT_TREE")
            if tree and self.tree_scroll_position is not None:
                v_scrollbar = tree.verticalScrollBar()
                if v_scrollbar:
                    v_scrollbar.setValue(self.tree_scroll_position)
                    print(f"已恢复项目树滚动位置: {self.tree_scroll_position}")
        except Exception as e:
            print(f"执行项目树滚动位置恢复时出错: {e}")

    def _save_editor_scroll_position(self):
        """保存编辑器滚动位置"""
        try:
            text_editor = PluginRepository().find('TEXT_EDIT')
            if text_editor and hasattr(text_editor, 'SendScintilla'):
                # 保存第一个可见行
                # 使用数值常量代替导入
                self.editor_scroll_position = text_editor.SendScintilla(2152)  # SCI_GETFIRSTVISIBLELINE
                # 保存光标位置
                self.editor_cursor_position = text_editor.getCursorPosition()
                print(f"已保存编辑器滚动位置: {self.editor_scroll_position}, 光标位置: {self.editor_cursor_position}")
        except Exception as e:
            print(f"保存编辑器滚动位置时出错: {e}")

    def _restore_editor_scroll_position(self):
        """恢复编辑器滚动位置"""
        try:
            if self.editor_scroll_position is not None:
                text_editor = PluginRepository().find('TEXT_EDIT')
                if text_editor and hasattr(text_editor, 'SendScintilla'):
                    # 使用QTimer延迟恢复，确保内容已加载
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(100, lambda: self._do_restore_editor_scroll())
        except Exception as e:
            print(f"恢复编辑器滚动位置时出错: {e}")

    def _do_restore_editor_scroll(self):
        """实际执行编辑器滚动位置恢复"""
        try:
            text_editor = PluginRepository().find('TEXT_EDIT')
            if text_editor and hasattr(text_editor, 'SendScintilla'):
                # 恢复滚动位置
                if self.editor_scroll_position is not None:
                    text_editor.SendScintilla(2613, self.editor_scroll_position)  # SCI_SETFIRSTVISIBLELINE
                # 恢复光标位置
                if self.editor_cursor_position is not None:
                    text_editor.setCursorPosition(self.editor_cursor_position[0], self.editor_cursor_position[1])
                print(f"已恢复编辑器滚动位置: {self.editor_scroll_position}, 光标位置: {self.editor_cursor_position}")
        except Exception as e:
            print(f"执行编辑器滚动位置恢复时出错: {e}")

    def _save_current_tab_index(self):
        """保存当前页签索引"""
        try:
            edit_tab = PluginRepository().find('EDIT_TAB')
            if edit_tab:
                self.current_tab_index = edit_tab.currentIndex()
                print(f"已保存当前页签索引: {self.current_tab_index}")
        except Exception as e:
            print(f"保存当前页签索引时出错: {e}")

    def _restore_current_tab_index(self):
        """恢复当前页签索引"""
        try:
            if self.current_tab_index is not None:
                edit_tab = PluginRepository().find('EDIT_TAB')
                if edit_tab:
                    edit_tab.setCurrentIndex(self.current_tab_index)
                    print(f"已恢复当前页签索引: {self.current_tab_index}")
        except Exception as e:
            print(f"恢复当前页签索引时出错: {e}")


class Save(object):

    @staticmethod
    def save_all():
        # 保存滚动位置
        scroll_manager = ScrollPositionManager()
        scroll_manager.save_positions()

        _list = ModifiedItemRepository().find('MODIFIED_ITEM')
        if _list:
            if isinstance(_list, list):
                modified_item = [item for item in _list]
                Save._save_modified_item(modified_item)
            else:
                Save._save_modified_item([_list])

        # 恢复滚动位置
        scroll_manager.restore_positions()

    @staticmethod
    def _save_modified_item(modified_item):
        edit_frame = PluginRepository().find('EDIT_TAB')
        if modified_item:
            if edit_frame.tabText(edit_frame.currentIndex()) == TEXT_EDIT:
                Save._save_current_item_before_press_btn()
            for item in modified_item:
                Save._save_item(item)

    @staticmethod
    def _save_current_item_before_press_btn():
        """
        在按钮操作前保存当前项目的内容

        该方法用于在执行某些按钮操作之前，自动保存当前正在编辑的文件内容，
        确保用户在文本编辑器中的修改不会丢失。

        执行流程：
        1. 获取当前选中的项目（文件/测试用例等）
        2. 从文本编辑器插件中获取当前显示的文件内容
        3. 获取对应的数据文件对象
        4. 将编辑器中的内容更新到数据文件对象中

        注意：此方法不会触发文件的物理保存，只是更新内存中的数据对象
        """
        # 获取当前选中的项目（可能是测试用例、关键字、套件等）
        current_item = CurrentItem().get_current_item()

        # 从文本编辑器插件中获取当前屏幕显示的文件内容
        current_file_screen_content = PluginRepository().find('TEXT_EDIT').text()

        # 根据当前项目获取对应的数据文件对象
        data_file_obj = Save._get_data_file(current_item)

        # 将编辑器中的内容更新到数据文件对象中（内存更新，非物理保存）
        data_file_obj.update(current_file_screen_content)

    @staticmethod
    def save():
        # 保存滚动位置
        scroll_manager = ScrollPositionManager()
        scroll_manager.save_positions()

        item = CurrentItem().get_current_item()
        Save._save_item(item)
        SignalDistributor().format_save()

        # 恢复滚动位置
        scroll_manager.restore_positions()

    @staticmethod
    def _save_item(item):
        parent = item.parent()
        edit_frame = PluginRepository().find('EDIT_TAB')
        if edit_frame.tabText(edit_frame.currentIndex()) == TEXT_EDIT:
            Save._save_text_editor_file(item)
        elif edit_frame.tabText(edit_frame.currentIndex()) == EDIT:
            print('save_editor_file')
            Save._save_editor_file(item)
        if ModifiedItemRepository().find('MODIFIED_ITEM'):
            Save._remove_modified_item(item, parent, ModifiedItemRepository().find('MODIFIED_ITEM'))  # Remove the modified item from the repository after saving
        file_write_check = PluginRepository().find('FILE_WRITE_CHECK')
        if file_write_check and 'Permission' in file_write_check[0]:
            MessageBox().show_critical('no permission to write: '+PluginRepository().find('FILE_WRITE_CHECK')[1])

    @staticmethod
    def _clear_testcase(item):
        try:
            _list = item.get_child_list(TestcaseItem)
            for testcase in _list:
                ExecutiveTestCaseRepository().delete_testcase('EXECUTIVE_TESTCASES', testcase)
        except:
            pass

    @staticmethod
    def _save_text_editor_file(item):
        parent = item.parent()
        SignalDistributor().del_star_modify(item)
        current_item = CurrentItem().get_current_item()
        if current_item == item or current_item.parent() == item:
            Save._save_current_item(item, parent)
        else:
            Save._save_other_item(item, parent)

    @staticmethod
    def _save_current_item(item, parent):
        current_file_screen_content = PluginRepository().find('TEXT_EDIT').text()
        if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
            Save._save_data_file_with_update(item, current_file_screen_content)
        else:
            Save._save_data_file_with_update(parent, current_file_screen_content)
            CurrentItem().set_by_text(parent, item.text(0))

    @staticmethod
    def _save_other_item(item, parent):
        if isinstance(item, PyItem) or isinstance(item, SuiteItem) or \
                isinstance(item, ResourceItem) or isinstance(item, ProjectTreeItem):
            Save._save_data_file_without_update(item)
        elif isinstance(item, TestcaseItem) or isinstance(item, UserKeywordItem) or isinstance(item, VariableItem):
            Save._save_data_file_without_update(parent)

    @staticmethod
    @disable_curdir_processing
    def _save_data_file_with_update(item, current_file_screen_content):
        data_file_obj = Save._get_data_file(item)
        data_file_obj.update(current_file_screen_content)
        data_file_obj.save()
        item.refresh_children()

    @staticmethod
    @disable_curdir_processing
    def _save_data_file_without_update(item):
        Save._save_without_refresh(item)
        item.refresh_children()

    @staticmethod
    def _get_data_file(item):
        print('get_data_file')
        print(type(item).__name__ + 'Parser')
        parsed_item = ItemParserFacory().create(type(item).__name__ + 'Parser')
        return parsed_item.get_cur_data_file(item)

    @staticmethod
    def _remove_modified_item(item, parent, _list):
        # 处理_list可能是单个对象或列表的情况
        if _list is None or _list is False:
            return  # 没有修改的项目，直接返回

        # 确保_list是列表格式
        if not isinstance(_list, list):
            _list = [_list]  # 将单个对象转换为列表

        # 创建一个副本来避免在迭代时修改列表
        modified_list = _list.copy()

        if isinstance(item, SuiteItem) or isinstance(item, ResourceItem) \
                or isinstance(item, ProjectTreeItem) or isinstance(item, PyItem):
            if item in modified_list:
                modified_list.remove(item)
                print(f"已从修改列表中移除项目: {item.text(0)}")
        else:
            if parent and parent in modified_list:
                modified_list.remove(parent)  # 此时不能使用item.parent()获取父节点，因为item已保存，故item.parent()不是之前的父亲，故只能使用保存之前的父亲
                print(f"已从修改列表中移除父项: {parent.text(0)}")

        # 更新ModifiedItemRepository
        if len(modified_list) == 0:
            # 如果列表为空，删除整个键
            ModifiedItemRepository().delete('MODIFIED_ITEM')
            print("修改列表已清空")
        elif len(modified_list) == 1:
            # 如果只有一个项目，可以存储为单个对象
            ModifiedItemRepository().add('MODIFIED_ITEM', modified_list[0])
            print(f"修改列表更新为单个项目: {modified_list[0].text(0)}")
        else:
            # 如果有多个项目，存储为列表
            ModifiedItemRepository().add('MODIFIED_ITEM', modified_list)
            print(f"修改列表更新，剩余 {len(modified_list)} 个项目")

    @staticmethod
    def _save_editor_file(item):
        """
        Save the editor file and remove the star modification indicator.
        
        Args:
            item: The item to be saved.
        """
        Save._save_without_refresh(item)
        SignalDistributor().del_star_modify(item)

    @staticmethod
    def _save_run_file(item):
        Save._save_without_refresh(item)
        SignalDistributor().del_star_modify(item)
        SignalDistributor().format_save()

    @staticmethod
    @disable_curdir_processing
    def _save_without_refresh(item):
        data_file_obj = Save._get_data_file(item)
        data_file_obj.save()
