
import sys

import robot.utils.encoding


if sys.hexversion > 0x2060000:
    import json
    _JSONAVAIL = True
else:
    try:
        import simplej<PERSON> as json
        _JSONAVAIL = True
    except ImportError:
        _JSONAVAIL = False

try:
    import cPickle as pickle
except ImportError:
    import pickle as pickle

try:
    from cStringIO import <PERSON><PERSON>
except ImportError:
    from io import StringIO

HOST = "localhost"

try:
    from robot.running import EXECUTION_CONTEXTS

    def _is_logged(level):
        current = EXECUTION_CONTEXTS.current
        if current is None:
            return True
        out = current.output
        if out is None:
            return True
        return out._xmllogger._log_message_is_logged(level)
except ImportError:
    def _is_logged(level):
        from robot.output import OUTPUT
        if OUTPUT is None:
            return True
        return OUTPUT._xmllogger._log_message_is_logged(level)

# Setting Output encoding to UTF-8 and ignoring the platform specs
# RIDE will expect UTF-8
# Set output encoding to UTF-8 for piped output streams
robot.utils.encoding.OUTPUT_ENCODING = 'UTF-8'
# RF 2.6.3 and RF 2.5.7
robot.utils.encoding._output_encoding = robot.utils.encoding.OUTPUT_ENCODING


def dump(obj, fp):
    StreamHandler(fp).dump(obj)


def load(fp):
    return StreamHandler(fp).load()


def dumps(obj):
    fp = StringIO()
    StreamHandler(fp).dump(obj)
    return fp.getvalue()


def loads(s):
    fp = StringIO(s)
    return StreamHandler(fp).load()


class StreamHandler(object):

    loads = staticmethod(loads)
    dumps = staticmethod(dumps)

    def __init__(self, fp):
        if _JSONAVAIL:
            self._json_encoder = json.JSONEncoder(separators=(',', ':'),
                                                  sort_keys=True).encode
            self._json_decoder = json.JSONDecoder(strict=False).decode
        else:
            def json_not_impl(dummy):
                raise NotImplementedError(
                    'Python version < 2.6 and simplejson not installed. Please'
                    ' install simplejson.')
            self._json_decoder = staticmethod(json_not_impl)
            self._json_encoder = staticmethod(json_not_impl)
        self.fp = fp

    def dump(self, obj):
        write_list = []
        if _JSONAVAIL:
            write_list.append('J')
            s = self._json_encoder(obj)
            write_list.extend([str(len(s)), '|', s])
        else:
            write_list.append('P')
            s = pickle.dumps(obj, pickle.HIGHEST_PROTOCOL)
            write_list.extend([str(len(s)), '|', s])
        result = ''.join(write_list)
        self.fp.write(result.encode('utf-8'))

    def load(self):
        header = self._load_header()
        msgtype = header[0]
        msglen = header[1:]
        if not msglen.isdigit():
            raise DecodeError('Message header not valid: %r' % header)
        msglen = int(msglen)
        buff = StringIO()
        buff.write(self.fp.read(msglen))
        try:
            if msgtype == 'J':
                return self._json_decoder(buff.getvalue())
            elif msgtype == 'P':
                return pickle.loads(buff.getvalue())
            else:
                raise DecodeError("Message type %r not supported" % msgtype)
        except DecodeError.wrapped_exceptions as e:
            raise DecodeError(str(e))

    def _load_header(self):
        buff = StringIO()
        while len(buff.getvalue()) == 0 or buff.getvalue()[-1] != '|':
            recv_char = self.fp.read(1)
            if not recv_char:
                raise EOFError('File/Socket closed while reading load header')
            buff.write(recv_char)
        return buff.getvalue()[:-1]


class StreamError(Exception):

    """
    Base class for EncodeError and DecodeError
    """
    pass


class EncodeError(StreamError):
    wrapped_exceptions = (pickle.PicklingError,)


class DecodeError(StreamError):
    wrapped_exceptions = (pickle.UnpicklingError,)
    if _JSONAVAIL:
        if hasattr(json, 'JSONDecodeError'):
            wrapped_exceptions = (pickle.UnpicklingError, json.JSONDecodeError)
