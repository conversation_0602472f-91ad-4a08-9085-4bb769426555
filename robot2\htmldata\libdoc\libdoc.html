<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="-1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="Generator" content="">
<link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKcAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAAqAAAAAAAAAAAAAAAAAAAALIAAAD/AAAA4AAAANwAAADcAAAA3AAAANwAAADcAAAA3AAAANwAAADcAAAA4AAAAP8AAACxAAAAAAAAAKYAAAD/AAAAuwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC/AAAA/wAAAKkAAAD6AAAAzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN8AAAD/AAAA+gAAAMMAAAAAAAAAAgAAAGsAAABrAAAAawAAAGsAAABrAAAAawAAAGsAAABrAAAADAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAIsAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAANEAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAAAAAAAAMgAAADIAAAAyAAAAMgAAADIAAAAyAAAAMgAAADIAAAAFAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAADwAAAB8AAAAAAAAAGAAAABcAAAAAAAAAH8AAABKAAAAAAAAAAAAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAADCAAAA/wAAACkAAADqAAAA4QAAAAAAAAD7AAAA/wAAALAAAAAGAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAIwAAAP4AAAD/AAAA/wAAAGAAAAAAAAAAAAAAAMkAAAD/AAAAigAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAAAAAAAIAAAAcAAAABkAAAAAAAAAAAAAAAAAAAAAAAAAEgAAAAAAAAAAAAAA2gAAAP8AAAD7AAAAywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN4AAAD/AAAAqwAAAP8AAACvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALIAAAD/AAAAsgAAAAAAAAC5AAAA/wAAAMoAAADAAAAAwAAAAMAAAADAAAAAwAAAAMAAAADAAAAAwAAAAMkAAAD/AAAAvAAAAAAAAAAAAAAAAAAAAKwAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAArQAAAAAAAAAAwAMAAIABAAAf+AAAP/wAAD/8AAAgBAAAP/wAAD/8AAA//AAAJIwAADHEAAA//AAAP/wAAB/4AACAAQAAwAMAAA==">
<link rel="stylesheet" type="text/css" href="libdoc.css" media="all">
<link rel="stylesheet" type="text/css" href="pygments.css" media="all">
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<link rel="stylesheet" type="text/css" href="../common/js_disabled.css" media="all">
<link rel="stylesheet" type="text/css" href="../common/doc_formatting.css" media="all">
<script type="text/javascript" src="../rebot/util.js"></script> <!-- TODO: Should util.js be moved under common? -->
<script type="text/javascript" src="../lib/jquery.min.js"></script>
<script type="text/javascript" src="../lib/jquery.tmpl.min.js"></script>
<script type="text/javascript" src="../lib/jquery.highlight.min.js"></script>
<!-- JS MODEL --><script type="text/javascript" src="../testdata/libdoc.js"></script>
<title></title>
</head>
<body>

<div id="javascript-disabled">
  <h1>Opening library documentation failed</h1>
  <ul>
    <li>Verify that you have <b>JavaScript enabled</b> in your browser.</li>
    <li>Make sure you are using a <b>modern enough browser</b>. If using Internet Explorer, version 8 or newer is required.</li>
    <li>Check are there messages in your browser's <b>JavaScript error log</b>. Please report the problem if you suspect you have encountered a bug.</li>
  </ul>
</div>

<script type="text/javascript">
    // Not using jQuery here for speed and to support ancient browsers.
    document.getElementById('javascript-disabled').style.display = 'none';
</script>

<script type="text/javascript">
    $(document).ready(function() {
        parseTemplates();
        document.title = libdoc.name;
        renderTemplate('base', libdoc, $('body'));
        if (libdoc.inits.length) {
            renderTemplate('importing', libdoc);
        }
        renderTemplate('shortcuts', libdoc);
        if (libdoc.contains_tags) {
            renderTemplate('tags', libdoc);
        }
        renderTemplate('keywords', libdoc);
        renderTemplate('footer', libdoc);
        scrollToHash();
        $(document).bind('keydown', handleKeydown);
    });

    function parseTemplates() {
        $('script[type="text/x-jquery-tmpl"]').map(function (idx, elem) {
            $.template(elem.id, elem.text);
        });
    }

    function renderTemplate(name, arguments, container) {
        if (!container) {
            container = $('#' + name + '-container');
            container.empty();
        }
        if (!arguments.search) {
            arguments.search = false;
        }
        $.tmpl(name + '-template', arguments).appendTo(container);
    }

    function handleKeydown(event) {
        event = event || window.event;
        var keyCode = event.keyCode || event.which;
        if (keyCode === 27)  // esc
            setTimeout(closeSearch, 0);
        if (keyCode === 83 && $('#search').is(':hidden'))  // s
            setTimeout(openSearch, 0);
    }

    function scrollToHash() {
        if (window.location.hash) {
            var hash = window.location.hash.substring(1).replace('+', ' ');
            window.location.hash = '';
            window.location.hash = hash;
        }
    }

    function tagSearch(tag) {
        var include = {tags: true, tagsExact: true};
        markMatches(tag, include);
        highlightMatches(tag, include);
        $('#keywords-container').find('.kw-row').addClass('hide-unmatched');
    }

    function doSearch() {
        var string = $('#search-string').val();
        var include = getIncludesAndDisableIfOnlyOneLeft();
        if (string) {
            markMatches(string, include);
            highlightMatches(string, include);
            setMatchVisibility();
        } else {
            resetKeywords();
        }
    }

    function getIncludesAndDisableIfOnlyOneLeft() {
        var name = $('#include-name');
        var args = $('#include-args');
        var doc = $('#include-doc');
        var tags = $('#include-tags');
        var include = {name: name.prop('checked'),
                       args: args.prop('checked'),
                       doc: doc.prop('checked'),
                       tags: !!tags.prop('checked')};
        if ((!include.name) && (!include.args) && (!include.doc)) {
            tags.prop('disabled', true);
        } else if ((!include.name) && (!include.args) && (!include.tags)) {
            doc.prop('disabled', true);
        } else if ((!include.name) && (!include.doc) && (!include.tags)) {
            args.prop('disabled', true);
        } else if ((!include.args) && (!include.doc) && (!include.tags)) {
            name.prop('disabled', true);
        } else {
            name.prop('disabled', false);
            args.prop('disabled', false);
            doc.prop('disabled', false);
            tags.prop('disabled', false);
        }
        return include;
    }

    function markMatches(pattern, include) {
        pattern = util.regexpEscape(pattern);
        if (include.tagsExact) {
            pattern = '^' + pattern + '$';
        }
        var regexp = new RegExp(pattern, 'i');
        var test = regexp.test.bind(regexp);
        var result = {contains_tags: libdoc.contains_tags};
        var matchCount = 0;
        result.keywords = util.map(libdoc.keywords, function (kw) {
            kw = $.extend({}, kw);
            kw.matched = (include.name && test(kw.name) ||
                          include.args && test(kw.args) ||
                          include.doc && test($(kw.doc).text()) ||
                          include.tags && util.any(util.map(kw.tags, test)));
            if (kw.matched)
                matchCount++;
            return kw
        });
        renderTemplate('shortcuts', result);
        renderTemplate('keywords', result);
        if (libdoc.contains_tags) {
            renderTemplate('tags', libdoc);
        }
        var ending = matchCount != 1 ? 's.' : '.';
        $('#match-count').show().text(matchCount + ' matched keyword' + ending);
        $('#altogether-count').hide();
        if (matchCount == 0)
            $('#keywords-container').find('table').empty();
    }

    function highlightMatches(string, include) {
        var shortcuts = $('#shortcuts-container').find('.match');
        var keywords = $('#keywords-container').find('.match');
        if (include.name) {
            shortcuts.highlight(string);
            keywords.find('.kw').highlight(string);
        }
        if (include.args) {
            keywords.find('.args').highlight(string);
        }
        if (include.doc) {
            keywords.find('.doc').highlight(string);
        }
        if (include.tags) {
            var matches = keywords.find('.tags').find('a').add(
                    $('#tags-container').find('a'));
            if (include.tagsExact) {
                matches = matches.filter(function (index, tag) {
                    return $(tag).text().toUpperCase() == string.toUpperCase();
                });
            }
            matches.highlight(string);
        }
    }

    function openSearch() {
        $('#search').show();
        $('#open-search').hide();
        $('#search-string').focus().select();
        $(document).scrollTop($("#Shortcuts").offset().top);
    }

    function closeSearch() {
        $('#search').hide();
        $('#open-search').show();
    }

    function resetSearch() {
        $('#search-string').val('');
        $('#include-name').prop('checked', true);
        $('#include-args').prop('checked', true);
        $('#include-doc').prop('checked', true);
        $('#hide-unmatched').prop('checked', false);
        resetKeywords();
    }

    function resetKeywords() {
        renderTemplate('shortcuts', libdoc);
        renderTemplate('keywords', libdoc);
        if (libdoc.contains_tags) {
            renderTemplate('tags', libdoc);
        }
        $('#match-count').hide();
        $('#altogether-count').show();
    }

    function setMatchVisibility() {
        var kws = $('#keywords-container').find('.kw-row');
        var hide = $('#hide-unmatched').prop('checked');
        kws.toggleClass('hide-unmatched', hide);
    }

    // http://stackoverflow.com/a/18484799
    var delay = (function () {
        var timer = 0;
        return function(callback, ms) {
            clearTimeout(timer);
            timer = setTimeout(callback, ms);
        };
    })();

</script>

<script type="text/x-jquery-tmpl" id="base-template">
    <h1>${name}</h1>
    <table class="metadata">
        {{if version}}<tr><th>Library version:</th><td>${version}</td></tr>{{/if}}
        {{if scope}}<tr><th>Library scope:</th><td>${scope}</td></tr>{{/if}}
        <tr><th>Named arguments:</th><td>{{if named_args}}supported{{else}}not supported{{/if}}</td></tr>
    </table>
    <div id="introduction-container">
        <h2 id="Introduction">Introduction</h2>
        <div class="doc">{{html doc}}</div>
    </div>
    <div id="importing-container"></div>
    <div id="shortcuts-container"></div>
    <div id="tags-container"></div>
    <div id="keywords-container"></div>
    <div id="footer-container"></div>
    <form id="search" action="javascript:void(0)">
        <fieldset>
            <legend id="search-title">Search keywords</legend>
            <input type="text" id="search-string" onkeyup="delay(doSearch, 500)">
            <fieldset>
                <legend>Search from</legend>
                <input type="checkbox" id="include-name" onclick="doSearch()" checked>
                <label for="include-name">Name</label>
                <input type="checkbox" id="include-args" onclick="doSearch()" checked>
                <label for="include-args">Arguments</label>
                <input type="checkbox" id="include-doc" onclick="doSearch()" checked>
                <label for="include-doc">Documentation</label>
                {{if libdoc.contains_tags}}
                <input type="checkbox" id="include-tags" onclick="doSearch()" checked>
                <label for="include-tags">Tags</label>
                {{/if}}
            </fieldset>
            <input type="checkbox" id="hide-unmatched" onclick="setMatchVisibility()" checked>
            <label for="hide-unmatched">Hide unmatched keywords</label>
            <div id="search-buttons">
                <input type="button" value="Reset" onclick="resetSearch()"
                       title="Reset search">
                <input type="button" value="Close" onclick="closeSearch()"
                       title="Close search (shortcut: <Esc>)">
            </div>
        </fieldset>
    </form>
    <div id="open-search" onclick="openSearch()" title="Search keywords (shortcut: s)"></div>
</script>

<script type="text/x-jquery-tmpl" id="importing-template">
    <h2 id="Importing">Importing</h2>
    <table border="1" class="keywords">
        <tr>
            <th class="args">Arguments</th>
            <th class="doc">Documentation</th>
        </tr>
        {{each inits}}
        <tr class="kw-row">
            <td class="args">
            {{each args}}
              <span>${$value}</span>{{if $index < args.length-1}}, {{/if}}
            {{/each}}
            </td>
            <td class="doc">{{html $value.doc}}</td>
        </tr>
        {{/each}}
    </table>
</script>

<script type="text/x-jquery-tmpl" id="shortcuts-template">
    <h2 id="Shortcuts">Shortcuts</h2>
    <div class='shortcuts'>
        {{each keywords}}
        <a href="#${encodeURIComponent($value.name)}"
           class="{{if $value.matched === false}}no-{{/if}}match"
           title="${$value.shortdoc}">${$value.name}</a>
        {{if $index < keywords.length-1}} &middot; {{/if}}
        {{/each}}
    </div>
</script>

<script type="text/x-jquery-tmpl" id="tags-template">
    <h2 id="Tags">Tags</h2>
    <div class='shortcuts'>
        {{each all_tags}}
        <a href="javascript:tagSearch('${$value}')"
           title="Show tests with this tag">${$value}</a> &middot;
        {{/each}}
        <a href="javascript:resetKeywords()" class="normal-first-letter"
           title="Show all tests">[Reset]</a>
    </div>
</script>

<script type="text/x-jquery-tmpl" id="keywords-template">
    <h2 id="Keywords">Keywords</h2>
    <table border="1" class="keywords">
        <tr>
            <th class="kw">Keyword</th>
            <th class="args">Arguments</th>
            {{if libdoc.contains_tags}}
            <th class="tags">Tags</th>
            {{/if}}
            <th class="doc">Documentation</th>
        </tr>
        {{each keywords}}
            {{tmpl($value) 'keyword-template'}}
        {{/each}}
    </table>
</script>

<script type="text/x-jquery-tmpl" id="keyword-template">
    <tr class="kw-row {{if matched === false}}no-{{/if}}match">
        <td class="kw">
            <a name="${name}" href="#${encodeURIComponent(name)}"
               title="Link to this keyword">${name}</a>
        </td>
        <td class="args">
            {{each args}}
            <span>${$value}</span>{{if $index < args.length-1}}, {{/if}}
            {{/each}}
        </td>
        {{if libdoc.contains_tags}}
        <td class="tags">
            {{each tags}}
            <a href="javascript:tagSearch('${$value}')"
               title="Show tests with this tag">${$value}</a>{{if $index < tags.length-1}}, {{/if}}
            {{/each}}
        </td>
        {{/if}}
        <td class="doc">{{html doc}}</td>
    </tr>
</script>


<script type="text/x-jquery-tmpl" id="footer-template">
    <p class="footer">
        <span id="altogether-count">Altogether ${keywords.length} keywords.</span>
        <span id="match-count"></span>
        <br>
        Generated by <a href="http://robotframework.org/robotframework/#built-in-tools">Libdoc</a> on ${generated}.
    </p>
</script>

</body>
</html>
