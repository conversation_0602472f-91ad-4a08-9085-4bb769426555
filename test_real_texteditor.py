#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真实TextEditor的双击事件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟必要的依赖
class MockPluginRepository:
    def __init__(self):
        self.text_editor = None

    def find(self, name):
        if name == 'PROJECT_EXPLORER':
            return MockProjectExplorer()
        elif name == 'TEXT_EDIT':
            return self.text_editor
        return None

    def set_text_editor(self, editor):
        self.text_editor = editor

# 创建全局实例
mock_plugin_repo = MockPluginRepository()

class MockProjectExplorer:
    def expand_item_by_path(self, file_path, item_name, is_reload_file, update_editor):
        print(f"MockProjectExplorer.expand_item_by_path called: {file_path}, {item_name}, {is_reload_file}, {update_editor}")
        # 模拟找到子项并更新编辑器
        if update_editor and item_name:
            print(f"模拟找到并选中子项: {item_name}")
            # 模拟调用text_editor.refresh_content_and_select_testcase
            text_editor = MockPluginRepository().find('TEXT_EDIT')
            if text_editor:
                text_editor.refresh_content_and_select_testcase(item_name, is_reload_file)

class MockCurrentItem:
    def get(self):
        return {"path": "/test/path.robot"}

# 替换原始的导入
sys.modules['utility.PluginRepository'] = type(sys)('utility.PluginRepository')
sys.modules['utility.PluginRepository'].PluginRepository = MockPluginRepository
sys.modules['model.CurrentItem'] = type(sys)('model.CurrentItem')
sys.modules['model.CurrentItem'].CurrentItem = MockCurrentItem

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

try:
    from controller.system_plugin.text_edit.TextEditor import TextEditor
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("真实TextEditor双击事件测试")
            self.setGeometry(100, 100, 800, 600)
            
            # 创建中央widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 添加说明标签
            info_label = QLabel("请双击编辑器中的任意行来测试双击事件。查看控制台输出。")
            layout.addWidget(info_label)
            
            # 创建真实的TextEditor
            self.text_editor = TextEditor()
            layout.addWidget(self.text_editor)
            
            # 设置一些测试文本
            test_content = """Test Case 1
    Log    This is a test step
    Should Be Equal    ${var}    expected

Test Case 2
    Log    Another test step
    Should Contain    ${text}    substring

*** Keywords ***
My Custom Keyword
    Log    This is a custom keyword
    Return From Keyword    success
"""
            self.text_editor.setText(test_content)
            
            print("真实TextEditor测试窗口已创建，请尝试双击文本编辑器中的行")

    def main():
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = TestWindow()
        window.show()
        
        print("应用程序已启动")
        print("请双击编辑器中的任意行来测试双击事件")
        print("查看控制台输出以确认事件是否被触发")
        
        sys.exit(app.exec_())

except Exception as e:
    print(f"无法导入TextEditor: {e}")
    print("使用简化版本进行测试")
    
    from PyQt5.Qsci import QsciScintilla
    
    class SimpleTextEditor(QsciScintilla):
        def __init__(self):
            super().__init__()
            self.setUtf8(True)
            self.setMouseTracking(True)
            self.setFocusPolicy(Qt.StrongFocus)
            self._file_path = "/test/path.robot"
            
        def mousePressEvent(self, event):
            print(f"mousePressEvent: button={event.button()}")
            super().mousePressEvent(event)
            
        def mouseDoubleClickEvent(self, event):
            print(f"双击事件被触发: button={event.button()}, pos={event.pos()}")
            
            if event.button() == Qt.LeftButton:
                line, _ = self.getCursorPosition()
                line_text = self.text(line)
                print(f"双击行号: {line}, 行内容: '{line_text}'")
                
                if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                    item_name = line_text.strip()
                    if item_name:
                        print(f"模拟展开路径: {item_name}")
                        self.expand_by_path(False)
            
            super().mouseDoubleClickEvent(event)
            
        def expand_by_path(self, is_reload_file=True):
            print(f"expand_by_path called with is_reload_file={is_reload_file}")
            # 模拟真实的expand_by_path行为
            line_from, _, line_to, _ = self.getSelection()
            if line_from == line_to:
                line_text = self.text(line_from)
                if not line_text.startswith((' ', '\t')) and not line_text.startswith('['):
                    item_name = line_text.strip()
                    print(f"expand_by_path: 项目名称 '{item_name}'")
                    # 模拟调用PROJECT_EXPLORER
                    mock_explorer = MockProjectExplorer()
                    mock_explorer.expand_item_by_path(self._file_path, item_name, is_reload_file, False)
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("简化TextEditor双击事件测试")
            self.setGeometry(100, 100, 800, 600)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            
            info_label = QLabel("请双击编辑器中的任意行来测试双击事件。查看控制台输出。")
            layout.addWidget(info_label)
            
            self.text_editor = SimpleTextEditor()
            layout.addWidget(self.text_editor)
            
            test_content = """Test Case 1
    Log    This is a test step
    Should Be Equal    ${var}    expected

Test Case 2
    Log    Another test step
    Should Contain    ${text}    substring

*** Keywords ***
My Custom Keyword
    Log    This is a custom keyword
    Return From Keyword    success
"""
            self.text_editor.setText(test_content)
            print("简化TextEditor测试窗口已创建")

    def main():
        app = QApplication(sys.argv)
        window = TestWindow()
        window.show()
        print("应用程序已启动")
        sys.exit(app.exec_())

if __name__ == "__main__":
    main()
