'''
Created on 2019年10月31日

@author: 10247557,10225935,10259183
'''
from PyQt5.QtWidgets import QTextEdit, QLineEdit
from PyQt5.QtCore import pyqtSignal, QObject
from iplatform.highlight.LogHighLighter import LogHighLighter
from controller.system_plugin.style.ThemeManager import ThemeManager


class LogTextEditor(QObject):
    theme_changed = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.theme_manager.theme_changed.connect(self._update_background)
        self._update_background()

    def _update_background(self):
        theme_id = self.theme_manager.get_current_theme()
        if theme_id == 'cool_blue':
            bg_color = "#E0EFFF"
        else:
            bg_color = "#FFFFFF" if not self.theme_manager.is_dark_theme() else "#2D2D2D"
        if hasattr(self, '_log_summary_editor'):
            self._log_summary_editor.setStyleSheet(f"background-color: {bg_color};")
            self._log_trace_editor.setStyleSheet(f"background-color: {bg_color};")
        else:
            self._log_summary_editor = QTextEdit()
            self._log_summary_editor.setStyleSheet(f"background-color: {bg_color};")
            self._log_summary_editor.setLineWrapMode(QTextEdit.NoWrap)
            self._log_trace_editor = QTextEdit()
            self._log_trace_editor.setStyleSheet(f"background-color: {bg_color};")
        self._log_trace_editor.setLineWrapMode(QTextEdit.NoWrap)
#         self._log_summary_editor.moveCursor(QTextCursor.End)
        self._log_summary_editor.setReadOnly(True)
        self._log_summary_editor.highlighter = LogHighLighter(self._log_summary_editor)
        self._log_trace_editor.highlighter = LogHighLighter(self._log_trace_editor)
        self._progress_editor = QLineEdit()
        self._progress_editor.setReadOnly(True)

    def get_log_summary_editor(self):
        return self._log_summary_editor

    def get_log_trace_editor(self):
        return self._log_trace_editor

    def get_progress_editor(self):
        return self._progress_editor
