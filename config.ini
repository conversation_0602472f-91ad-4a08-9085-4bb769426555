[General]
ERROR = 255, 0, 0
KEYWORD = 25, 105, 225
BACKGROUND = 255, 255, 255
TEXT = 0, 0, 0
VARIABLE = 34, 139, 34
FONT = Consolas
VERSION = RFCODE_20250723095125
SIZE = 8
LANGUAGE = ZH
PROJECT_PATH = 
LAST_CLICKED_PATH = D:\\autoframework\\mergecitestcases\\STC-BMCv3-COMM-FAN-1100_BMCV3\x652f\x6301\x786c\x76d8\x6e29\x5ea6\x7eb3\x5165\x6563\x70ed\x63a7\x5236\x7b56\x7565.robot\\STC-BMCv3-COMM-FAN-1101 IPMI\x547d\x4ee4\x914d\x7f6e\x673a\x68b0\x76d8\x8d77\x8c03\x548c\x5168\x901f\x95e8\x9650\x6d4b\x8bd5
DEFAULT_ARGUMENT = --loglevel trace
IS_LOAD_RF_ASSISTANT = false
PYTHON_VER = 3
ROBOT_VERSION = robot
UI_THEME = cool_blue
TRACE_LOG_COUNT = inf

