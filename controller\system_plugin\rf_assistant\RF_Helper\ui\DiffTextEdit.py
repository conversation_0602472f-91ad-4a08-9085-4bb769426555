# -*- coding: utf-8 -*-
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
import difflib


class DiffTextEdit(QTextBrowser):
    def __init__(self):
        super().__init__()
        # 应用主题样式
        self._apply_theme()

    def _get_theme_colors(self):
        """获取主题相关的颜色配置"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()

            if current_theme == 'dark':
                return {
                    'default_bg': '#353535',
                    'default_text': '#E0E0E0',
                    'comment_bg': '#4A4A4A',
                    'comment_text': '#B0B0B0',
                    'log_bg': '#2E4A6B',
                    'log_text': '#87CEFA',
                    'keyword_text': '#66D9EF',
                    'variable_text': '#A6E22E',
                    'section_text': '#F92672',
                    'string_text': '#E6DB74',
                }
            else:  # light theme
                return {
                    'default_bg': '#FFFFFF',
                    'default_text': '#000000',
                    'comment_bg': '#FFE4E1',
                    'comment_text': '#8B0000',
                    'log_bg': '#E6F3FF',
                    'log_text': '#0066CC',
                    'keyword_text': '#0000FF',
                    'variable_text': '#008000',
                    'section_text': '#800080',
                    'string_text': '#B8860B',
                }
        except Exception as e:
            print(f"获取主题颜色失败: {e}")
            # 返回默认浅色主题颜色
            return {
                'default_bg': '#FFFFFF',
                'default_text': '#000000',
                'comment_bg': '#FFE4E1',
                'comment_text': '#8B0000',
                'log_bg': '#E6F3FF',
                'log_text': '#0066CC',
                'keyword_text': '#0000FF',
                'variable_text': '#008000',
                'section_text': '#800080',
                'string_text': '#B8860B',
            }

    def _apply_theme(self):
        """应用主题样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors()

            # 设置编辑器背景和文字颜色
            self.setStyleSheet(f"""
                QTextEdit {{
                    background-color: {colors['editor_bg']};
                    color: {colors['editor_text']};
                    border: 1px solid {colors['border_color']};
                    border-radius: 4px;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 12px;
                }}
                QTextEdit:focus {{
                    border-color: #4A90E2;
                }}
            """)
        except Exception as e:
            print(f"应用DiffTextEdit主题失败: {e}")

    def set_colored_diff(self, text1, text2):
        diff = difflib.ndiff(text1.splitlines(keepends=True), text2.splitlines(keepends=True))
        for line in diff:
            fmt = QTextCharFormat()
            if line.startswith('-'):
                fmt.setBackground(QColor('pink'))
                self.append_colored_line(line, fmt)
            elif line.startswith('+'):
                fmt.setBackground(QColor('lightgreen'))
                self.append_colored_line(line, fmt)
            elif line.startswith('?'):
                continue
            else:
                fmt.setBackground(QColor('white'))
                self.append_colored_line(line, fmt)

    def set_colored_text(self, texts):
        # 获取主题颜色
        colors = self._get_theme_colors()

        for lines in texts:
            fmt = QTextCharFormat()
            line = lines.strip()
            if line.startswith('Comment'):
                fmt.setBackground(QColor(colors['comment_bg']))
                fmt.setForeground(QColor(colors['comment_text']))
                self.append_colored_line(line, fmt, True)
            elif line.startswith('log'):
                fmt.setBackground(QColor(colors['log_bg']))
                fmt.setForeground(QColor(colors['log_text']))
                self.append_colored_line(line, fmt, True)
            else:
                fmt.setBackground(QColor(colors['default_bg']))
                fmt.setForeground(QColor(colors['default_text']))
                self.append_colored_line(line, fmt, True)

    def move_cursor_to_keyword(self, keyword):
        """将光标移动到第一个高亮的关键字位置，并滚动到该位置。"""
        cursor = self.textCursor()
        document_length = self.document().toPlainText().find(keyword)
        # 找到关键字在文档中的位置
        if document_length != -1:
            # 移动光标到关键字开始的位置
            cursor.setPosition(document_length)
            # 设置文本光标位置，以便自动滚动到关键字
            self.setTextCursor(cursor)
            self.ensureCursorVisible()

    def set_colored_text_test_case(self, texts):
        # 获取主题颜色
        colors = self._get_theme_colors()

        for line in texts:
            fmt = QTextCharFormat()
            if line.startswith('RDC路径') or line.startswith('脚本路径'):
                fmt.setForeground(QColor(colors['default_text']))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('*'):
                fmt.setForeground(QColor(colors['section_text']))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith(' ') or line.startswith('\t'):
                fmt.setForeground(QColor(colors['default_text']))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith('Variables') or line.startswith('Resource') or line.startswith('Suite'):
                fmt.setForeground(QColor(colors['keyword_text']))
                self.append_colored_line(line, fmt, True)
                continue
            elif line.startswith('用例脚本'):
                fmt.setForeground(QColor(colors['section_text']))
                self.append_colored_line(line, fmt, True)
                continue
            else:
                fmt.setForeground(QColor(colors['keyword_text']))
                self.append_colored_line(line, fmt, True)

    def set_colored_text_rdc_step(self, texts):
        for line in texts:
            fmt = QTextCharFormat()
            if line.startswith('操作步骤'):
                fmt.setForeground(QColor('blue'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('预期结果'):
                fmt.setForeground(QColor('green'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('用例名称'):
                fmt.setForeground(QColor('purple'))
                self.append_colored_line(line, fmt, True)
                continue
            if line.startswith('预置条件'):
                fmt.setForeground(QColor('brown'))
                self.append_colored_line(line, fmt, True)
                continue
            else:
                fmt.setForeground(QColor('black'))
                self.append_colored_line(line, fmt, True)

    def append_colored_line(self, line, fmt, isChangeLine=False):
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(line, fmt)
        if isChangeLine:
            cursor.insertBlock()



