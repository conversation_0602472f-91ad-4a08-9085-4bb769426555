# coding=utf-8
'''
Created on 2019年12月25日

@author: 10240349
'''
from PyQt5.Qt import Qt, QBasicTimer, QWidget, pyqtSignal, QThread
from PyQt5.QtWidgets import <PERSON><PERSON>oxLayout, QProgressBar, QGridLayout

from settings.i18n.Loader import LanguageLoader
from utility.PluginRepository import PluginRepository


class LoadingBar(QWidget):

    def __init__(self, file_counter):
        super(LoadingBar, self).__init__()
        self._file_counter = file_counter
        self._file_counter.add.connect(self._update_progress)
        self.timer = QBasicTimer()
        self.step = 0
        self.initUI()

    def initUI(self):
        self.setWindowModality(Qt.ApplicationModal)
        self.setWindowTitle(LanguageLoader().get('LOADING'))
        self.setWindowFlags(Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.resize(250, 100)
        self._set_progressbar()
        layout = QVBoxLayout(self)
        self.setLayout(layout)

    def _set_progressbar(self):
        self._progress_bar = QProgressBar(self)
        self._progress_bar.setAlignment(Qt.AlignCenter)
        layout = QGridLayout(self)
        layout.addWidget(self._progress_bar, 0, 0, 3, 0)

    def _update_progress(self):
        if self._file_counter.get_analyzed_percentage() >= 100:
            return
        self._progress_bar.setValue(self._file_counter.get_analyzed_percentage())

    def run(self):
        self.show()

    def destroy(self):
        self.timer.stop()
        if self._progress_bar:
            self._progress_bar.close()
            self.close()


class KeywordsLoadingBar(LoadingBar):

    def __init__(self, file_counter):
        super(KeywordsLoadingBar, self).__init__(file_counter)
        self._init_ui()

    def _init_ui(self):
        self._file_counter.add.connect(self._update)
        self._label = PluginRepository().find('KEYWORD_PROGRESS')
        self._status_bar = PluginRepository().find('STATUS_BAR')
        self._status_bar.addPermanentWidget(self._label)

    def _update(self):
        if self._file_counter.get_analyzed_percentage() >= 100:
            self._label.setText('关键字搜索进度搜索完毕！')
            return
        self._label.setText('关键字搜索进度: '+str(self._file_counter.get_analyzed_percentage())+'%')


class TestcasesLoadingBar(LoadingBar):

    def __init__(self, file_counter):
        super(TestcasesLoadingBar, self).__init__(file_counter)
        self._init_ui()

    def _init_ui(self):
        self._file_counter.add.connect(self._update)
        self._label = PluginRepository().find('KEYWORD_PROGRESS')
        self._status_bar = PluginRepository().find('STATUS_BAR')
        self._status_bar.addPermanentWidget(self._label)
 
    def _update(self):
        if self._file_counter.get_analyzed_percentage() >= 100:
            self._label.setText('用例搜索进度搜索完毕！')
            return
        self._label.setText('用例搜索进度: '+str(self._file_counter.get_analyzed_percentage())+'%')


class WorkTread(QThread):

    add = pyqtSignal()

    def __init__(self, _dict, combox_text, search_line_text):
        super().__init__()

    def run(self):
        self.add.emit(self._parse_dict())


def except_hook(cls, exception, traceback):
    sys.__excepthook__(cls, exception, traceback)


if __name__ == '__main__':
    import sys
    import cgitb
    cgitb.enable(format='text')
    sys.excepthook = except_hook
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    w = LoadingBar()
    w.run()
    sys.exit(app.exec_())
