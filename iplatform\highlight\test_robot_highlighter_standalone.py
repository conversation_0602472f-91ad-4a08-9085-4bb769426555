# -*- coding: utf-8 -*-
"""
独立测试RobotHighlighter的功能（不依赖UserSettings）
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtCore import Qt
from PyQt5.Qsci import QsciScintilla, QsciLexerCustom
import re


class RobotHighlighterStandalone(QsciLexerCustom):
    # 定义样式ID
    Default = 0
    Section = 1
    Keyword = 2
    Variable = 3
    Comment = 4
    TestCase = 5
    Setting = 6
    Number = 7
    String = 8
    BuiltinKeyword = 9
    Tag = 10
    
    def __init__(self, parent=None):
        super(RobotHighlighterStandalone, self).__init__(parent)
        
        # 设置默认字体
        font = QFont()
        font.setStyleName('Normal')
        font.setPointSize(10)  # 默认字体大小
        font.setFamily("Consolas")  # 默认字体
        self.setDefaultFont(font)
        
        # 设置各种样式的颜色
        self.setColor(QColor("#000000"), self.Default)  # 黑色 - 默认文本
        self.setColor(QColor("#0000FF"), self.Section)  # 蓝色 - 部分标题
        self.setColor(QColor("#800080"), self.Keyword)  # 紫色 - 关键字
        self.setColor(QColor("#FF8C00"), self.Variable)  # 深橙色 - 变量
        self.setColor(QColor("#008000"), self.Comment)  # 绿色 - 注释
        self.setColor(QColor("#8B0000"), self.TestCase)  # 深红色 - 测试用例名
        self.setColor(QColor("#4B0082"), self.Setting)  # 靛蓝色 - 设置项
        self.setColor(QColor("#FF00FF"), self.Number)  # 洋红色 - 数字
        self.setColor(QColor("#DC143C"), self.String)  # 深红色 - 字符串
        self.setColor(QColor("#0000CD"), self.BuiltinKeyword)  # 中蓝色 - 内置关键字
        self.setColor(QColor("#006400"), self.Tag)  # 深绿色 - 标签
        
        # 设置字体样式
        self.setFont(font, self.Default)
        bold_font = QFont(font)
        bold_font.setBold(True)
        self.setFont(bold_font, self.Section)
        self.setFont(bold_font, self.TestCase)
        self.setFont(bold_font, self.Keyword)
        
        # 定义内置关键字
        self.builtin_keywords = [
            'log', 'sleep', 'should be true', 'should be false', 
            'should be equal', 'should not be equal', 'should contain',
            'should not contain', 'run keyword', 'run keyword if',
            'run keyword and return status', 'set variable', 'get variable value',
            'create list', 'create dictionary', 'append to list',
            'get from dictionary', 'set to dictionary', 'comment',
            'pass execution', 'fail', 'fatal error', 'import library',
            'import resource', 'import variables', 'set test variable',
            'set suite variable', 'set global variable', 'get time',
            'evaluate', 'call method', 'convert to integer', 'convert to string',
            'should be empty', 'should not be empty', 'length should be',
            'should match', 'should not match', 'should match regexp',
            'should not match regexp', 'get length', 'count values in list',
            'get from list', 'get index from list', 'copy list',
            'reverse list', 'sort list', 'combine lists', 'remove from list',
            'remove duplicates', 'get dictionary keys', 'get dictionary values',
            'dictionary should contain key', 'dictionary should not contain key',
            'lists should be equal', 'dictionaries should be equal'
        ]
        
        # 编译正则表达式以提高性能
        self.section_pattern = re.compile(r'^\*{3}\s*(Settings?|Variables?|Test Cases?|Keywords?|Tasks?)\s*\*{3}', re.IGNORECASE)
        self.variable_pattern = re.compile(r'\$\{[^}]+\}|@\{[^}]+\}|&\{[^}]+\}|%\{[^}]+\}')
        self.setting_pattern = re.compile(r'^\s*\[(Tags|Documentation|Setup|Teardown|Template|Timeout|Arguments|Return)\]', re.IGNORECASE)
        self.number_pattern = re.compile(r'\b\d+\.?\d*\b')
        self.string_pattern = re.compile(r'"[^"]*"|\'[^\']*\'')
        
    def language(self):
        return "RobotFramework"
    
    def description(self, style):
        descriptions = {
            self.Default: "Default",
            self.Section: "Section",
            self.Keyword: "Keyword",
            self.Variable: "Variable",
            self.Comment: "Comment",
            self.TestCase: "TestCase",
            self.Setting: "Setting",
            self.Number: "Number",
            self.String: "String",
            self.BuiltinKeyword: "BuiltinKeyword",
            self.Tag: "Tag"
        }
        return descriptions.get(style, "")
    
    def styleText(self, start, end):
        # 初始化样式
        self.startStyling(start)
        
        # 获取要处理的文本
        text = self.parent().text()[start:end]
        
        # 按行处理
        lines = text.split('\n')
        current_pos = start
        in_test_case_section = False
        in_keyword_section = False
        
        for line in lines:
            line_start = current_pos
            line_length = len(line)
            
            # 默认样式
            self.setStyling(line_length, self.Default)
            
            # 检查是否是部分标题
            if self.section_pattern.match(line):
                self.startStyling(line_start)
                self.setStyling(line_length, self.Section)
                # 更新当前部分状态
                if 'test case' in line.lower():
                    in_test_case_section = True
                    in_keyword_section = False
                elif 'keyword' in line.lower():
                    in_keyword_section = True
                    in_test_case_section = False
                else:
                    in_test_case_section = False
                    in_keyword_section = False
            else:
                # 处理注释
                if line.strip().startswith('#') or '\tComment\t' in line or line.strip().startswith('Comment'):
                    self.startStyling(line_start)
                    self.setStyling(line_length, self.Comment)
                # 处理测试用例名或关键字名（行首非空白字符）
                elif line and not line[0].isspace() and (in_test_case_section or in_keyword_section):
                    self.startStyling(line_start)
                    if in_test_case_section:
                        self.setStyling(line_length, self.TestCase)
                    else:
                        self.setStyling(line_length, self.Keyword)
                else:
                    # 处理行内的各种元素
                    self._style_line_content(line, line_start)
            
            # 移动到下一行（包括换行符）
            current_pos += line_length + 1
    
    def _style_line_content(self, line, line_start):
        """处理行内的各种语法元素"""
        # 首先设置整行为默认样式
        self.startStyling(line_start)
        self.setStyling(len(line), self.Default)
        
        # 处理设置项
        setting_match = self.setting_pattern.match(line)
        if setting_match:
            self.startStyling(line_start + setting_match.start())
            self.setStyling(setting_match.end() - setting_match.start(), self.Setting)
            
            # 特别处理[Tags]
            if 'Tags' in setting_match.group():
                # 查找标签内容
                tag_content = line[setting_match.end():].strip()
                if tag_content:
                    self.startStyling(line_start + setting_match.end())
                    self.setStyling(len(line) - setting_match.end(), self.Tag)
        
        # 处理变量
        for match in self.variable_pattern.finditer(line):
            self.startStyling(line_start + match.start())
            self.setStyling(match.end() - match.start(), self.Variable)
        
        # 处理字符串
        for match in self.string_pattern.finditer(line):
            self.startStyling(line_start + match.start())
            self.setStyling(match.end() - match.start(), self.String)
        
        # 处理数字
        for match in self.number_pattern.finditer(line):
            # 确保数字不在变量或字符串内
            if not self._is_in_variable_or_string(line, match.start(), match.end()):
                self.startStyling(line_start + match.start())
                self.setStyling(match.end() - match.start(), self.Number)
        
        # 处理内置关键字
        line_lower = line.lower()
        for keyword in self.builtin_keywords:
            keyword_lower = keyword.lower()
            # 使用制表符或多个空格作为分隔符
            patterns = [
                f'\t{keyword_lower}\t',
                f'\t{keyword_lower}$',
                f'  {keyword_lower}  ',
                f'  {keyword_lower}$'
            ]
            for pattern in patterns:
                index = 0
                while True:
                    index = line_lower.find(pattern, index)
                    if index == -1:
                        break
                    # 找到关键字的实际起始位置
                    keyword_start = index + len(pattern) - len(keyword_lower) - (1 if pattern.endswith('$') else len(pattern) - len(keyword_lower) - len(keyword_lower))
                    if pattern.startswith('\t'):
                        keyword_start = index + 1
                    elif pattern.startswith('  '):
                        keyword_start = index + 2
                    
                    self.startStyling(line_start + keyword_start)
                    self.setStyling(len(keyword), self.BuiltinKeyword)
                    index += len(pattern)
    
    def _is_in_variable_or_string(self, line, start, end):
        """检查给定位置是否在变量或字符串内"""
        # 检查是否在变量内
        for match in self.variable_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True
        
        # 检查是否在字符串内
        for match in self.string_pattern.finditer(line):
            if match.start() <= start < match.end() or match.start() < end <= match.end():
                return True
        
        return False


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RobotFramework语法高亮测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建编辑器
        self.editor = QsciScintilla()
        self.setCentralWidget(self.editor)
        
        # 设置语法高亮器
        self.highlighter = RobotHighlighterStandalone(self.editor)
        self.editor.setLexer(self.highlighter)
        
        # 设置编辑器属性
        self.editor.setUtf8(True)  # 支持UTF-8编码
        self.editor.setIndentationsUseTabs(False)
        self.editor.setIndentationWidth(4)
        self.editor.setAutoIndent(True)
        
        # 设置测试内容
        test_content = """*** Settings ***
Documentation    这是一个测试套件的说明文档
Library          SeleniumLibrary
Resource         ../resources/common.robot
Variables        ../variables/config.py

*** Variables ***
${用户名}        测试用户
${密码}          123456
@{列表变量}      项目1    项目2    项目3
&{字典变量}      key1=值1    key2=值2

*** Test Cases ***
测试用例_登录系统
    [Documentation]    测试登录功能
    [Tags]    冒烟测试    登录    中文标签
    打开浏览器    https://example.com    chrome
    输入用户名    ${用户名}
    输入密码      ${密码}
    点击登录按钮
    验证登录成功
    [Teardown]    关闭浏览器

测试用例_数据验证
    [Tags]    数据测试
    ${结果}    执行查询    SELECT * FROM users WHERE id = 100
    log    查询结果：${结果}
    should be equal    ${结果}[0]    预期值
    ${数字}    set variable    12345
    ${文本}    set variable    "这是一个字符串"
    Comment    这是一个注释行
    
*** Keywords ***
打开浏览器
    [Arguments]    ${网址}    ${浏览器类型}=chrome
    [Documentation]    打开指定的浏览器并访问网址
    Open Browser    ${网址}    ${浏览器类型}
    Maximize Browser Window
    
输入用户名
    [Arguments]    ${用户名}
    Input Text    id=username    ${用户名}
    
输入密码
    [Arguments]    ${密码}
    Input Password    id=password    ${密码}
    
点击登录按钮
    Click Button    xpath=//button[@type='submit']
    
验证登录成功
    Wait Until Page Contains    欢迎您
    Page Should Contain    ${用户名}
    
关闭浏览器
    Close All Browsers
    
执行查询
    [Arguments]    ${查询语句}
    [Return]    ${查询结果}
    ${查询结果}    Query    ${查询语句}
"""
        
        self.editor.setText(test_content)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())
