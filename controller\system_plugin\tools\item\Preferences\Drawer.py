# coding=utf8
'''
Created on 2020年1月6日

@author: 10247557/10240349
'''
from PyQt5.Qt import Qt

from controller.system_plugin.tools.item.Preferences.view.Language import Language
from controller.system_plugin.tools.item.Preferences.view.TraceLog import TraceLog
from controller.system_plugin.tools.item.Preferences.view.Theme import Theme
from utility.Singleton import Singleton


@Singleton
class Drawer():

    def __init__(self):
        self._layout = None
        self._dialog = None  # 初始化对话框引用

    def get_layout(self, item):
        self._layout = eval('self._get_{}_layout()'.format(item.lower()))
        return self._layout

    def _get_language_layout(self):
        language = Language()
        language.load()
        layout = language.get_layout()
        return layout

    def _get_trace_log_layout(self):
        tracelog = TraceLog()
        layout = tracelog.get_layout()
        tracelog.set_item()
        return layout

    def _get_theme_layout(self):
        theme = Theme(dialog=self._dialog)  # 传递对话框引用
        theme.load()
        layout = theme.get_layout()
        return layout
