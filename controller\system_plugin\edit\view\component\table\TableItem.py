'''
Created on 2019年11月8日

@author: 10240349
'''
from _functools import partial

from PyQt5.Qt import Qt, QApplication, QCursor
from PyQt5.QtWidgets import QStyledItemDelegate, \
    QCompleter, QLineEdit, QListView

from controller.system_plugin.edit.view.component.table.KeywordsListModel import KeywordsListModel
from settings.SystemSettings import SystemSettings
from controller.system_plugin.edit.view.component.table.KeywordDescription import KeywordDescription
from controller.system_plugin.SignalDistributor import SignalDistributor


class TableItem(QStyledItemDelegate):

    def __init__(self, parent=None):
        super(TableItem, self).__init__(parent)
        self._keyword_description = KeywordDescription()

    def createEditor(self, parent, option, index):
        item_data = index.data()
        self.editor = TableLineEdit(parent, self)
        self.editor.setText(item_data)
        completer = QCompleter()
        table_list_view = TableListView(self)
        table_list_view.setMinimumSize(SystemSettings().get_value('KEYWORDS_WIDTH'),
                                       SystemSettings().get_value('KEYWORDS_HEIGHT'))
        completer.setPopup(table_list_view)
#         completer.setMaxVisibleItems(12)
        completer.setFilterMode(Qt.MatchContains)  # 包含做匹配
        completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.editor.setCompleter(completer)  # 提示自动补全
        self._model = KeywordsListModel()
        self._model.set_keywords()
        completer.setModel(self._model)
        completer.highlighted.connect(partial(self._open_dialog_by_completer, completer))
        completer.activated.connect(self._keyword_description.close_dialog)
        return self.editor

    def _open_dialog_by_completer(self, completer):
        row = completer.popup().currentIndex().row()
        index = self._model.index(row)
        current_item_name = completer.completionModel().data(index, Qt.DisplayRole)
        self._keyword_description.open_dialog(current_item_name,
                                              completer.popup().pos().x() + completer.popup().width(),
                                              completer.popup().pos().y())


class TableLineEdit(QLineEdit):

    def __init__(self, parent, delegate):
        self._delegate = delegate
        super(TableLineEdit, self).__init__(parent)
        self.textChanged.connect(self._text_changed)

    @staticmethod
    def _text_changed(text):
        SignalDistributor().modify_table_item_event(text)

    def mousePressEvent(self, event):
        self._delegate._keyword_description.close_dialog()
        return super().mousePressEvent(event)

    def focusOutEvent(self, event):
        x = QCursor.pos().x()
        y = QCursor.pos().y()
        action = QApplication.widgetAt(x, y)
        if action and action.parent() and hasattr(self._delegate, "_text_des") and self._delegate._text_des:
            if action.parent().parent() != self._delegate._text_des and action.parent() != self._delegate._text_des:
                self._delegate._keyword_description.close_dialog()
        return super().focusOutEvent(event)


class TableListView(QListView):

    def __init__(self, delegate):
        self._delegate = delegate
        super(TableListView, self).__init__()

    def currentChanged(self, current, previous):
        if not current.isValid():
            self._delegate._keyword_description.close_dialog()
        return super().currentChanged(current, previous)
