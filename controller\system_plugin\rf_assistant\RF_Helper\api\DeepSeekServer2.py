from huggingface_hub import snapshot_download
import os
from transformers import AutoModelForCausalLM, AutoTokenizer
from typing import Dict
import logging
from functools import wraps
import time

def retry(max_retries=3, delay=1, exceptions=(Exception,)):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_retries:
                        raise e
                    print(f"Error: {e}, {delay}秒后第{attempt+1}次重试...")
                    time.sleep(delay)
        return wrapper
    return decorator

def setup_proxy() -> None:
    """Configure proxy settings for HTTP and HTTPS"""
    proxy_url = 'http://proxyhk.zte.com.cn:80'
    os.environ.update({
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url
    })
    # 设置镜像源示例（南京大学镜像）
    os.environ['HF_ENDPOINT'] = 'https://mirror.nju.edu.cn/huggingface'

def load_model(model_name: str = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B'):
    """
    Load the language model with error handling
    
    Args:
        model_name: Name/path of the model to load
    Returns:
        Loaded model instance
    """
    try:
        setup_proxy()
        snapshot_download(repo_id="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B")
    except Exception as e:
        logging.error(f"Failed to load model {model_name}: {str(e)}")
        raise


@retry(max_retries=13, delay=2, exceptions=(ConnectionError,))
def main():
    """Main function to demonstrate the usage of the model loading and text generation functions."""    
    # Load the model
    print('--------Loading model--------')
    load_model()
    print('--------Model loaded--------')
    # Generate text using the model

if __name__ == "__main__":
    print('--------Starting main function--------')
    while True:
        try:
            main()
            break
        except Exception as e:
            print(f"An error occurred: {str(e)}")
            time.sleep(5)
            print("Retrying...")