'''
Created on 2019年10月31日

@author: 10247557,10225935,10259183
'''
from PyQt5.QtWidgets import QTextEdit
from PyQt5.QtCore import pyqtSignal, QObject
from PyQt5.QtGui import QTextCursor
from controller.system_plugin.style.ThemeManager import ThemeManager


class LogTraceTextEditor(QObject):
    theme_changed = pyqtSignal()

    def __init__(self):
        '''
        Constructor
        '''
        super().__init__()
        self.theme_manager = ThemeManager()
        self.theme_manager.theme_changed.connect(self._update_background)
        self._update_background()

    def _update_background(self):
        theme_id = self.theme_manager.get_current_theme()
        if theme_id == 'cool_blue':
            bg_color = "#E0EFFF"
        else:
            bg_color = "#FFFFFF" if not self.theme_manager.is_dark_theme() else "#2D2D2D"
        if hasattr(self, '_text_editor'):
            self._text_editor.setStyleSheet(f"background-color: {bg_color};")
        else:
            self._text_editor = QTextEdit()
            self._text_editor.setStyleSheet(f"background-color: {bg_color};")

    def get_editor(self):
        return self._text_editor

    def append(self, text):
        """Append text to the QTextEdit."""
        cursor = self._text_editor.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(text)
        self._text_editor.setTextCursor(cursor)
        self._text_editor.ensureCursorVisible()
