# coding=utf-8
'''
Created on 2019年11月5日

@author: 10240349
'''
import copy
import os
import re

from settings.HistoryProject import HistoryProject
from settings.i18n.Loader import LanguageLoader
from utility.ExecutiveTestCaseRepository import ExecutiveTestCaseRepository
from view.common.MessageBox import MessageBox


class CmdGenerator(object):

    def __init__(self, arguments, run_tag, skip_tag):
        self._executive_cmd = {}
        self._arguments = arguments
        self._run_tag = run_tag
        self._skip_tag = skip_tag
        self._testcases = copy.deepcopy(ExecutiveTestCaseRepository().find('EXECUTIVE_TESTCASES'))
        self._workdir = HistoryProject().read('PROJECT_PATH')
        self._format_workdir()

    def _format_workdir(self):
        self._workdir_dot = re.sub(r'/', '.', self._workdir)

    def get(self):
        self._set_testcase()
        self._set_cmd()
        return self._executive_cmd

    def _set_testcase(self):
        if not self._testcases:
            self._testcases = ''
        else:
            self._format_testsuite()
            self._assemble_testcases()

    def _format_testsuite(self):
        for testcase_list in self._testcases:
            testsuite = testcase_list[0]
            testsuite_dot = re.sub(r'\\|/', '.', testsuite)
            testcase_list[0] = testsuite_dot

    def _assemble_testcases(self):
        self._project_name = self._workdir_dot.split('.')[-1]
        for testcase_list in self._testcases:
            testsuite = testcase_list[0]
            testsuite_dot = re.sub(self._workdir_dot, '', testsuite)
            testsuite_dot = testsuite_dot.lstrip('.')
            testcase_list[0] = self._project_name + '.' + os.path.splitext(testsuite_dot)[0]
            testcase = testcase_list[0] + '.' + testcase_list[1]
            testcase_list[1] = testcase

    def _set_cmd(self):
        if not self._testcases:
            MessageBox().show_critical(LanguageLoader().get('TESTCASE_INFO'))
        else:
            self._executive_cmd = {'testcases': self._testcases, 'workdir': self._workdir}
            if self._arguments:
                self._executive_cmd['arguments'] = self._arguments
            if self._run_tag:
                self._executive_cmd['run_tag'] = self._run_tag
            if self._skip_tag:
                self._executive_cmd['skip_tag'] = self._skip_tag

    def assemble_executive_testcase(self):
        testcases_item = ExecutiveTestCaseRepository().find('EXECUTIVE_TESTCASES_ITEM')
        executive_item = {}
        for _dict in testcases_item:
            for k, v in _dict.items():
                key = self._parse_testcase_path(k)
                executive_item[key] = v
        ExecutiveTestCaseRepository().add('EXECUTIVE_ITEM', executive_item)

    def _parse_testcase_path(self, k):
        path_dot = re.sub(r'\\|/', '.', k)
        _path_dot = re.sub(self._workdir_dot, '', path_dot)
        _list = _path_dot.split('.')[1:]
        name = self._project_name
        for i in _list:
            name += '.' + i
        return name

if __name__ == "__main__":
    arguments = ''
    run_tag = ''
    skip_tag = ''
    c = CmdGenerator(arguments, run_tag, skip_tag)
    c.assemble_executive_testcase()
