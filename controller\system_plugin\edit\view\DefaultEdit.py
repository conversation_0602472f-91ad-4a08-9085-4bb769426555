# coding=utf-8
'''
Created on 2019年11月11日

@author: 10247557
'''

from PyQt5.Qt import Qt, <PERSON><PERSON><PERSON><PERSON>, QFont, QCursor
from PyQt5.QtWidgets import QPushButton, QHBoxLayout, QLineEdit

from controller.system_plugin.edit.view.component.Documentation import Documentation
from controller.system_plugin.edit.view.component.LineEditArea import LineEditArea
from controller.system_plugin.edit.view.component.table.Table import Table
from model.CurrentItem import CurrentItem
from settings.i18n.Loader import LanguageLoader


class DefaultEdit(QWidget):

    def __init__(self, parent):
        super().__init__()
        self._parent_window = parent
        self._is_visible = True

    def load(self):
        self._path = self._set_name_area()
        self._settings = self._set_settings_button()
        self._documentation = self._set_documentation_area()
        self._teardown = self._set_teardown_area()
        self._timeout = self._set_timeout_area()
        self._table = self._set_table_area()

    def _set_name_area(self):
        self._name_line = QLineEdit()
        self._name_line.setFont(QFont("Roman times", 10, QFont.Bold))
        self._name_line.setStyleSheet("border-width:0;border-style:outset")
        self._name_line.setReadOnly(True)
        layout = QHBoxLayout()
        layout.addWidget(self._name_line)
        return layout

    def _set_settings_button(self):
        layout = QHBoxLayout()
        self._settings_btn = QPushButton(LanguageLoader().get('SETTINGS'), self._parent_window)
        self._settings_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self._settings_btn.setFixedSize(120, 25)
        self._settings_btn.clicked.connect(self._set_visible)
        layout.addWidget(self._settings_btn, 0, Qt.AlignLeft)
        return layout

    def _set_documentation_area(self):
        documentation = Documentation(self._parent_window)
        documentation.load()
        return documentation

    def _set_teardown_area(self):
        teardown = LineEditArea(self._parent_window, 'Teardown')
        teardown.load()
        return teardown

    def _set_timeout_area(self):
        timeout = LineEditArea(self._parent_window, 'Timeout')
        timeout.load()
        return timeout

    def _set_table_area(self):
        
        table = Table(self._parent_window)
        table.load()
        return table

    def _set_visible(self):
        if self._is_visible:
            self._is_visible = False
            self._set_visible_area()
        else:
            self._is_visible = True
            self._set_visible_area()

    def _set_visible_area(self):
        self._documentation.set_visible(self._is_visible)
        self._teardown.set_visible(self._is_visible)
        self._timeout.set_visible(self._is_visible)

    def fill_data(self, parsed_item):
        self._name_line.setText(CurrentItem().get().get('name'))
        self._name_line.setCursorPosition(0)
        self._documentation.fill_data(parsed_item.documentation)
        self._teardown.fill_data(parsed_item.teardown)
        self._timeout.fill_data(parsed_item.timeout)
        self._table.fill_data(parsed_item)

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接DefaultEdit主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._current_theme = theme_id
        self._apply_theme(theme_id)

    def _apply_current_theme(self):
        """应用当前主题"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            self._current_theme = current_theme
            self._apply_theme(current_theme)
        except Exception as e:
            print(f"应用DefaultEdit当前主题失败: {e}")

    def _apply_theme(self, theme_id):
        """应用主题样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            colors = theme_manager.get_theme_colors(theme_id)

            # 应用主题到名称行
            self._apply_name_line_theme(colors, theme_id)

            # 应用主题到设置按钮
            self._apply_settings_button_theme(colors, theme_id)

            # 应用主题到整个组件
            self._apply_widget_theme(colors, theme_id)

            print(f"DefaultEdit已应用主题: {theme_id}")

        except Exception as e:
            print(f"应用DefaultEdit主题失败: {e}")

    def _apply_name_line_theme(self, colors, theme_id):
        """应用主题到名称行"""
        try:
            if theme_id == 'eye_protect_green':
                # 墨绿色主题
                name_line_style = f"""
                    QLineEdit {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        border: 1px solid {colors['border_color']};
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }}
                """
            elif theme_id == 'dark':
                # 深色主题
                name_line_style = f"""
                    QLineEdit {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        border: 1px solid {colors['border_color']};
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }}
                """
            elif theme_id == 'cool_blue':
                # 淡蓝色主题
                name_line_style = f"""
                    QLineEdit {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        border: 1px solid {colors['border_color']};
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }}
                """
            else:
                # 浅色主题
                name_line_style = f"""
                    QLineEdit {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        border: 1px solid {colors['border_color']};
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }}
                """

            self._name_line.setStyleSheet(name_line_style)

        except Exception as e:
            print(f"应用名称行主题失败: {e}")

    def _apply_settings_button_theme(self, colors, theme_id):
        """应用主题到设置按钮"""
        try:
            if theme_id == 'eye_protect_green':
                # 墨绿色主题 - 使用绿色系按钮
                button_style = """
                    QPushButton {
                        background-color: #2E7D32;
                        color: #C8E6C9;
                        border: 1px solid #336633;
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #388E3C;
                        border-color: #4CAF50;
                    }
                    QPushButton:pressed {
                        background-color: #1B5E20;
                        border-color: #1B5E20;
                    }
                """
            elif theme_id == 'dark':
                # 深色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#404040')};
                        color: {colors.get('dialog_button_text', '#E0E0E0')};
                        border: 1px solid {colors.get('border_color', '#555555')};
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#4A90E2')};
                        color: #FFFFFF;
                    }}
                    QPushButton:pressed {{
                        background-color: #357ABD;
                        color: #FFFFFF;
                    }}
                """
            elif theme_id == 'cool_blue':
                # 淡蓝色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#C4DAFF')};
                        color: {colors.get('dialog_button_text', '#333333')};
                        border: 1px solid {colors.get('border_color', '#A0C0FF')};
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#4A90E2')};
                        color: #FFFFFF;
                    }}
                    QPushButton:pressed {{
                        background-color: #357ABD;
                        color: #FFFFFF;
                    }}
                """
            else:
                # 浅色主题
                button_style = f"""
                    QPushButton {{
                        background-color: {colors.get('dialog_button_bg', '#F0F0F0')};
                        color: {colors.get('dialog_button_text', '#000000')};
                        border: 1px solid {colors.get('border_color', '#D0D0D0')};
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {colors.get('dialog_button_hover_bg', '#E0E0E0')};
                    }}
                    QPushButton:pressed {{
                        background-color: #D0D0D0;
                    }}
                """

            self._settings_btn.setStyleSheet(button_style)

        except Exception as e:
            print(f"应用设置按钮主题失败: {e}")

    def _apply_widget_theme(self, colors, theme_id):
        """应用主题到整个组件"""
        try:
            if theme_id == 'eye_protect_green':
                # 墨绿色主题
                widget_style = f"""
                    QWidget {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                    }}
                """
            elif theme_id == 'dark':
                # 深色主题
                widget_style = f"""
                    QWidget {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                    }}
                """
            elif theme_id == 'cool_blue':
                # 淡蓝色主题
                widget_style = f"""
                    QWidget {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                    }}
                """
            else:
                # 浅色主题
                widget_style = f"""
                    QWidget {{
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                    }}
                """

            self.setStyleSheet(widget_style)

        except Exception as e:
            print(f"应用组件主题失败: {e}")
