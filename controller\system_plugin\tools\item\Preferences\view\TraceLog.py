# coding=utf-8
'''
Created on 2020年1月9日

@author: 10240349
'''
from _functools import partial

from PyQt5.Qt import Q<PERSON>abel, QWidget
from PyQt5.QtWidgets import QHBoxLayout, QVBoxLayout, QComboBox

from settings.SystemSettings import SystemSettings
from settings.i18n.Loader import LanguageLoader


DEFAULT_TRACE_LOG_COUNT = 5
TRACE_LOG_COUNT_LINE_WIDTH = 75
MIN_VAL = 1
MAX_VAL = 99
PLACE_HOLDERF = '1-99'

LEVEL = {'Default': 5, 'All': "inf", 'More': 50, 'Less': 1}
COMBOX_LIB = ['Default', 'All', 'More', 'Less']


class TraceLog(QWidget):

    def __init__(self):
        super(TraceLog, self).__init__()
        self._init_ui()

    def _init_ui(self):
        self._layout = QVBoxLayout()
        box = self._set_count_layout()
        self._layout.addLayout(box)

    def set_item(self):
        count = SystemSettings().read('TRACE_LOG_COUNT')
        if not count or str(count) == str(5):
            self._combox.setCurrentIndex(0)  # Default
        elif str(count) == str(50):
            self._combox.setCurrentIndex(2)  # More
        elif str(count) == str(1):
            self._combox.setCurrentIndex(3)  # Less
        else:
            self._combox.setCurrentIndex(1)  # All

    def get_layout(self):
        return self._layout

    def _set_count_layout(self):
        hbox = QHBoxLayout()
        self._set_label()
        self._set_combox_widget()
        hbox.addWidget(self._label)
        hbox.addWidget(self._combox)
        hbox.addStretch()
        return hbox

    def _set_label(self):
        self._label = QLabel(LanguageLoader().get('TRACE_LOG'))

    def _set_combox_widget(self):
        self._combox = QComboBox(self)
        self._combox.addItems(COMBOX_LIB)
        self._combox.currentIndexChanged.connect(partial(self._set_trace_log_count, self))

    @staticmethod
    def _set_trace_log_count(this):
        key = this._combox.currentText()
        SystemSettings().write('TRACE_LOG_COUNT', LEVEL[key])
