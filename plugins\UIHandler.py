# coding=utf-8
'''
Created on 2019年10月29日

@author: 10240349
'''
import json

from PyQt5.Qt import QSettings

from settings.SystemSettings import SYSTEM_ROOT_DIR
from utility.UIRepository import UIRepository


class UIHandler(object):

    def handle(self, key, obj, doc):
        methods = self._get_methods(obj)
        doc += self._filter_methods(key, methods)
        assemble_obj = self._assemble(key, obj, doc)
        if UIRepository().add(key, assemble_obj):
            self._write(key, doc)

    def _get_methods(self, obj):
        return dir(obj)

    def _filter_methods(self, key, methods):
        methods_str = ':' 
        for method in methods:
            if not method.startswith('__') and (method != 'x' and method != 'y'):
                methods_str = methods_str + method + ','
        return methods_str[:-1]

    def _assemble(self, key, obj, doc):
        obj = {key: obj, 'doc': doc}
        return obj

    def _write(self, key, value):
        settings = QSettings('{}/plugins/ui_api.ini'.format(SYSTEM_ROOT_DIR), QSettings.IniFormat)
        return settings.setValue(key, value)
