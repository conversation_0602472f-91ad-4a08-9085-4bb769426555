
class TestExecutionResults(object):
    RUNNING = 'Running'
    PASSED = 'Passed'
    FAILED = 'Failed'

    def __init__(self):
        self.clear()

    def test_execution_started(self):
        self.clear()

    def clear(self):
        self.results = {}

    def set_running(self, test):
        self.results[test] = self.RUNNING

    def set_passed(self, test):
        self.results[test] = self.PASSED

    def set_failed(self, test):
        self.results[test] = self.FAILED

    def is_running(self, test):
        return test in self.results and self.results[test] == self.RUNNING

    def has_passed(self, test):
        return test in self.results and self.results[test] == self.PASSED

    def has_failed(self, test):
        return test in self.results and self.results[test] == self.FAILED
